<script setup>
import { ref, defineProps, watch, onMounted } from 'vue';
import router from '../../../router';
import { useRoute } from 'vue-router';
import { Googleanalytics, setActiveElem, cdn } from '../../../helpers/helper';
import {creationToolStore} from '../../../store/index';
import LandMarkCard from '../../ALEComponents/LandMarkCard/LandMarkCard.vue';
import NearByFloatingButton from '../../ALEComponents/NearByFloatingButton/NearByFloatingButton.vue';
// import PopUpMessageBox from '../../ALEComponents/PopUpMessageBox/PopUpMessageBox.vue';
import {addSVGDeepZoom} from '../../../helpers/helper';
import OpenSeadragon from 'openseadragon';
import { LandmarkCategories } from '../../../config/masterdata';
const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
}, routeData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
}});
const show_modal = ref(false), position=ref(false), selectedData=ref({route: false, landmark: false}), landmarkSVGElem = ref({}), routeSVGElem = ref({});
const Store = creationToolStore();
// const checkAutoExit = ref(false);
const route = useRoute();
const currentCategory = ref(route.query.category);
const startTime = ref(new Date), timespent = ref(0);
if (route.query.category && route.query.landmarkId){
  props.sceneType==='deep_zoom'?document.querySelector('.openseadragon-canvas canvas').classList.add("opacity-50"):document.getElementById("svg_highres_image")?document.getElementById("svg_highres_image").classList.add("opacity-50"):document.getElementById("svg_lowres_image")?.classList.add("opacity-50");
}
const activeCategory = ref(route.query.category || 'Show All');
function placeModal (id, landmarkId){
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  timespent.value = difference;
  startTime.value=new Date();
  Googleanalytics("landmark_clicked", {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
    landmark_name: Store.landmarkData[landmarkId].name,
    landmark_id: landmarkId,
    landmark_category: Store.landmarkData[landmarkId].category,
    // landmark_timespent: timespent.value,
    // landmark_timestart: startTime.value,
  });

  props.sceneType==='deep_zoom'?document.querySelector('.openseadragon-canvas canvas').classList.add("opacity-50"):document.getElementById("svg_highres_image").classList.add("opacity-50");
  position.value={x: 2, y: 2};
  router.replace({ name: 'projectScene', query: { ...route.query, landmarkId: id, category: Store.landmarkData[landmarkId].category } });
}

function HoverListener () {
  if (props.sceneType!=='deep_zoom'){
    const ParentSVGLayer = document.getElementById('SVGLayer');
    const ChildSVGLayer = document.querySelectorAll('[id^="landmarkData"]');
    Object.values(ChildSVGLayer).forEach((child) => {
      const styleAttr = child.getAttribute("style");
      if (child.tagName === 'g' && !styleAttr) {
        child.addEventListener('mouseover', () => {
          child.classList.add("opacity-100");
          Object.values(ParentSVGLayer.children[0].children).forEach((otherChild) => {
            if (otherChild.tagName === 'g' && otherChild !== child) {
              otherChild.style.opacity = "0.7";
              otherChild.style.transitionDuration = "0.4s";
              // child.style.filter="drop-shadow(2px 4px 6px black)";
            }
          });

        });

        child.addEventListener('mouseleave', () => {
          Object.values(ParentSVGLayer.children[0].children).forEach((otherChild) => {
            if (otherChild.tagName === 'g') {
              otherChild.style.opacity = "1";
              otherChild.style.transitionDuration = "0.6s";
              child.style.filter="none";
            }
          });
          child.classList.remove("opacity-100");
        });
      }
    });
  }
}

if (route.query.landmarkId && !Store.landmarkData[route.query.landmarkId]) {
  const currentQuery = { ...route.query };
  delete currentQuery.landmarkId;
  // delete currentQuery.category;
  // router.push({ name: 'projectScene', query: currentQuery });
  window.location.href = window.location.href.split("?")[0];// Need To Check the Refresh Issue
}

onMounted(() => {
  if (route.query.landmarkId){
    const svgGroup = document.getElementById(route.query.landmarkId);
    if (svgGroup) {
      const rect = svgGroup.getBoundingClientRect();
      position.value = { x: 0, y: rect.y + 30 };
      selectedData.value = {
        route: props.layerData[route.query.landmarkId].layer_data.landmark.route_id,
        landmark: props.layerData[route.query.landmarkId].layer_data.landmark.landmark_id,
      };
      show_modal.value = true;
    }

  }
  if (props.sceneType === 'deep_zoom') {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        landmarkSVGElem.value[item.layer_data.layer_id]={'g': obj.svgElement.children[0], 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel};
        obj.svgElement.children[0].style.cursor= 'pointer';
        if (item.layer_data.landmark && (Store.landmarkData[item.layer_data.landmark.landmark_id]?.category===currentCategory.value || currentCategory.value===undefined)){
          obj.svgElement.children[0].classList.remove('!hidden');
        } else {
          obj.svgElement.children[0].classList.add('!hidden');
        }
        new OpenSeadragon.MouseTracker({
          element: obj.svgElement.children[0],
          clickHandler: function (e) {
            obj.svgElement.children[0].style.opacity = "1";
            obj.svgElement.children[0].setAttribute("active", "true");
            Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
              if (otherChild.children[0].tagName === 'g' && !otherChild.children[0].getAttribute("active")) {
                otherChild.children[0].style.opacity = "0.5";
                otherChild.children[0].style.transitionDuration = "0.4s";
                // otherChild.children[0].style.filter = "drop-shadow(2px 4px 6px black)";
              }
            });
            placeModal(item.layer_data.layer_id, item.layer_data.landmark.landmark_id, e);
          },
        });
        obj.svgElement.children[0].addEventListener('mouseover', () => {
          obj.svgElement.children[0].style.opacity = "1";
          obj.svgElement.children[0].setAttribute("active", "true");

          Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
            if (otherChild.children[0].tagName === 'g' && !otherChild.children[0].getAttribute("active")) {
              otherChild.children[0].style.opacity = "0.5";
              otherChild.children[0].style.transitionDuration = "0.4s";

            }

          });
          obj.svgElement.children[0].style.filter = "none";

        });
        obj.svgElement.children[0].addEventListener('mouseleave', () => {
          Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
            if (otherChild.children[0].tagName === 'g') {
              otherChild.children[0].removeAttribute("active");
              otherChild.children[0].style.opacity = "1";
              otherChild.children[0].style.transitionDuration = "0.6s";
              obj.svgElement.children[0].style.filter="none";
            }
            obj.svgElement.children[0].style.filter = "none";

          });
        });
        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.children[0].classList.add('!visible');
            obj.svgElement.children[0].classList.remove('!hidden');
          } else {
            obj.svgElement.children[0].classList.remove('!visible');
            obj.svgElement.children[0].classList.add('!hidden');
          }
        }
      });
    }
    if (Object.values(props.routeData).length > 0) {
      Object.values(props.routeData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        obj.svgElement.children[0].style.cursor= 'pointer';
        obj.svgElement.children[0].style.strokeDasharray = (window.viewer.viewport._contentSize.x > (1920 * 4)) ? 100000 : 10000;
        obj.svgElement.children[0].style.strokeDashoffset = (window.viewer.viewport._contentSize.x > (1920 * 4)) ? 100000 : 10000;
        // This Does Make the Route Faster for 16K Images
        obj.svgElement.children[0].style.animation = (window.viewer.viewport._contentSize.x > (1920*4)) ? 'dash 40s linear forwards' :'dash 15s linear forwards';
        if (route.query.landmarkId && props.layerData[route.query.landmarkId].layer_data.landmark?.route_id===item.layer_data.layer_id){
          obj.svgElement.children[0].classList.remove('!hidden');
        } else {
          obj.svgElement.children[0].classList.add('!hidden');
        }
        routeSVGElem.value[item.layer_data.layer_id]={"g": obj.svgElement.children[0]};
      });
    }
  }
  HoverListener();
});

watch(() => {
  return route.query;
}, async (newPath) => {
  currentCategory.value = newPath.category;
  if (newPath.landmarkId){
    selectedData.value={
      route: props.layerData[newPath.landmarkId].layer_data.landmark.route_id,
      landmark: props.layerData[newPath.landmarkId].layer_data.landmark.landmark_id,
    };
    if (props.sceneType==='deep_zoom'){
      routeSVGElem.value[selectedData.value.route].g.classList.remove('!hidden');
      routeSVGElem.value[selectedData.value.route].g.classList.add('!visible');
      Object.values(props.layerData).forEach((layer) => {
        landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.add('!opacity-50');
        if (selectedData.value.landmark === layer.layer_data.landmark.landmark_id) {
          landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!opacity-50');
          // landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.add('!opacity-100');

        } else {
          landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.add('!opacity-50');
        }
      });
    }
    setTimeout(() => {
      show_modal.value=true;
    }, Store.isMobile?100:0);

  } else {
    if (props.sceneType==='deep_zoom'){
      Object.values(props.layerData).forEach((layer) => {
        landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!opacity-100');
        if (layer.layer_data.landmark && (Store.landmarkData[layer.layer_data.landmark.landmark_id]?.category===currentCategory.value || currentCategory.value===undefined)){
          landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!opacity-50');
          landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!hidden');
        } else {
          landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.add('!hidden');
          // landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.add('!opacity-100');
        }
      });
    }
    selectedData.value.landmark = false;
    selectedData.value.route = false;
    show_modal.value=false;
  }

});
function closeModal (e){
  // Check if clicking on the overlay (not the card content) or if called without event
  if (!e || (e.target && (e.target.getAttribute("clickaway")==="true" || e.target === e.currentTarget))){
    show_modal.value=false;
    if (props.sceneType==='deep_zoom'){
      routeSVGElem.value[selectedData.value.route].g.classList.add('!hidden');
      routeSVGElem.value[selectedData.value.route].g.classList.remove('!visible');
    }
    selectedData.value.landmark = false;
    selectedData.value.route = false;
    const currentQuery = { ...route.query };
    delete currentQuery.landmarkId;
    if (activeCategory.value!=='Show All'){
      router.replace({ name: 'projectScene', query: {radius: route.query.radius, category: route.query.category}});
    } else {
      delete currentQuery.category;
      router.replace({ name: 'projectScene', query: {radius: route.query.radius}});
    }
    props.sceneType==='deep_zoom'?document.querySelector('.openseadragon-canvas canvas').classList.remove("opacity-50"):document.getElementById("svg_highres_image")?document.getElementById("svg_highres_image").classList.remove("opacity-50"):document.getElementById("svg_lowres_image")?.classList.remove("opacity-50");
  }
}

function closeModalX (){
  show_modal.value=false;
  selectedData.value.landmark = false;
  selectedData.value.route = false;
  const currentQuery = { ...route.query };
  delete currentQuery.landmarkId;
  if (activeCategory.value!=='Show All'){
    router.replace({ name: 'projectScene', query: {radius: route.query.radius, category: route.query.category}});
  } else {
    delete currentQuery.category;
    router.replace({ name: 'projectScene', query: {radius: route.query.radius}});
  }

}

const setActiveButton = (value) => {
  if (value === null || value.value === 'Show All') {
    activeCategory.value = value.value;
    router.push({ name: 'projectScene', query: {radius: route.query.radius}});
  } else {
    activeCategory.value = value.value;
    router.push({ name: 'projectScene', query: { category: value.value, radius: route.query.radius } });
  }
};
// const closeFullscreenModal = ref(false);

// function setFullScreenCookie () {
//   if (!getCookie('fullscreen')) {
//     const expiryTime = new Date(Date.now() + (30 * 6000)); // 30 minutes in milliseconds
//     document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
//     closeFullscreenModal.value = true;
//   }
// }
// const onClickButton = (id) => {
//   if (id === 'fullscreen') {
//     setFullScreenCookie();
//     if (!document.fullscreenElement &&
//       !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
//       if (document.documentElement.requestFullscreen) {
//         document.documentElement.requestFullscreen();
//       } else if (document.documentElement.mozRequestFullScreen) {
//         document.documentElement.mozRequestFullScreen();
//       } else if (document.documentElement.webkitRequestFullscreen) {
//         document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
//       } else if (document.documentElement.msRequestFullscreen) {
//         document.documentElement.msRequestFullscreen();
//       }
//       checkAutoExit.value = false;
//       Store.isFullScreen = !Store.isFullScreen;

//     } else {
//       if (document.exitFullscreen) {
//         document.exitFullscreen();
//       } else if (document.mozCancelFullScreen) {
//         document.mozCancelFullScreen();
//       } else if (document.webkitExitFullscreen) {
//         document.webkitExitFullscreen();
//       } else if (document.msExitFullscreen) {
//         document.msExitFullscreen();
//       }
//       checkAutoExit.value = true;
//       Store.isFullScreen = !Store.isFullScreen;
//     }
//   }
// };
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(landmarkSVGElem.value, Store.currentZoomlevel);
});

// Watch for category changes to update landmark visibility
watch(() => currentCategory.value, () => {
  if (props.sceneType === 'deep_zoom') {
    Object.values(props.layerData).forEach((layer) => {
      if (layer.layer_data.landmark && landmarkSVGElem.value[layer.layer_data.layer_id]) {
        if (Store.landmarkData[layer.layer_data.landmark.landmark_id]?.category === currentCategory.value || currentCategory.value === undefined) {
          landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!hidden');
        } else {
          landmarkSVGElem.value[layer.layer_data.layer_id].g.classList.add('!hidden');
        }
      }
    });
  }
});

let filteredCategory;

if (Store.SceneData){

  let svgData;
  for (const key in Store.SceneData){
    if (Store.SceneData[key].sceneData._id === route.params.sceneId){
      svgData = JSON.parse(JSON.stringify(Store.SceneData[key].svgData));
    }
  }
  const landmarkCategoires=[];
  for (const key in svgData){
    for (const a in svgData[key].layers){
      if (Object.hasOwn(svgData[key].layers[a], 'category') && svgData[key].layers[a].type==='label'){
        if (!landmarkCategoires.includes(svgData[key].layers[a].category)){
          landmarkCategoires.push(svgData[key].layers[a].category);
        }
      } else if (Object.hasOwn(svgData[key].layers[a], 'landmark')){
        if (!landmarkCategoires.includes(Store.landmarkData[svgData[key].layers[a].landmark.landmark_id].category)){
          landmarkCategoires.push(Store.landmarkData[svgData[key].layers[a].landmark.landmark_id].category);
        }

      }
    }
  }
  const categoryNames = landmarkCategoires;

  // Step 2: Filter the category object
  filteredCategory = Object.keys(LandmarkCategories.category)
    .filter((key) => categoryNames.includes(key) || key === 'All')
    .reduce((obj, key) => {
      let displayName = LandmarkCategories.category[key].value;
      if (key === 'health') {
        displayName = 'healthcare';
      } else if (key === 'landmark') {
        displayName = 'landmarks';
      }
      obj[key] = {
        ...LandmarkCategories.category[key],
        displayName: displayName,
      };
      return obj;
    }, {});

}
</script>
<template>
  <!-- <rect
    v-if="show_modal"
    class="rect_overlay"
    width="100%"
    height="100%"
    fill="rgba(0, 0, 0, 0.5)"
  /> -->

  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="layer in Object.values(routeData).filter(filter_layer=>{return selectedData['route']===filter_layer.layer_data.layer_id})"
    :id="layer.layer_data.layer_id"
    :key="layer"
    class="routeCls"
    :class="props.sceneType!=='deep_zoom'?[layer.layer.getAttribute('class') +' '+layer.layer_data.type]:''"
    v-html="layer.layer.innerHTML"
  />
  <g
    v-for="(layer,index) in layerData"
    v-show="props.sceneType!=='deep_zoom' &&(Store.landmarkData[layer.layer_data.landmark['landmark_id']]?.category===currentCategory || currentCategory===undefined)"
    :id="`landmarkData+${index}`"
    :key="index"
    class="cursor-pointer "
    :class="[
      selectedData.landmark
        ? Store.landmarkData[selectedData.landmark]._id === layer.layer_data.landmark.landmark_id
          ? '!opacity-100'
          : '!opacity-50'
        : 'opacity-100',
      props.sceneType !== 'deep_zoom'
        ? [
          layer.layer.getAttribute('class') + ' transition-all duration-3000 ' + layer.layer_data.type,
          route.query.landmarkId === layer.layer_data.layer_id || route.query.landmarkId === undefined ? '' : ''
        ]
        : ''
    ]"

    @click="(event) =>placeModal(layer.layer_data.layer_id,layer.layer_data.landmark['landmark_id'], event)"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
  <!-- eslint-disable vue/no-v-html -->
  <!--eslint-enable-->
  <portal to="landmark">
    <div
      v-if="show_modal && Store.landmarkData"
      class="fixed top-0 left-0 w-screen h-full flex justify-center z-20"
      @click="closeModal"
    >
      <LandMarkCard
        class="descriptionAnimation items-center absolute"
        :class="[
          Object.values(filteredCategory).length > 2 ? 'bottom-24' : Store.isMobile ? 'bottom-20':'bottom-8',
          Store.isLandscape ? '!bottom-2 right-20' : ''
        ]"
        :name="Store.landmarkData[selectedData['landmark']].name "
        :description="Store.landmarkData[selectedData['landmark']].description"
        :distance="Store.landmarkData[selectedData['landmark']].distance"
        :car-timing="Store.landmarkData[selectedData['landmark']].car_timing"
        :transit-timing="Store.landmarkData[selectedData['landmark']].transit_timing"
        :walk-timing="Store.landmarkData[selectedData['landmark']].walk_timing"
        :thumbnail="Store.landmarkData[selectedData['landmark']].thumbnail"
        :data="Store.landmarkData[selectedData['landmark']]"
        :is-mobile="Store.isMobile"
        @close-card="closeModalX"
        @go-to-project="handleGoToLandmark"
      />
    </div>
    <!-- <div class="flex justify-center">
      <div class="absolute z-10 bottom-[6.4rem] sm:bottom-auto sm:top-12 top-auto px-4 ">
        <PopUpMessageBox
          message="Enter to full screen mode for best experience"
          button="Enter"
          @close-x="setFullScreenCookie"
          @enter="onClickButton('fullscreen')"
        />
      </div>
    </div> -->
    <!-- <div class="w-full h-full absolute top-0 bg-black opacity-35 z-1"></div> -->
    <div
      v-if="Object.values(filteredCategory).length > 2"
      class="fixed w-full flex justify-center z-10"
      :class="Store.isMobile ? 'bottom-24' : Store.isLandscape? 'bottom-3': 'bottom-10'"
    >
      <NearByFloatingButton
        v-if="(!Store.isMobile && !Store.isLandscape) || (Store.isMobile && !show_modal) || (Store.isLandscape && !show_modal)"
        class="flex"
        :class="Store.isMobile || Store.isLandscape ? 'w-full' : 'sm:w-[49%] md:w-[59%] lg:w-[69%] xl:[70%]'"
        :itemsList="Object.values(filteredCategory)"
        :active="activeCategory ? Object.values(filteredCategory).findIndex((e) => e.value === activeCategory) : 0"
        :sliderButton="false"
        :objectNameKey="`displayName`"
        :objectIconKey="`icon`"
        @button-clicked="setActiveButton"
      />
    </div>
  </portal>
</template>
<style scoped>
  .routeCls{
    stroke-dasharray: 10000;
    stroke-dashoffset: 10000;
     animation: dash 20s linear forwards;
  }

  @keyframes dash {
        to {
          stroke-dashoffset: 0;
        }
      }

@media screen and (min-width: 768px) {

  .slide-animation
{

  animation:slide 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}

}
@media screen and (max-width: 768px) {
.slide-animation
{
  animation:popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}
}
@keyframes slide
{
  0%
  {
    right :-24em;
  }
  50%
  {
    right:6em;
  }
  100%
  {
    @apply md:right-16 ;

  }
}

@keyframes popup
{
  0%
  {
    bottom :-24em;
  }

  100%
  {
    @apply md:bottom-0 ;

  }
}
.descriptionAnimation
{
  animation:des-popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}

@keyframes des-popup
{
  0%
  {
    bottom :-24em;
  }

  100%
  {
    @apply md:bottom-8 ;

  }
}
.opacity-70 { opacity: 0.7; }
.opacity-100 { opacity: 1; }
/* .drop-shadow { filter: drop-shadow(2px 4px 6px black); } */
</style>
