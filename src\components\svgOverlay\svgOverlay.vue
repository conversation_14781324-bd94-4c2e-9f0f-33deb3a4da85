<script setup>
import { ref, defineProps, defineEmits, watch, onUnmounted } from 'vue';
import IndexLayer from "./IndexLayer.vue";
import imageLayer from './Layers/imageLayer.vue';
// Import LoaderComponent from '../ALEComponents/LoaderComponent/LoaderComponent.vue';
// Import breadCrumb from '../ALEComponents/BreadCrumb/BreadCrumb.vue';
import { creationToolStore } from '../../store';
import {useRoute} from 'vue-router';
import router from '../../router/index';
import FloorPlanCard from '../ALEComponents/FloorPlanCard/FloorPlanCard.vue';
import CloudTransition from '../ALEComponents/CloudTransition/CloudTransition.vue';
import InfoModal from '../ALEComponents/InfoModal/InfoModal.vue';
import  svgPanZoom from "svg-pan-zoom";
import { cdn, getCookie, getSplashCookie } from '../../helpers/helper';
import ToggleButton from '../ALEComponents/ToggleButton/ToggleButton.vue';
import ProgressBar from '../ALEComponents/ProgressBar/ProgressBar.vue';
// Import PictureCard from '../ALEComponents/PictureCard/PictureCard.vue';
const Store = creationToolStore();
const route = useRoute();
// Const image = ref("https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fpixy.org%2Fsrc%2F477%2F4774988.jpg&f=1&nofb=1&ipt=9c4dfb810ffe9c2a015db0547035e8ce0f209c40394d0b76df073bdbb8e1be0c&ipo=images");
const emit = defineEmits([
  'clickEvent',
  'mouseEnter',
  'mouseLeave',
  'imageHigResLoaded',
  'imageLowResLoaded',
  'svgLoaded',
  'transitionEnd',
  'onClick',
  'removeLoader',
  'removeOverlay',
  'loadothers',
]);
const portaltargets = ref([]);
const showSlider = ref(false);
const containerRef = ref(null), SVGLayer=ref(null), lowResLoaded=ref(false), highresLoader=ref(false), showAnimation=ref(false), animationImageurl=ref(false), showUnitplanCard=ref(false);
const props = defineProps({
  data: {
    type: Object,
    default () {
      return {};
    },
  },
  bucketURL: {type: String, default: ""},
  replaceURL: {type: String, default: ""},
  landmarkData: {
    type: Object,
    default () {
      return {};
    },
  },
});
const transitionHelpRef = ref(null), currentId=ref(props.data.sceneData._id);
const layers = ref([]);
const showLoader = ref(true), svgLoaded=ref(false);
const checkAutoExit = ref(false);
const closeFullscreenModal = ref(false);
const loaderProgress = ref(null);
const splashCookie = getSplashCookie();
// Store.hideLogo=true;
async function readSVGLink (data) {
  return new Promise((resolve) => {
    var requestOptions = {
      method: "GET",
      redirect: "follow",
    };
    if (data.svg_url) {
      const url =  cdn(data.svg_url);
      fetch(url, requestOptions)
        .then((response) => response.text())
        .then((result) => {
          var tempElement = document.createElement('div');
          tempElement.innerHTML = result;
          for (let i = 0; i<tempElement.children.length;i++){
            for (let j = 0; j<tempElement.children[i].children.length;j++){
              let layerType;
              if (data.layers[tempElement.children[i].children[j].id]){
                layerType =data.layers[tempElement.children[i].children[j].id].type;
                if (!portaltargets.value.includes(layerType)) {
                  portaltargets.value.push(layerType);
                }
              } else {
                layerType='style';
              }
              layers.value[layerType] = layers.value[layerType]?layers.value[layerType]:layerType==='style'?[]:{};
              if (layerType==='style'){
                layers.value[layerType].push(
                  {
                    layer_data: data.layers[tempElement.children[i].children[j].id],
                    layer: tempElement.children[i].children[j],
                  },
                );
              } else {
                layers.value[layerType][tempElement.children[i].children[j].id]=
                {
                  layer_data: data.layers[tempElement.children[i].children[j].id],
                  layer: tempElement.children[i].children[j],
                };
              }
            }
          }
          resolve();
        });
    }

  });
}
function scrollToCenter () {
  if (window.panzoomer) {
    window.panzoomer.resize(); // update SVG cached size and controls positions
    window.panzoomer.fit();
    window.panzoomer.center();
  }
  const container = containerRef.value;
  const scrollTarget = SVGLayer.value;
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;

  if (screenWidth < 991) {
    const targetWidth = scrollTarget.clientWidth;
    const scrollLeft = (targetWidth - screenWidth) / 2;
    container.scrollLeft = scrollLeft;
  }

  if (screenHeight < 700) {
    const targetHeight = scrollTarget.clientHeight;
    const scrollTop = (targetHeight - screenHeight) / 2;
    container.scrollTop = scrollTop;
  }
}

window.addEventListener('resize', scrollToCenter);
function initSVG (){
  if (window.panzoomer) {
    window.panzoomer.resetZoom();
    window.panzoomer.resetPan();
  }
  loaderProgress.value = 0;
  layers.value = [];
  emit('removeOverlay', false);
  svgLoaded.value=false;
  if (Object.keys(props.data.svgData).length) {
    Promise.all([...Object.values(props.data.svgData).map((svg) => readSVGLink(svg))]).then(() => {
      svgLoaded.value=true;

      setTimeout(() => {
        scrollToCenter();
        // SetupPanzoom()

      }, 100);
    });
  }

}
initSVG();
watch(props, () => {
  showLoader.value=true;
  lowResLoaded.value=false;
  highresLoader.value=false;
  if (window.panzoomer) {
    // window.panzoomer.destroy();
    // delete window.panzoomer;
  }
  if (currentId.value===props.data.sceneData.parent){
    animationImageurl.value=Store.SceneData[props.data.sceneData.parent].sceneData.background.low_resolution;
    showAnimation.value=true;
  } else {
    animationImageurl.value=Store.SceneData[currentId.value].sceneData.background.low_resolution;
    showAnimation.value=true;
  }
  currentId.value=props.data.sceneData._id;
  initSVG();
});
function lowresLoaded (){
  emit('removeLoader');
  lowResLoaded.value=true;
  highresLoader.value=false;
  showLoader.value=false;
  if (props.data.sceneData.root){
    setTimeout(() => {
      if (!Store.showInfoModal.includes(props.data.sceneData._id)) {
        Store.showInfoModal.push(props.data.sceneData._id);
      }
    }, 4000);
  }
  if (transitionHelpRef.value) {
    transitionHelpRef.value.startAnimation();
  }
}
function findCenterOfDiv () {
  const panstart_panmove = new CustomEvent('hammer_panstart_panmove', {
    detail: {
      key: 'event',
    },
  });
  document.dispatchEvent(panstart_panmove);

}

function initpanZoom (options) {
  var instance = options.instance,
    initialScale = 1,
    pannedX = 0,
    pannedY = 0;
  // Init Hammer
  // Listen only for pointer and touch events
  this.hammer = window.Hammer(options.svgElement, {
    InputClass: window.Hammer.SUPPORT_POINTER_EVENTS ? window.Hammer.PointerEventInput : window.Hammer.TouchInput,
  });
  // Enable pinch
  this.hammer.get('pinch').set({enable: true});
  // Handle double tap
  this.hammer.on('doubletap', function (){
    instance.zoomIn();
  });
  // Handle pan
  this.hammer.on('panstart panmove', function (ev){
    // On pan start reset panned variables
    if (ev.type === 'panstart') {
      pannedX = 0;
      pannedY = 0;
    }
    // Console.log(instance,{x: ev.deltaX - pannedX, y: ev.deltaY - pannedY})
    // Pan only the difference
    instance.panBy({x: ev.deltaX - pannedX, y: ev.deltaY - pannedY});
    pannedX = ev.deltaX;
    pannedY = ev.deltaY;
  });
  // Handle pinch
  this.hammer.on('pinchstart pinchmove', function (ev){
    // On pinch start remember initial zoom
    if (ev.type === 'pinchstart') {
      initialScale = instance.getZoom();
      instance.zoomAtPoint(initialScale * ev.scale, {x: ev.center.x, y: ev.center.y});
    }
    instance.zoomAtPoint(initialScale * ev.scale, {x: ev.center.x, y: ev.center.y});
  });
  // Prevent moving the page on some devices when panning over SVG
  options.svgElement.addEventListener('touchmove', function (e){
    e.preventDefault();
  });
  options.svgElement.addEventListener('mousemove', function (e){
    e.preventDefault();
  });
}
function setupPanzoom (){
  var beforePan;
  var eventsHandler;
  beforePan = function (oldPan, newPan){
    var gutterWidth = this.getSizes().width,
      gutterHeight = this.getSizes().height,
      // Computed variables
      sizes = this.getSizes(),
      leftLimit = -((sizes.viewBox.x + sizes.viewBox.width) * sizes.realZoom) + gutterWidth,
      rightLimit = sizes.width - gutterWidth - (sizes.viewBox.x * sizes.realZoom),
      topLimit = -((sizes.viewBox.y + sizes.viewBox.height) * sizes.realZoom) + gutterHeight,
      bottomLimit = sizes.height - gutterHeight - (sizes.viewBox.y * sizes.realZoom);

    var customPan = {};
    customPan.x = Math.max(leftLimit, Math.min(rightLimit, newPan.x));
    customPan.y = Math.max(topLimit, Math.min(bottomLimit, newPan.y));
    return customPan;
  };
  eventsHandler = {
    haltEventListeners: ['touchstart', 'touchend', 'touchmove', 'touchleave', 'touchcancel'],
    init: initpanZoom,
    destroy: function (){
      this.hammer.destroy();
    },
  };
  const panzoomer =  svgPanZoom(`#SVGLayer`, {
    viewportSelector: '.svg-pan-zoom_viewport',
    zoomEnabled: true,
    controlIconsEnabled: false,
    fit: false,
    contain: true,
    center: true,
    beforePan: beforePan,
    onZoom: findCenterOfDiv,
    onPan: findCenterOfDiv,
    preventMouseEventsDefault: true,
    dblClickZoomEnabled: false,
    minZoom: 1,
    customEventsHandler: eventsHandler,
  });
  window.panzoomer = panzoomer;
}
function highresLoaded (){
  highresLoader.value=true;

  if (!props.data.sceneData.video) {
    setupPanzoom();
  }
  emit('loadothers');
}
// If (getCookie('oldSceneId') && getCookie('oldSceneId')!=='undefined'){
//   LowResLoaded.value=false;
//   If (getCookie('oldSceneId')===props.data.sceneData.parent){
//     AnimationImageurl.value=Store.SceneData[props.data.sceneData.parent].sceneData.background.low_resolution;
//     ShowAnimation.value=true;
//   } else {
//     AnimationImageurl.value=Store.SceneData[getCookie('oldSceneId')].sceneData.background.low_resolution;
//     ShowAnimation.value=true;
//   }
// }

function setFullScreenCookie () {
  if (!getCookie('fullscreen')) {
    const expiryTime = new Date(Date.now() + (30 * 60000));
    document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
    closeFullscreenModal.value = true;
  }
}
// ADDED
const onClickButton = (id) => {
  if (id === 'radius'){
    router.push({ name: route.fullPath.includes('masterscene') ? 'masterScene' : 'projectScene', query: { ...route.query, [id]: route.query[id] === undefined ? true : route.query[id] === 'false' ? true : false } });
    if (route.query[id] === 'true') {
      document.getElementById("svg_highres_image").classList.remove("opacity-50");
    } else {
      document.getElementById("svg_highres_image").classList.add("opacity-50");
    }
  }
  if (id === 'fullscreen') {
    setFullScreenCookie();
    if (!document.fullscreenElement &&
      !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.mozRequestFullScreen) {
        document.documentElement.mozRequestFullScreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
      } else if (document.documentElement.msRequestFullscreen) {
        document.documentElement.msRequestFullscreen();
      }
      checkAutoExit.value = false;
      Store.isFullScreen = !Store.isFullScreen;
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
      checkAutoExit.value = true;
      Store.isFullScreen = !Store.isFullScreen;
    }

  }
};

watch(() => loaderProgress.value, () => {
  if (!loaderProgress.value){
    emit('removeOverlay', true);
  }
});

function gotoUnit (){
  router.push({name: "unit.Child", params: {unitplanId: Store.allUnitCardData[showUnitplanCard.value].unitplan_id}, query: {...route.query, unit_id: showUnitplanCard.value, unit_name: Store.allUnitCardData[showUnitplanCard.value].name}});
  showUnitplanCard.value=false;
}
function closeModal (e){
  if (e.target.getAttribute("clickaway")==="true"){
    showUnitplanCard.value=false;
  }
}

// Function goToPreviousScene () {
//   If (Store.SceneData[route.params.sceneId].sceneData.parent){
//     Router.push({name: 'projectScene', params: {sceneId: Store.SceneData[route.params.sceneId].sceneData.parent}});
//   }
// }

setTimeout(() => {
  showSlider.value=true;
}, 600);

onUnmounted(() => {
  Store.hideLogo=false;
});

</script>

<template>
  <ProgressBar
    v-if="showLoader && (splashCookie[route.params.projectId] ||
      (Store.projectCardData?.[route.params.projectId]?.projectSettings?.ale &&
        (!Store.projectCardData[route.params.projectId].projectSettings.ale.welcome_video ||
          !Store.projectCardData[route.params.projectId].projectSettings.ale.welcome_thumbnail))) || (loaderProgress!==false && loaderProgress!==null)"
    :class="!Store.currentSceneVideo && !Store.currentSceneVideoThumb?'z-20':'z-7'"
    :progress="loaderProgress"
  />
  <!-- <div  class="sm:fixed  w-full sm:top-20 sm:right-7 z-[4] sm:bottom-auto bottom-24 absolute flex justify-center sm:align-bottom sm:justify-end">
  <PictureCard
    :imageUrl = "image"
    title= "Palmiera"
    location=" Business Bay"
    bedrooms="2 to 4 BR"
    price=" AED 75,00,000"
    type="Apartment"
  />
</div> -->
  <InfoModal
    v-if="!Store.showInfoModal.includes(props.data.sceneData._id) && props.data.sceneData.root"
    :info-text="props.data.sceneData.info_text"
    :info-icon="Store.logo === 'light' ? 'https://storagecdn.propvr.tech/CreationtoolAssets%2Fsiteassets%2Fclick_white.svg?alt=media' : 'https://storagecdn.propvr.tech/CreationtoolAssets%2Fsiteassets%2Fblack_click.svg?alt=media'"
  />
  <CloudTransition
    v-if="props.data.sceneData.clouds &&!showLoader"
    class="z-[1]"
  />
  <!-- <div
    v-if="!showLoader && props.data.sceneData"
    class="absolute top-0 z-[4] sm:hidden"
  >
    <Backbar
      :is-root="props.data.sceneData.root"
      :scene-name="route.query.floor_id? 'Floor '+route.query.floor_id: Store.SceneData[route.params.sceneId].sceneData.name"
      :thumbnail="Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo"
      @go-back="goToPreviousScene"
    />
  </div> -->
  <!-- <div v-if="showAnimation" class="relative w-full h-full ">
    <transitionComp   @animationEnd="animationEnd"  ref="transitionHelpRef" :lowResImage="animationImageurl"></transitionComp>
  </div> -->

  <!-- <div
    v-if="!showLoader"
    class="absolute w-full  top-0 z-[4]"
  >
    <DamacHeader
      :property-data="Store.unitData"
      @gotounit="GotounitCard"
      @open-favourites="openFavourites"
    />

    <div class="left-0 top-[0.45rem] z-[2]  w-full ">
      <breadCrumb
        :logo="Store.organization_thumbnail"
        :bread-crumb-data="Store.breadCrumb"
        @move-to-scene="moveToLocation"
        @to-full-screen="onClickButton"
      />
    </div>
  </div> -->
  <div
    v-if="showUnitplanCard"
    class="fixed top-0 floordataa z-20 left-0 w-screen h-screen"
    clickaway="true"
    @click="closeModal"
  >
    <FloorPlanCard
      style="position: absolute;right: 1.5rem;top: 8rem;"
      :unitId="Store.allUnitCardData[showUnitplanCard]._id"
      :title="Store.allUnitCardData[showUnitplanCard].name"
      :status="Store.allUnitCardData[showUnitplanCard].status"
      :price="Store.allUnitCardData[showUnitplanCard].price"
      :measurement="Store.allUnitCardData[showUnitplanCard].measurement"
      :bedrooms="Store.allUnitCardData[showUnitplanCard].bedroom"
      :currency-title="Store.allUnitCardData[showUnitplanCard].currency"
      :is-show-buttons="true"
      button-view="floorplanview"
      :isMobile="Store.isMobile"
      :hideStatus="Store.hideStatus"
      @show-unitplan="gotoUnit"
    />
  </div>
  <div class="svgContainer">
    <div class="svgWrapper">
      <div
        id="svgContent"
        ref="containerRef"
        class="svgContent"
      >
        <svg
          id="SVGLayer"
          ref="SVGLayer"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          x="0px"
          y="0px"
          viewBox="0 0 1920 1080"
          style="enable-background:new 0 0 1920 1080;"
          xml:space="preserve"
          preserveAspectRatio="xMidYMid slice"
          class="svgLayer z-20"
        >
          <imageLayer
            :lowres="props.data.sceneData.background.low_resolution"
            :highres="props.data.sceneData.background.high_resolution"
            :video="props.data.sceneData.video"
            @lowres-loaded="lowresLoaded"
            @highres-loaded="highresLoaded"
            @progress-data="(progress)=>{loaderProgress=progress}"
          />
          <IndexLayer
            v-if="svgLoaded && lowResLoaded"
            :layer-data="layers"
          />
        </svg>
        <div />
      </div>

      <!-- <div
        v-if="Store.isMobile"
        class="absolute top-32 right-8 mt-2 max-sm:right-4 flex flex-col-reverse max-sm:top-32 justify-center items-end  z-[5]"
      >
        <ControlButton
          v-if="layers['radius']"
          :is-hover="showTooltip.includes('radius')"
          :active="route.query['radius'] === 'true'"
          :inactive-s-v-g="toolBtnData.radiusInactiveSVG"
          :active-s-v-g="toolBtnData.radiusActiveSVG"
          class="!m-0 border"
          :class="Store.isMobile ? 'h-10 w-10 p-' : ''"
          @on-click="onClickButton(&quot;radius&quot;)"
          @mouseout="removeItem('radius')"
        />
      </div> -->
      <div
        v-if="!Store.isMobile && !showLoader && !Store.isLandscape"
        class="fixed bottom-10 right-5 z-10"
      >
        <ToggleButton
          v-if="layers['radius']"
          :label="'Radius'"
          :active="route.query['radius'] === 'true'"
          @on-toggle="onClickButton(&quot;radius&quot;)"
        />
      </div>
    </div>
  </div>
  <portal-target
    v-for="name in portaltargets"
    :key="name"
    :name="name"
  >
    <slot />
  </portal-target>

  <portal-target
    name="image_layer"
  >
    <slot />
  </portal-target>
</template>

<style scoped>
.svgContainer {
  height: 100%;
  width: 100%;
  position: relative;
  user-select: none;
  overflow: hidden;
  /* transform-origin: center center; */
  /* transition: transform 0.5s, opacity 0.5s; */
  /* position: absolute;
  top: 0; */

}

/* @media screen and (max-width: 480px)
{
  .svgContainer {
    height: 85%;
  }
} */
/* .svgLayer{
  animation-name: FadeIn;
  animation-duration: 3s;
}
@keyframes FadeIn {
  from {
    opacity:0;
  }
  to {
    opacity:1;
  }
} */
.svgWrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.fade-in-overlay {
  opacity: 0;
  transition: opacity 2s ease;
}

.fade-in-animation {
  opacity: 1;
}

.svgContainer.active {
  transform: scale(1.2);
  opacity: 0;
}

.svgContent {
  width: 100%;
  height: 100%;
  overflow-x: auto;
  white-space: nowrap;
  scroll-behavior: smooth;
}

.svgContent::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.svg-pan-zoom_viewport
{
  height: 100%;
  width: 100%;
}
</style>
