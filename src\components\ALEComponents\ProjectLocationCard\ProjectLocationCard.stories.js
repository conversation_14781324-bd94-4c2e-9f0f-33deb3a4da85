import ProjectLocationCard from './ProjectLocationCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/ProjectLocationCard',
  component: ProjectLocationCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    title: "Bays102 by Danube",
    name: "Business Bay",
    minBedroom: 2,
    maxBedroom: 4,
    currency: 'AED',
    price: 7500000,
    image: 'https://flowbite.com/docs/images/blog/image-4.jpg',
  },
  parameters: {
    design:
      {
        type: "figma",
        url: "https://www.figma.com/design/tzsvPTCvtPiJh166XcefIW/PropVR-Lite-with-Flowbite-Design-system?node-id=130-1799&m=dev",
      },
  },
};
