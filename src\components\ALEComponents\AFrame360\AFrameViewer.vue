<script setup>
/* global THREE */
import { ref, onMounted, watch, computed, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { creationToolStore } from '../../../store';
import NearByFloatingButton from '../NearByFloatingButton/NearByFloatingButton.vue';
import DropDown from '../DropDown/DropDown.vue';
import { cdn, Googleanalytics } from '../../../helpers/helper';
import hotspotIcon from '../../../../public/assets/VRTour/hotspot.svg';
// import * as THREE from 'three';

const route = useRoute();
const Store = creationToolStore();
const props = defineProps({
  tourData: {
    type: Object,
    required: true,
  },
});
const lastRotation = ref({ x: 0, y: 0, z: 0 });
const isChangingImage = ref(false);

// console.log("tourData", props.tourData);
const isTextureLoaded = ref(false);
const isThumbLoaded = ref(false);
const SceneTag = ref(null);
const cameraControls =  ref(null);
const cameraControls_camera = ref(null);
const isFirstLoad = ref(true);
const isInitialOrientationPhase = ref(true);

const currentImageId = ref('');
const currentGroupId = ref('');
const isLoading = ref(true);
const isMounted = ref(false);
const showHotspots = ref(true);
const imageLoading = ref(true);
const startTime = ref(new Date), timespent = ref(0);
// console.log("isMobile: " + Store.isMobile);

// Define hasGroups computed property early to avoid use-before-define
const hasGroups = computed(() => props.tourData?.groups && Object.keys(props.tourData.groups).length > 0);
const hotspotSvgPath = computed(() => {
  return hotspotIcon || cdn('assets/hotspot.svg');
});
function postMessageToParent (type, data) {
  try {
    if (window.parent !== window) {
      console.log('Attempting to send message:', { type, data });
      window.parent.postMessage({
        type,
        data,
      }, '*');
      console.log('Message sent successfully');
    } else {
      console.log('Running in top-level window, skipping postMessage');
    }
  } catch (error) {
    console.error('Failed to post message:', error);
  }
}
//  Tracking function for image changes
const trackImageChange = (image) => {
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  console.log("name", Store.projectCardData?.name);
  timespent.value = difference;
  const currentGroup = props.tourData.groups?.[currentGroupId.value] || {};
  Googleanalytics("vr_tour_image_click", {
    organization_id: route.params.organizationId || '',
    organization_name: Store.organization_name || '',
    project_name: Store.projectCardData?.name || '',
    project_id: route.params.projectId || '',
    tour_name: props.tourData?.name || '',
    tour_id: route.params.tourId || '',
    unitplan_name: Store.unitplanData[route.params.unitplanId]?.name || '',
    unitplan_id: route.params.unitplanId || '',
    group_id: currentGroupId.value || '',
    group_name: currentGroup?.name || '',
    image_id: image?._id,
    image_name: image?.name || '',
    // subgroup_id: '',
    // subgroup_name: '',
    // image_timespent: timespent.value || '',
    // image_timestart: startTime.value|| '',
  });
};

//  Tracking function for group changes
const trackGroupChange = (groupId) => {
  const selectedGroup = props.tourData.groups?.[groupId] || {};

  Googleanalytics("vr_tour_group_click", {
    organization_id: route.params.organizationId || '',
    organization_name: Store.organization_name || '',
    project_name: Store.projectCardData?.name || '',
    project_id: route.params.projectId || '',
    tour_name: props.tourData?.name || '',
    tour_id: route.params.tourId || '',
    unitplan_name: Store.unitplanData[route.params.unitplanId]?.name || '',
    unitplan_id: route.params.unitplanId || '',
    group_id: groupId || '',
    group_name: selectedGroup?.name || '',
    // subgroup_id: '',
    // subgroup_name: '',
  });
};

// Define resetCameraOrientation function early to avoid use-before-define
const resetCameraOrientation = () => {
  const camera = document.getElementById('player');
  if (camera && camera.object3D) {
    // Reset to default orientation
    camera.object3D.rotation.set(0, 0, 0);
    camera.object3D.quaternion.set(0, 0, 0, 1);

    // If using CameraControls
    if (cameraControls.value) {
      cameraControls.value.reset(false);
    }
  }
};

const applyOrientation = (orientationStr, isInitial = false) => {
  if (!orientationStr) {
    return;
  }

  try {
    const parts = orientationStr.split(' ').map(Number);
    if (parts.length < 2) {
      return;
    }

    // Normalize the rotation values to standard ranges
    let xDeg = parts[0];
    let yDeg = parts[1];
    const zDeg = parts[2] || 0;

    // Normalize Y rotation to 0-360 range
    yDeg = yDeg % 360;
    if (yDeg < 0) {
      yDeg += 360;
    }

    // Keep X in reasonable range (-90 to 90)
    xDeg = Math.max(-90, Math.min(90, xDeg));

    console.log(`Normalized rotation: ${xDeg} ${yDeg} ${zDeg}`);

    const camera = document.getElementById('player');
    if (!camera || !camera.object3D) {
      return;
    }

    // Use XYZ for initial orientation (from image data)
    // Use YXZ for live tracking updates
    const rotationOrder = isInitial ? 'XYZ' : 'YXZ';

    console.log(`Applying orientation with order ${rotationOrder}:`, `${xDeg} ${yDeg} ${zDeg}`);

    // For initial loads, we need to be more aggressive
    if (isFirstLoad.value && isInitial) {
      // Reset the camera first
      camera.object3D.rotation.set(0, 0, 0);
      camera.object3D.updateMatrix();

      // Apply a direct rotation first
      camera.object3D.rotation.x = THREE.Math.degToRad(xDeg);
      camera.object3D.rotation.y = THREE.Math.degToRad(yDeg);
      camera.object3D.rotation.z = THREE.Math.degToRad(zDeg);
      camera.object3D.rotation.order = rotationOrder;
      camera.object3D.updateMatrix();
      camera.object3D.updateMatrixWorld(true);

      // Then also apply via quaternion for good measure
      const quaternion = new THREE.Quaternion();
      quaternion.setFromEuler(new THREE.Euler(
        THREE.Math.degToRad(xDeg),
        THREE.Math.degToRad(yDeg),
        THREE.Math.degToRad(zDeg),
        rotationOrder,
      ));
      camera.object3D.quaternion.copy(quaternion);

      // Force full updates
      camera.object3D.updateMatrix();
      camera.object3D.updateMatrixWorld(true);

      if (cameraControls.value) {
        // Force the controls to fully reset and update
        cameraControls.value.reset(true);
        cameraControls.value.update(0);
      }
    } else {
      // Normal orientation application for subsequent changes
      const quaternion = new THREE.Quaternion();
      quaternion.setFromEuler(new THREE.Euler(
        THREE.Math.degToRad(xDeg),
        THREE.Math.degToRad(yDeg),
        THREE.Math.degToRad(zDeg),
        rotationOrder,
      ));

      camera.object3D.quaternion.copy(quaternion);
      camera.object3D.updateMatrixWorld(true);

      if (cameraControls.value) {
        cameraControls.value.update(0);
      }
    }
  } catch (error) {
    console.error('Error applying orientation:', error);
  }
};

// Then modify the setOrientation function to pass the isInitial flag
const setOrientation = (orientationStr, isInitial = false) => {
  applyOrientation(orientationStr, isInitial);
};

const currentImage = computed(() => {
  if (!currentImageId.value || !props.tourData?.images) {
    return null;
  }
  return props.tourData.images[currentImageId.value];
});

// Define switchImage function early to avoid use-before-define
function switchImage (imageId) {
  if (imageId === currentImageId.value) {
    return;
  }
  const image = props.tourData.images[imageId];
  console.log("Image", image);
  trackImageChange(image);

  isChangingImage.value = true;
  showHotspots.value = false;
  isTextureLoaded.value = false;
  isThumbLoaded.value = false;
  currentImageId.value = imageId;

  if (image?.groupId) {
    currentGroupId.value = image.groupId;
  }

  // Clear any existing camera modifications
  resetCameraOrientation();

  // Post message about image change with COMPLETE image data
  console.log('Sending imageChange message:', {
    imageId,
    imageName: image?.name,
    thumbnail: image?.thumbnail,
    url: image?.url,
    rotation: image?.rotation,
  });
  postMessageToParent('imageChange', {
    imageId,
    imageName: image?.name,
    thumbnail: image?.thumbnail,
    url: cdn(image?.url),
    rotation: image?.rotation,  // Include rotation data
  });

  // Apply orientation if available for this image
  if (image?.rotation) {
    console.log('Setting initial rotation from image data:', image.rotation);
    setOrientation(image.rotation, true); // true = isInitial
  }

  // Force sky element update
  setTimeout(() => {
    const sky = document.querySelector('a-sky');
    if (sky && currentImage.value) {
      // First set thumbnail for faster loading
      sky.setAttribute('src', cdn(currentImage.value.thumbnail));

      // Then schedule the full image
      setTimeout(() => {
        sky.setAttribute('src', cdn(currentImage.value.url));
      }, 100);
    }
  }, 50);

  // Re-enable tracking after a longer delay
  setTimeout(() => {
    showHotspots.value = true;
    isChangingImage.value = false;

    // Force tracking to start working by explicitly setting both flags
    isInitialOrientationPhase.value = false;
    isFirstLoad.value = false;

    console.log('Image switch complete - tracking should now be active');
  }, 1000);
}

// Define findFirstImage function early to avoid use-before-define
const findFirstImage = () => {
  if (hasGroups.value && props.tourData?.groups) {
    const firstGroupId = Object.keys(props.tourData.groups)[0];
    const groupImage = Object.entries(props.tourData.images)
      .find(([_, image]) => image.groupId === firstGroupId);

    if (groupImage) {
      return groupImage[0]; // Return the image ID
    }
  }

  if (props.tourData?.images) {
    const firstImage = Object.values(props.tourData.images)[0];
    if (firstImage) {
      return firstImage.id; // Return the image ID
    }
  }

  return null;
};

// CameraControls Setup
const cameraControlsSetup = () => {
  console.log("cameraControlsSetup");
  const clock = new THREE.Clock();
  console.log("@@@@@", SceneTag.value);
  const scene1 = SceneTag.value;
  cameraControls_camera.value = document.getElementById('player').getObject3D('camera'); // camera
  cameraControls_camera.value.position.set(0, 0, 1e-5); // position setup XYZ (1e-5 - 0.00001)
  const renderer = scene1.renderer;
  cameraControls.value = new window.CameraControls(cameraControls_camera.value, renderer.domElement);

  // Main animation loop for camera controls
  (function anim () {
    const delta = clock.getDelta();
    cameraControls.value.update(delta);
    requestAnimationFrame(anim);
  })();

  window.cameraControls = cameraControls.value; // expose to global object.

  // Refactored to reduce nesting level
  const setupCameraTracking = () => {
    if (!cameraControls.value) {
      return;
    }

    const threshold = 0.5; // Minimum change in degrees to trigger update
    let lastRotation = { x: 0, y: 0, z: 0 };
    let throttleTimeout = null;

    // Function to track rotation and handle throttling
    const processRotationUpdate = () => {
      if (isInitialOrientationPhase.value || isChangingImage.value || throttleTimeout) {
        return;
      }

      try {
        // Get rotation from THREE camera directly
        if (!cameraControls_camera.value) {
          return;
        }

        // We need the camera's world rotation, not just the local rotation
        const worldQuaternion = new THREE.Quaternion();
        cameraControls_camera.value.getWorldQuaternion(worldQuaternion);

        // Convert quaternion to euler angles
        const euler = new THREE.Euler().setFromQuaternion(worldQuaternion, 'YXZ');

        // Convert from radians to degrees and ensure consistent format
        const currentRotation = {
          x: THREE.Math.radToDeg(euler.x),
          y: THREE.Math.radToDeg(euler.y),
          z: THREE.Math.radToDeg(euler.z),
        };

        // Check if rotation changed significantly
        const hasSignificantChange =
          Math.abs(currentRotation.x - lastRotation.x) > threshold ||
          Math.abs(currentRotation.y - lastRotation.y) > threshold ||
          Math.abs(currentRotation.z - lastRotation.z) > threshold;

        if (!hasSignificantChange) {
          return;
        }

        // Update last rotation
        lastRotation = { ...currentRotation };

        // Format as space-separated string (for orientation setting)
        const orientationStr = `${currentRotation.x} ${currentRotation.y} ${currentRotation.z}`;

        console.log('Sending rotation update:', orientationStr);

        // Send message with both object and string formats for flexibility
        postMessageToParent('cameraRotation', {
          x: currentRotation.x,
          y: currentRotation.y,
          z: currentRotation.z,
          orientation: orientationStr,
        });

        // Throttle updates
        throttleTimeout = setTimeout(() => {
          throttleTimeout = null;
        }, 50);
      } catch (error) {
        console.error('Error tracking camera rotation:', error);
      }
    };

    // Set up tracking loop
    function trackingLoop () {
      processRotationUpdate();
      requestAnimationFrame(trackingLoop);
    }

    trackingLoop();
  };

  // Setup tracking first - it will be controlled by isChangingImage
  setupCameraTracking();

  // Replace the applyInitialOrientation function with a simpler initializer
  const initializeScene = () => {
    console.log('Initializing scene with first image');

    // Find the first image to display
    const firstImage = findFirstImage();
    if (firstImage) {
      // Use switchImage for initial load, same as transitions
      console.log('Using switchImage for initial load:', firstImage);
      switchImage(firstImage);
    } else {
      console.warn('No images found to initialize scene');
    }
  };

  const scene = document.querySelector('a-scene');
  if (scene) {
    if (scene.hasLoaded) {
      initializeScene();
    } else {
      scene.addEventListener('loaded', initializeScene);
    }
  } else {
    // Fallback if scene not found yet
    setTimeout(initializeScene, 800);
  }
};

window.CameraControls.install( { THREE: window.THREE } );
delete window.AFRAME.components.initload; // delete the initial load existing component
window.AFRAME.registerComponent('initload', {  // Custom AFRAME Register Component - init load
  init: function () {
    console.log("initload");
    cameraControlsSetup(); // Run the cameracontrol setup mtd
  },
});

const getGroupsList = () => {
  if (!hasGroups.value) {
    return [];
  }

  // Filter out groups that have no images
  return Object.entries(props.tourData.groups)
    .filter(([groupId]) => {
      // Check if any images exist for this group
      return Object.values(props.tourData.images).some(
        (image) => image.groupId === groupId,
      );
    })
    .sort((a, b) => a[1].order - b[1].order)
    .map(([id, group]) => ({
      value: id,
      name: group.name,
      category: id,
    }));
};

const getImages = () => {
  if (!props.tourData?.images) {
    return [];
  }

  const images = Object.entries(props.tourData.images);

  if (hasGroups.value) {
    if (!currentGroupId.value) {
      return [];
    }
    return images
      .filter(([_, image]) => image.groupId === currentGroupId.value)
      // Add sorting by order
      .sort((a, b) => (a[1].order || 0) - (b[1].order || 0))
      .map(([id, image]) => ({
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
          <path d="M17.0309 10.9218C19.5182 6.33548 18.3845 3.22238 14.129 3C11.036 3.05905 7.84555 4.72552 5.29889 7.30361C3.16169 9.46717 1.05739 12.5658 1.82988 15.515C2.00045 16.1662 2.33304 16.6098 2.85842 17.0273C4.2703 18.1493 5.65613 18.248 8.32522 17.5927C11.0282 16.7714 12.704 15.534 14.117 14.2681M14.117 14.2681C14.1184 14.2668 14.1199 14.2654 14.1214 14.2641C14.124 14.2617 14.1223 14.2573 14.1187 14.2573C14.116 14.2573 14.1141 14.26 14.115 14.2625C14.1157 14.2644 14.1163 14.2663 14.117 14.2681ZM14.117 14.2681C14.4256 15.1808 14.2301 15.9953 13.6987 17.5927" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>`,
        id,
        value: image.id,
        name: image.name,
      }));
  }

  // If no groups, show all images sorted by order
  return images
    .sort((a, b) => (a[1].order || 0) - (b[1].order || 0))
    .map(([id, image]) => ({
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
        <path d="M17.0309 10.9218C19.5182 6.33548 18.3845 3.22238 14.129 3C11.036 3.05905 7.84555 4.72552 5.29889 7.30361C3.16169 9.46717 1.05739 12.5658 1.82988 15.515C2.00045 16.1662 2.33304 16.6098 2.85842 17.0273C4.2703 18.1493 5.65613 18.248 8.32522 17.5927C11.0282 16.7714 12.704 15.534 14.117 14.2681M14.117 14.2681C14.1184 14.2668 14.1199 14.2654 14.1214 14.2641C14.124 14.2617 14.1223 14.2573 14.1187 14.2573C14.116 14.2573 14.1141 14.26 14.115 14.2625C14.1157 14.2644 14.1163 14.2663 14.117 14.2681ZM14.117 14.2681C14.4256 15.1808 14.2301 15.9953 13.6987 17.5927" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>`,
      id,
      value: image.id,
      name: image.name,
    }));
};

function handleTextureLoaded () {
  console.log('handleTextureLoaded called, isThumbLoaded:', isThumbLoaded.value);

  if (!isThumbLoaded.value) {
    isThumbLoaded.value = true;
    // Switch to full resolution after thumbnail loads
    const sky = document.querySelector('a-sky');
    if (sky && currentImage.value) {
      console.log('Setting full resolution image:', currentImage.value.url);
      sky.setAttribute('src', currentImage.value.url);
      postMessageToParent('loadingStatus', {
        status: 'thumbnailLoaded',
        imageId: currentImageId.value,
      });
    }
  } else {
    // Full resolution loaded
    console.log('Full resolution image loaded');
    isTextureLoaded.value = true;
    imageLoading.value = false;
    isLoading.value = false;

    // Reapply orientation just to be sure
    if (currentImage.value?.rotation && cameraControls.value) {
      console.log('Reapplying orientation after full load:', currentImage.value.rotation);
      setOrientation(currentImage.value.rotation);
    }

    postMessageToParent('loadingStatus', {
      status: 'fullImageLoaded',
      imageId: currentImageId.value,
    });
  }
}

const handleHotspotClick = (destinationId) => {
  postMessageToParent('hotspotClick', {
    fromImageId: currentImageId.value,
    toImageId: destinationId,
  });
  switchImage(destinationId);
};

const handleGroupChange = (group) => {
  // console.log("handleGroupChange", group);
  const selectedGroupId = group.category || group.value;
  trackGroupChange(selectedGroupId);
  currentGroupId.value = null;
  setTimeout(() => {
    currentGroupId.value = group.category || group.value;
  }, 1);

  const groupImages = getImages();
  if (groupImages.length > 0) {
    switchImage(groupImages[0].id);
  }
};

const currentDropdownValue = computed(() => {
  if (!currentGroupId.value || !props.tourData?.groups?.[currentGroupId.value]) {
    return null;
  }
  return {
    name: props.tourData.groups[currentGroupId.value].name,
    category: currentGroupId.value,
  };
});

const currentIndex = computed(() => {
  const images = getImages();
  return images.findIndex((img) => img.id === currentImageId.value);
});

const leftButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M18.375 10.5L2.625 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M7 14.875L2.625 10.5L7 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

const rightButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M2.625 10.5L18.375 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M14 14.875L18.375 10.5L14 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

const handleImageChange = (item) => {
  if (item && item.id) {
    switchImage(item.id);
  }
};

const setupOrientationListener = () => {
  // First, check URL parameters for guest mode
  const urlParams = new URLSearchParams(window.location.search);
  const isGuest = urlParams.get('salestoolGuest') === 'true';

  const applyOrientationWithRetry = async (orientationString) => {
    // Reset camera first
    resetCameraOrientation();

    // Create an array of delay promises
    const applyPromises = [0, 100, 300, 500].map((delay) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          applyOrientation(orientationString, true);
          resolve();
        }, delay);
      });
    });

    // Wait for all orientation applications
    await Promise.all(applyPromises);

    // Re-enable tracking after final application
    isChangingImage.value = false;
    console.log('Guest orientation update complete');
  };

  window.addEventListener('message', async (event) => {
    try {
      const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

      // Skip if it's our own message
      if (data.isOwnMessage) {
        return;
      }

      // For guests, prioritize initialOrientation messages
      if (isGuest && (data.type === 'initialOrientation' || data.type === 'imageChange') && data.data) {
        console.log('Guest received orientation update:', data);

        // Set flags to prevent tracking during orientation update
        isChangingImage.value = true;

        let orientationString = '';

        // Extract orientation from various message formats
        if (typeof data.data === 'string') {
          orientationString = data.data;
        } else if (data.data.orientation) {
          orientationString = data.data.orientation;
        } else if (data.data.rotation) {
          orientationString = data.data.rotation;
        } else if (typeof data.data.x === 'number') {
          orientationString = `${data.data.x} ${data.data.y} ${data.data.z || 0}`;
        }

        if (orientationString) {
          console.log('Guest applying orientation:', orientationString);
          await applyOrientationWithRetry(orientationString);

          // Store this as the last rotation to prevent immediate change detection
          if (typeof lastRotation.value !== 'undefined') {
            const parts = orientationString.split(' ').map(Number);
            lastRotation.value = {
              x: parts[0],
              y: parts[1] % 360,
              z: parts.length > 2 ? parts[2] : 0,
            };
          }
        }
        return;
      }

      // Handle image changes in guest mode
      if (isGuest && data.type === 'imageChange' && data.data && data.data.imageId) {
        console.log('Guest received image change:', data.data);
        if (data.data.imageId && props.tourData?.images[data.data.imageId]) {
          console.log('Guest switching to image:', data.data.imageId);
          switchImage(data.data.imageId);
        }
        return;
      }

      // Handle regular orientation/rotation message types
      if ((data.type === 'updateOrientation' || data.type === 'cameraRotation') && data.data) {
        console.log('Received rotation update:', data);

        let orientationString = '';

        // Handle different message formats
        if (typeof data.data === 'string') {
          orientationString = data.data;
        } else if (data.data.orientation) {
          orientationString = data.data.orientation;
        } else if (typeof data.data.x === 'number') {
          orientationString = `${data.data.x} ${data.data.y} ${data.data.z || 0}`;
        }

        if (orientationString) {
          console.log('Applying orientation:', orientationString);
          applyOrientation(orientationString, false); // false = not initial (tracking)
        }
      }
    } catch (error) {
      console.error('Error processing orientation message:', error);
    }
  });
};

const registerComponents = () => {
  const AFRAME = window.AFRAME;
  if (!AFRAME) {
    return;
  }

  if (AFRAME.components['hotspot-listener']) {
    delete AFRAME.components['hotspot-listener'];
  }

  AFRAME.registerComponent('hotspot-listener', {
    init: function () {
      this.el.addEventListener('mouseenter', () => {
        const labelEntity = this.el.querySelector('.hotspot-label');
        if (labelEntity) {
          labelEntity.setAttribute('visible', true);
        }
        document.querySelector('a-scene').style.cursor = 'pointer';
      });

      this.el.addEventListener('mouseleave', () => {
        const labelEntity = this.el.querySelector('.hotspot-label');
        if (labelEntity) {
          labelEntity.setAttribute('visible', false);
        }
        document.querySelector('a-scene').style.cursor = 'default';
      });

      this.el.addEventListener('click', () => {
        const destinationId = this.el.getAttribute('data-destination');
        if (destinationId) {
          handleHotspotClick(destinationId);
        }
      });
    },
  });
};

function testPostMessage () {
  // Test each type of message
  postMessageToParent('tourLoaded', {
    totalImages: Object.keys(props.tourData.images || {}).length,
    hasGroups: hasGroups.value,
    groups: props.tourData.groups,
  });

  postMessageToParent('loadingStatus', {
    status: 'thumbnailLoaded',
    imageId: currentImageId.value,
  });

  postMessageToParent('imageChange', {
    imageId: currentImageId.value,
    imageName: currentImage.value?.name,
    thumbnail: currentImage.value?.thumbnail,
    url: currentImage.value?.url,
  });
}

const ensureAFrameInitialized = () => {
  // Ensure A-Frame global object exists
  if (!window.AFRAME) {
    console.error('A-Frame not loaded yet');
    return false;
  }

  // Check if A-Frame is initialized properly
  if (!window.AFRAME.scenes || !window.AFRAME.components) {
    window.AFRAME.scenes = window.AFRAME.scenes || [];
    window.AFRAME.components = window.AFRAME.components || {};
    window.AFRAME.systems = window.AFRAME.systems || {};
  }

  // Force register essential components if missing
  if (!window.AFRAME.components.position) {
    console.warn('Re-initializing essential A-Frame components');
    // This triggers re-registration of core components
    window.AFRAME.registerComponent('__core_init__', {
      init: function () {
        this.el.removeAttribute('__core_init__');
      },
    });
  }

  return true;
};

// Modify onMounted
onMounted(() => {
  if (!ensureAFrameInitialized()) {
    console.error('Failed to initialize A-Frame properly');
    return;
  }
  isMounted.value = true;
  registerComponents();
  setupOrientationListener(); // Set up listener for orientation updates

  setTimeout(() => {
    console.log('Running postMessage tests...');
    testPostMessage();
  }, 2000);

  // Send initial load message
  postMessageToParent('tourLoaded', {
    totalImages: Object.keys(props.tourData.images || {}).length,
    hasGroups: hasGroups.value,
    groups: props.tourData.groups,
  });

  if (hasGroups.value && props.tourData?.groups) {
    const firstGroupId = Object.keys(props.tourData.groups)[0];
    currentGroupId.value = firstGroupId;

    const groupImage = Object.entries(props.tourData.images)
      .find(([_, image]) => image.groupId === firstGroupId);

    if (groupImage) {
      switchImage(groupImage[0]);
    }
  } else if (props.tourData?.images) {
    const firstImage = Object.values(props.tourData.images)[0];
    if (firstImage) {
      switchImage(firstImage.id);
    }
  }
});

onBeforeUnmount(() => {
  const scene = document.querySelector('a-scene');
  if (scene) {
    scene.parentNode?.removeChild(scene);
  }
});

watch(currentGroupId, (newGroupId) => {
  if (newGroupId && props.tourData.groups[newGroupId]) {
    const groupImages = getImages();
    if (groupImages.length > 0 && !groupImages.find((img) => img.id === currentImageId.value)) {
      switchImage(groupImages[0].id);
    }
  }
});

watch(() => props.tourData, (newData) => {
  if (newData && isMounted.value) {
    const firstImageId = Object.keys(newData.images)[0];
    if (firstImageId) {
      switchImage(firstImageId);
    }
  }
}, { deep: true });
</script>

<template>
  <div class="vr-container relative w-full h-screen">
    <div
      v-if="currentImage"
      class="h-full w-full flex justify-center items-center text-white overflow-hidden relative"
    >
      <div
        v-if="!isTextureLoaded"
        class="absolute inset-0  bg-black"
        style="background-size: cover; background-position: center;"
        :style="{ backgroundImage: `url(${cdn(currentImage.thumbnail)})` }"
      />

      <a-scene
        id="VR_scene"
        ref="SceneTag"
        embedded
        loading-screen="enabled:false"
        vr-mode-ui="enabled:false"
        device-orientation-permission-ui="enabled: false"
        renderer="colorManagement:true;sortObjects:true;maxCanvasWidth:1920;maxCanvasHeight:1920;"
        initload
      >
        <!-- Mouse Cursor (cursor & raycaster for interactive el) -->
        <a-entity
          id="mouseCursor"
          cursor="rayOrigin: mouse;fuse:false;"
          raycaster="objects: [data-raycastable];"
        />

        <!-- Player (Camera) -->
        <a-entity
          id="player"
          camera="far:10000;near:0.5;fov:100"
        />

        <!-- A-sky -->
        <template v-if="currentImage">
          <a-sky
            :key="currentImageId"
            :src="cdn(currentImage.thumbnail)"
            @materialtextureloaded="handleTextureLoaded"
          />
        </template>

        <!-- Hotspots -->
        <a-entity v-if="showHotspots && currentImage?.links">
          <a-image
            v-for="(link, linkId) in currentImage.links"
            :key="linkId"
            :position="link.position"
            :data-destination="link.destination_img_id"
            :src="hotspotSvgPath"
            look-at="[camera]"
            data-raycastable
            hotspot-listener
            material=" transparent: true"
            scale="0.2 0.2"
            animation="property: scale; from: 0.2 0.2 0.2; to: 0.25 0.25 0.25; dur: 1000; dir: alternate; loop: true"
          >
            <a-entity
              class="hotspot-label"
              position="0 0.9 0"
              visible="false"
            >
              <a-plane
                width="3"
                height="0.7"
                material="color: white;"
                position="0 0 0"
              />
              <a-text
                :value="link.text"
                font="/assets/VRTour/KelsonSans.fnt"
                align="center"
                width="3.9"
                color="black"
                position="0 0 0"
                scale="1.9 1.9"
              />
            </a-entity>
          </a-image>
        </a-entity>
      </a-scene>
    </div>

    <!-- Navigation controls remain the same -->
    <div class="fixed w-full flex justify-center bottom-10 z-[3]">
      <NearByFloatingButton
        class="flex"
        :class="Store.isMobile ? 'w-full' : 'sm:w-[38%] md:w-[48%] lg:w-[60%] xl:[75%]'"
        :itemsList="getImages()"
        :sliderButton="true"
        :active="currentIndex"
        :leftButton="leftButton"
        :rightButton="rightButton"
        :objectIconKey="'icon'"
        :objectNameKey="'name'"
        @button-clicked="handleImageChange"
      />
    </div>

    <div
      v-if="hasGroups"
      class="fixed z-[6]"
      :class="[
        Store.isMobile ? 'right-3' : 'left-9',
        Store.isMobile ? 'bottom-[9rem]' : 'bottom-10'
      ]"
    >
      <DropDown
        v-if="currentDropdownValue"
        placement="top"
        :list="getGroupsList()"
        :defaultValue="currentDropdownValue"
        :align-to-end="Store.isMobile ? true : false"
        type="object"
        objectKey="name"
        @select-option="handleGroupChange"
      />
    </div>
  </div>
</template>

<style scoped>
.vr-container {
  position: relative;
  width: 100%;
  height: 100vh;
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

#VR_scene {
  width: 100%;
  height: 100%;
}
</style>
