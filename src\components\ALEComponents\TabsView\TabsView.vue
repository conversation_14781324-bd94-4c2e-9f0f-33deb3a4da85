<template>
  <div class="flex items-center flex-nowrap overflow-x-auto">
    <div
      v-if="navigator.exterior"
      class="flex justify-center items-center gap-3 px-5 py-3 w-full"
      :class="[( activeButton === 'exterior' ? 'bg-primary text-primaryText border-b-[3px] border-primary' : 'text-secondaryText bg-secondary border-b-[3px] border-transparent')]"
      @click="setActiveButton('exterior')"
    >
      <svg
        class="w-[1.25rem] h-[1.25rem]"
        viewBox="0 0 21 21"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.16797 5.49984C2.16797 3.92849 2.16797 3.14281 2.65613 2.65466C3.14428 2.1665 3.92995 2.1665 5.5013 2.1665C7.07265 2.1665 7.85833 2.1665 8.34648 2.65466C8.83464 3.14281 8.83464 3.92849 8.83464 5.49984V7.1665C8.83464 8.73785 8.83464 9.5235 8.34648 10.0117C7.85833 10.4998 7.07265 10.4998 5.5013 10.4998C3.92995 10.4998 3.14428 10.4998 2.65613 10.0117C2.16797 9.5235 2.16797 8.73785 2.16797 7.1665V5.49984Z"
          :stroke="activeButton === 'exterior' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          :fill="activeButton === 'exterior' ? 'var(--primaryText)' : 'none'"
          stroke-width="1.5"
        />
        <path
          d="M2.16797 16.3335C2.16797 15.5569 2.16797 15.1687 2.29484 14.8623C2.46399 14.454 2.78845 14.1295 3.19683 13.9603C3.50312 13.8335 3.8914 13.8335 4.66797 13.8335H6.33464C7.1112 13.8335 7.49949 13.8335 7.80578 13.9603C8.21415 14.1295 8.53861 14.454 8.70777 14.8623C8.83464 15.1687 8.83464 15.5569 8.83464 16.3335C8.83464 17.1101 8.83464 17.4983 8.70777 17.8047C8.53861 18.213 8.21415 18.5375 7.80578 18.7067C7.49949 18.8335 7.1112 18.8335 6.33464 18.8335H4.66797C3.8914 18.8335 3.50312 18.8335 3.19683 18.7067C2.78845 18.5375 2.46399 18.213 2.29484 17.8047C2.16797 17.4983 2.16797 17.1101 2.16797 16.3335Z"
          :stroke="activeButton === 'exterior' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          :fill="activeButton === 'exterior' ? 'var(--primaryText)' : 'none'"
          stroke-width="1.5"
        />
        <path
          d="M12.168 13.8333C12.168 12.262 12.168 11.4763 12.6561 10.9882C13.1443 10.5 13.93 10.5 15.5013 10.5C17.0726 10.5 17.8583 10.5 18.3465 10.9882C18.8346 11.4763 18.8346 12.262 18.8346 13.8333V15.5C18.8346 17.0713 18.8346 17.857 18.3465 18.3452C17.8583 18.8333 17.0726 18.8333 15.5013 18.8333C13.93 18.8333 13.1443 18.8333 12.6561 18.3452C12.168 17.857 12.168 17.0713 12.168 15.5V13.8333Z"
          :stroke="activeButton === 'exterior' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          :fill="activeButton === 'exterior' ? 'var(--primaryText)' : 'none'"
          stroke-width="1.5"
        />
        <path
          d="M12.168 4.6665C12.168 3.88994 12.168 3.50165 12.2948 3.19536C12.464 2.78699 12.7885 2.46253 13.1968 2.29337C13.5031 2.1665 13.8914 2.1665 14.668 2.1665H16.3346C17.1112 2.1665 17.4995 2.1665 17.8058 2.29337C18.2141 2.46253 18.5386 2.78699 18.7078 3.19536C18.8346 3.50165 18.8346 3.88994 18.8346 4.6665C18.8346 5.44307 18.8346 5.83135 18.7078 6.13765C18.5386 6.54602 18.2141 6.87048 17.8058 7.03964C17.4995 7.1665 17.1112 7.1665 16.3346 7.1665H14.668C13.8914 7.1665 13.5031 7.1665 13.1968 7.03964C12.7885 6.87048 12.464 6.54602 12.2948 6.13765C12.168 5.83135 12.168 5.44307 12.168 4.6665Z"
          :stroke="activeButton === 'exterior' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          :fill="activeButton === 'exterior' ? 'var(--primaryText)' : 'none'"
          stroke-width="1.5"
        />
      </svg>
      <p>
        Exterior
      </p>
    </div>
    <div
      v-if="navigator.unitplan"
      class="flex justify-center items-center px-5 py-3 gap-3 w-full"
      :class="[( activeButton === 'unitplan' ? 'bg-primary text-primaryText border-b-[3px] border-primary' : 'text-secondaryText bg-secondary border-b-[3px] border-transparent')]"
      @click="setActiveButton('unitplan')"
    >
      <svg
        class="w-[1.25rem] h-[1.25rem]"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          :stroke="activeButton === 'unitplan' ? 'none' : 'var(--primaryText)'"
          :fill="activeButton === 'unitplan' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          stroke-width="1.5"
          d="M9.03125 2.58326L9.03125 5.53955C9.03125 5.95376 9.36704 6.28955 9.78125 6.28955C10.1955 6.28955 10.5313 5.95376 10.5313 5.53955L10.5313 2.54722C11.2153 2.53955 11.9627 2.53955 12.7812 2.53955C17.4953 2.53955 19.8524 2.53955 21.3167 4.00402C22.327 5.01423 22.6403 6.44917 22.7375 8.78955L18.7813 8.78955C18.367 8.78955 18.0313 9.12534 18.0313 9.53955C18.0313 9.95376 18.367 10.2896 18.7813 10.2896L22.7736 10.2896C22.7813 10.9736 22.7813 11.721 22.7813 12.5396C22.7813 17.2536 22.7813 19.6107 21.3167 21.0751C20.5003 21.8916 19.4063 22.2529 17.7813 22.4127L13.5313 22.5205C13.5415 20.1821 15.4404 18.2896 17.7813 18.2896C18.1955 18.2896 18.5313 17.9538 18.5313 17.5396C18.5313 17.1253 18.1955 16.7896 17.7813 16.7896C14.6057 16.7896 12.0314 19.3638 12.0313 22.5393C11.4998 22.5388 11.0006 22.5371 10.5313 22.5319L10.5313 10.2896L14.7813 10.2896C15.1955 10.2896 15.5313 9.95376 15.5313 9.53955C15.5313 9.12534 15.1955 8.78955 14.7813 8.78955L9.78125 8.78955L2.82495 8.78955C2.92215 6.44917 3.2355 5.01423 4.24572 4.00402C5.25593 2.9938 6.69087 2.68045 9.03125 2.58326ZM2.78892 10.2896C2.78125 10.9736 2.78125 11.721 2.78125 12.5396C2.78125 17.2536 2.78125 19.6107 4.24572 21.0751C5.25593 22.0853 6.69087 22.3986 9.03125 22.4958L9.03125 10.2896L2.78892 10.2896Z"
        />
      </svg>
      <p>
        Unitplan
      </p>
    </div>
    <div
      v-if="navigator.interior"
      class="flex justify-center items-center px-5 py-3 gap-3 w-full"
      :class="[( activeButton === 'interior' ? 'bg-primary text-primaryText border-b-[3px] border-primary' : 'text-secondaryText bg-secondary border-b-[3px] border-transparent')]"
      @click="setActiveButton('interior')"
    >
      <svg
        class="w-[1.25rem] h-[1.25rem]"
        viewBox="0 0 21 21"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.35259 7.14022C6.365 5.17607 6.3712 4.194 6.98523 3.58767C7.59927 2.98134 8.58135 2.98754 10.5455 2.99995C12.5096 3.01235 13.4917 3.01855 14.0981 3.63259C14.7044 4.24663 14.6982 5.2287 14.6858 7.19284L14.6437 13.8594C14.6313 15.8235 14.6251 16.8056 14.011 17.412C13.397 18.0183 12.4149 18.0121 10.4508 17.9996C8.48662 17.9872 7.50454 17.981 6.89821 17.367C6.29188 16.7529 6.29809 15.7709 6.31049 13.8068L6.35259 7.14022Z"
          :stroke="activeButton === 'interior' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          :fill="activeButton === 'interior' ? 'var(--primaryText)' : 'none'"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M2.19141 6.28076C2.64666 6.37071 2.98113 6.52685 3.24462 6.80093C3.85084 7.43156 3.84447 8.44033 3.83173 10.4579C3.81898 12.4754 3.81261 13.4841 3.19848 14.1071C2.93155 14.3778 2.59513 14.5297 2.13878 14.6139"
          :stroke="activeButton === 'interior' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          :fill="activeButton === 'interior' ? 'var(--primaryText)' : 'none'"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M18.858 6.38601C18.4017 6.47021 18.0653 6.6221 17.7983 6.89284C17.1842 7.51576 17.1778 8.52453 17.1651 10.5421C17.1524 12.5596 17.146 13.5683 17.7522 14.199C18.0157 14.4731 18.3501 14.6292 18.8054 14.7192"
          :stroke="activeButton === 'interior' ? 'var(--primaryText)' : 'var(--secondaryText)'"
          :fill="activeButton === 'interior' ? 'var(--primaryText)' : 'none'"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <p>
        Interior
      </p>
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps, defineEmits, defineExpose, watch } from 'vue';
const emit = defineEmits(['handleSelection']);
const props = defineProps({
  navigator: {type: Array, required: true},
  defaultId: {type: String, required: true},
});
const activeButton = ref(props.defaultId);
function setActiveButton (button){
  activeButton.value = button;
  emit("handleSelection", button);
}
watch(() => props.defaultId, (newVal) => {
  activeButton.value = newVal;
});
defineExpose({setActiveButton});
</script>
