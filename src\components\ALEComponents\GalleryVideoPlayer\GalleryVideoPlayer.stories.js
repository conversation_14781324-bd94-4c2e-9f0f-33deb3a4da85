import GalleryVideoPlayer from './GalleryVideoPlayer.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/GalleryVideoPlayer',
  component: GalleryVideoPlayer,
  tags: ['autodocs'],
};

export const Primary = {
  args: {
    url: 'https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fof4KuR%2Fprojects%2F6613c37649c85c346626d2b0%2Fgallery%2F6645d56bb4932f59459b5d8a%2FSmaple_1.mp4?alt=media',
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/3ScwDHZPtk2FRiRLsSU2T2/ALE-V.2?type=design&node-id=1530-1572&mode=dev",
    },
  },
};
