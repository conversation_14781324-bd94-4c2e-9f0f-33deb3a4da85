import  UnitPlanCard from "./UnitPlanCard.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/ UnitPlanCard',
  component: UnitPlanCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    selectItem: true,
    allowEnterVr: true,
    name: '2BHK',
    bedrooms: 2,
    measurementType: 'Sqft',
    minMeasurement: 2000,
    maxMeasurement: 3000,
    favoritesStatus: false,
    thumbnail:
      "https://imgs.search.brave.com/xfJQm3xt_vN2tGjt5wzZRUF3eZ7KSes8jVZ9jU9ZhDo/rs:fit:860:0:0/g:ce/aHR0cHM6Ly9jZG4u/cGl4YWJheS5jb20v/cGhvdG8vMjAxNy8w/Ni8yNC8xMS8wNi9i/b2FyZC0yNDM3NDQ2/XzY0MC5qcGc",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/3ScwDHZPtk2FRiRLsSU2T2/ALE-V.2?type=design&node-id=2068-1614&mode=dev",
    },
  },

};
