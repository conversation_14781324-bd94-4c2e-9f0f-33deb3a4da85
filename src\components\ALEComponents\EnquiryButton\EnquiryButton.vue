<!-- <script setup>
import { FwbButton} from 'flowbite-vue';
import { ref, defineEmits, defineProps } from 'vue';

const fill =ref('white');
const isClicked = ref(false);

defineProps({
  message: {
    type: String,
    default: "",
  },

});

const emit = defineEmits(['handleEnquiry']);

const toggle = () => {
  isClicked.value = !isClicked.value;
  if (fill.value === 'white'){
    fill.value = 'black';
  }
  if (isClicked.value === false){
    fill.value = 'white';
  }
  emit('handleEnquiry');
};

</script>

<template>
  <div>
    <FwbButton
      :class="isClicked ? 'bg-white' : 'bg-black'"
      class="state focus:ring-0 outline-0 rounded-[50px] bg-black text-white hover:bg-white  hover:text-black  hover:outline-0"
      @click="toggle()"
    >
      <template #prefix>
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M22.0999 12C22.0999 16.6392 17.8015 20.4 12.4999 20.4C10.796 20.4071 9.11536 20.0038 7.6003 19.224L2.8999 20.4L4.5055 16.6524C3.4915 15.3204 2.8999 13.7208 2.8999 12C2.8999 7.36078 7.1983 3.59998 12.4999 3.59998C17.8015 3.59998 22.0999 7.36078 22.0999 12ZM8.8999 10.8H6.4999V13.2H8.8999V10.8ZM18.4999 10.8H16.0999V13.2H18.4999V10.8ZM11.2999 10.8H13.6999V13.2H11.2999V10.8Z"
            fill="white"
          />
        </svg>
      </template>
      {{ message }}
    </FwbButton>
  </div>
</template>

<style>
.state:hover svg path {
  fill: black;
}

.filler:hover svg path {
  stroke: black;
}
</style> -->

<template>
  <fwb-button
    class="flex flex-row gap-2 h-10"
    :class="Store.isMobile?'btn !bg-transparent focus:ring-0':'bg-blue-700'"
    @click="handleClick()"
  >
    <template #prefix>
      <div v-html="svg" />
    </template>
    {{ message }}
  </fwb-button>
</template>

<script setup>
import { FwbButton } from 'flowbite-vue';
import {  defineEmits, defineProps } from 'vue';
import { creationToolStore } from '../../../store';
const Store = creationToolStore();

defineProps({
  message: {
    type: String,
    default: "",
  },
  svg: {
    type: String,
    default: "",
  },
  isMobile: {
    type: Boolean,
    default: false,
  },

});

const emit = defineEmits(['handleEnquiry']);

const handleClick = () => {
  emit('handleEnquiry');
};
</script>

<style lang="scss">

.btn{
  background-color: none !important;
  padding: 0.25rem !important;
}

</style>
