<script setup>
import { ref } from 'vue';
import { creationToolStore } from '../store';
import { useRoute } from 'vue-router';
const route = useRoute();
const Store = creationToolStore();
const scenes = ref(false);
Store.getMasterScenes(route.params.organizationId).then((res) => {
  scenes.value=res;
  Object.values(res).forEach((scene) => {
    if (scene.sceneData.root){
      window.location.href='/'+route.params.organizationId+'/masterscene/'+scene.sceneData._id;
    }
  });
});
</script>
<template>
  <div>Loading</div>
</template>
<style scoped>

</style>
