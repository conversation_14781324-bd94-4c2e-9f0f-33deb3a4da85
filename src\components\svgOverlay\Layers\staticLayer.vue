<script setup>
import { defineProps, onMounted, ref, watch } from 'vue';
import { cdn, addSVGDeepZoom, setActiveElem } from '../../../helpers/helper';
import { creationToolStore } from '../../../store';
const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
}});
const Store = creationToolStore();
const staticSVGElem = ref({});
if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        obj.svgElement.style.cursor= 'pointer';
        staticSVGElem.value[item.layer_data.layer_id] = {'g': obj.svgElement, 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel};
        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.classList.add('!visible');
            obj.svgElement.classList.remove('!hidden');
          } else {
            obj.svgElement.classList.remove('!visible');
            obj.svgElement.classList.add('!hidden');
          }
        }
      });
    }
  });
}
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(staticSVGElem.value, Store.currentZoomlevel);
});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="(layer,index) in layerData"
    v-show="sceneType!=='deep_zoom'"
    :id="index"
    :key="index"
    :class="sceneType!=='deep_zoom'?[layer.layer.getAttribute('class') +' '+layer.layer_data.type+'opacity-100']:''"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
</template>
