<template>
  <div
    ref="dropDownList"
    class="dropdown"
  >
    <button
      class="dropdown-btn bg-secondary text-secondaryText "
      :class="Store.isMobile ? 'min-w-24' : 'min-w-[6rem] '"
      @click="() => isOpen = !isOpen"
    >
      <div class="flex gap-2 items-center">
        <span
          v-if="selectedCurrency"
          class="icon w-5 h-5 m-0 fill-primary"
        >
          <img
            :src="`https://flagcdn.com/48x36/${countryList[selectedCurrency].toLowerCase()}.png`"
            alt="flag"
          >
        </span>
        <span v-if="!Store.isMobile"> {{ selectedCurrency }}</span>
        <span v-else>{{ selectedCurrency }}</span>
      </div>
      <span :class="Store.isMobile ? '' : 'arrow'">
        <svg
          :class="[isOpen ? 'w-3.5 h-3.5 rotate-[-180] transition-transform duration-500' : 'w-3.5 h-3.5 rotate-180 transition-transform duration-500']"
          width="14"
          height="7"
          viewBox="0 0 14 7"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            class="fill-secondaryText"
            d="M7.0147 0C6.68056 6.62804e-05 6.36012 0.122775 6.12388 0.341136L1.08388 4.99826C0.963539 5.10566 0.86755 5.23414 0.801514 5.37618C0.735479 5.51823 0.70072 5.67101 0.699266 5.8256C0.697813 5.98019 0.729692 6.13351 0.793046 6.27659C0.8564 6.41968 0.949959 6.54967 1.06826 6.65899C1.18657 6.76831 1.32725 6.85476 1.4821 6.9133C1.63695 6.97184 1.80287 7.0013 1.97017 6.99996C2.13747 6.99861 2.30281 6.96649 2.45653 6.90548C2.61026 6.84446 2.74929 6.75576 2.86552 6.64456L7.0147 2.81058L11.1639 6.64456C11.4015 6.85664 11.7198 6.97399 12.0502 6.97134C12.3805 6.96869 12.6966 6.84624 12.9302 6.63038C13.1638 6.41451 13.2963 6.12249 13.2992 5.81722C13.302 5.51195 13.175 5.21785 12.9455 4.99826L7.90552 0.341136C7.66928 0.122775 7.34885 6.62804e-05 7.0147 0Z"
          />
        </svg>
      </span>
    </button>
    <ul
      v-if="isOpen"
      class="dropdown-menu w-[-webkit-fill-available] bg-secondary opacity-90 text-secondaryText"
    >
      <template
        v-for="currency in categoryList"
        :key="currency"
      >
        <li
          class="dropdown-item hover:bg-primary hover:text-secondary hover:opacity-80"
          @click="selectCurrency(currency)"
        >
          <span v-if="!Store.isMobile"> {{ currency }} </span>
          <span v-else> {{ currency }} </span>
        </li>
      </template>
    </ul>
  </div>
</template>
<script setup>

import { ref } from 'vue';
import { countryList } from '../../../config/masterdata';
import { onClickOutside } from '@vueuse/core';
import { getCookie, setCookie } from '../../../helpers/helper';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';

const selectedCurrency = ref('AED');
const route = useRoute();
const Store = creationToolStore();
const isOpen = ref(false);
const categoryList = ref(null);
const dropDownList = ref();

function selectCurrency (currency) {
  selectedCurrency.value = currency;
  isOpen.value = false;
  setCookie('selectedCurrency', currency, 1);
  Store.currencyData.currency = currency;
}

function setSelectedCurrency (response) {
  const cookieCurrency = getCookie('selectedCurrency');

  if (cookieCurrency) {
    selectedCurrency.value = cookieCurrency;
    Store.currencyData.currency = cookieCurrency;
  } else {
    selectedCurrency.value = response.baseCurrency;
    Store.currencyData.currency = response.baseCurrency;
  }

  Store.currencyData.exchangeRatio = response.exchangeRatio;

  categoryList.value = Object.values(response.exchangeRatio).map((item) => {
    return item.currency;
  });
}

if (Object.keys(Store.organizationDetails).length === 0) {
  Store.getOrganization(route.params.organizationId).then((res) => {
    setSelectedCurrency(res);
  });
} else {
  setSelectedCurrency(Store.organizationDetails);
}

onClickOutside(dropDownList, () => isOpen.value = false);
</script>

<style scoped>
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    gap: 1em;
    align-items: center;
}

.arrow {
    margin-left: auto;
}

.dropdown-menu {
    height: 250px;
    position: absolute;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1;
    list-style: none;
    padding: 0;
    overflow-y: auto;
}

.dropdown-item {
    padding: 10px;
    cursor: pointer;
  text-align: center;
}
</style>
