import axios from "axios";
import { cdn } from "./helper";
// Import { fetchUrl, getMaxAgeInMilliseconds, storeUrl } from './helper';
const authorization_key = import.meta.env.VITE_APP_AUTHORIZATION_TOKEN;
const shaToken = authorization_key ;
export async function GetRequestWithHeaders ({ url, organization }) {
  return new Promise((resolve, reject) => {
    var config = {
      method: "get",
      url,
      headers: {
        organization: organization,
        ...(shaToken && { Authorization: `Bearer ${shaToken}` }),
      },
    };

    axios(config)
      .then(function (response) {
        const authHeader = config.headers.Authorization;

        if (authHeader) {
          if (response.data.status === 1) {
            resolve(response.data);
          } else {
            reject(response.data);
          }
        } else {
          if (response.data.status === 1) {
            const decryptData = response.data;
            resolve(decryptData);
          } else {
            reject(response.data);
          }
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });
}
export async function PostRequestWithHeaders ({ url, body }) {
  return new Promise((resolve, reject) => {
    var config = {
      method: "post",
      url,
      headers: {
        organization: body.organization,
      },
      data: body,
    };

    axios(config)
      .then(function (response) {
        if (response.data.status === 1) {
          resolve(response.data);
        } else {
          reject(response);
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });
}
export function GetCurrencySymbol (currencyCode) {
  const currencySymbols = {
    USD: "$",
    EUR: "€",
    GBP: "£",
    INR: "₹",
  };
  const upperCaseCurrencyCode = currencyCode.toUpperCase();
  return currencySymbols[upperCaseCurrencyCode];
}
export async function GetRequest ({ url }) {
  return new Promise((resolve, reject) => {
    var config = {
      method: "get",
      url,
      headers: {
        ...(shaToken && { Authorization: `Bearer ${shaToken}` }),
      },
    };

    axios(config)
      .then(function (response) {
        const authHeader = config.headers.Authorization;

        if (authHeader) {
          if (response.data.status === 1) {
            resolve(response.data);
          } else {
            reject(response);
          }
        } else {
          if (response.data.status === 1) {
            const decryptData = response.data;
            resolve(decryptData);
          } else {
            reject(response);
          }
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });
}
export async function fetchImageURL (url) {
  try {
    const response = await fetch(url);
    // Check for HTTP errors
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
    }
    // Convert the response into a blob
    const blob = await response.blob();
    // Generate a URL for the blob
    const blobUrl = URL.createObjectURL(blob);
    return { url, blob, blobUrl };
  } catch (error) {
    return null;
  }
}
export async function fetchVideo (data, imgurl) {
  // Extract video_tag values
  const video_tags = Object.values(data)
    .map((item) => item.layer_data.video_tag)
    .filter((video_tag) => video_tag);
  video_tags.forEach((item) => {
    fetchImageURL(cdn(item));
  });
  const img = new Image();
  img.src = cdn(imgurl);
}
