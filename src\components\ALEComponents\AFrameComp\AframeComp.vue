<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { cdn, loadImageData } from '../../../helpers/helper';

const emit = defineEmits(['progressData', 'hotspotClick']);

const props = defineProps({
  sceneData: {
    type: Object,
    required: true,
  },
});

const highResDataLoaded = ref(false);
const SceneTag = ref();
const highResFile = computed(() => props.sceneData.file);
const thumbnail = computed(() => props.sceneData.thumbnail);
const isTextureLoaded = ref(false)

// Default hotspot icon
const defaultHotspotIcon = "https://storagecdn.propvr.tech/assets%2Fhotspot.svg";

const setConfigure = (obj) => {
  if (SceneTag.value){
    const camera = document.getElementById('player');
    const aSKy = SceneTag.value.querySelector('a-sky');
    const getRotationDegrees = obj?.rotation?.split(' '); // XYZ

    if (aSKy && camera){
      const src = obj.file; // get the image url and convert to cdn
      console.log("_inside if");
      const separator = src.includes("?") ? "&" : "?"; // get the separator
      const newSrc = `${src}${separator}t=${new Date().getTime()}`;       // Append a random query parameter to find this src is unique to avoid aframe optimize assets logic
      // set url (360 img)
      aSKy.setAttribute('src', newSrc); // url
      // set orientation
      if(getRotationDegrees){
        const lookControls = camera.components["look-controls"];
        if (lookControls) {
            // Convert degrees to radians using correct Three.js method
            const pitchRad = (Number(getRotationDegrees[0]) * Math.PI) / 180;
            const yawRad = (Number(getRotationDegrees[1]) * Math.PI) / 180;
            
            if (lookControls.pitchObject) {
              lookControls.pitchObject.rotation.x = pitchRad;
            }
            if (lookControls.yawObject) {
              lookControls.yawObject.rotation.y = yawRad;
            }
        }else{
          camera.addEventListener('componentinitialized', function (evt) {
            if (evt.detail.name === 'look-controls') {
            const pitchRad = (Number(getRotationDegrees[0]) * Math.PI) / 180;
                const yawRad = (Number(getRotationDegrees[1]) * Math.PI) / 180;
                
                if (lookControls.pitchObject) {
                  lookControls.pitchObject.rotation.x = pitchRad;
                }
                if (lookControls.yawObject) {
                  lookControls.yawObject.rotation.y = yawRad;
                }
                
                camera.removeEventListener('componentinitialized', handler);
            }
          });
        }
      }
    }
  }
};


const loadHighResImage = (imgURL) => {
  loadImageData(imgURL, (progress) => emit('progressData', 50+(progress/2)), () => {if(isTextureLoaded.value) emit('progressData', false)}).then((highRes) => {
    highResDataLoaded.value = highRes;
    setConfigure(props.sceneData);
  });
};

onMounted(() => {
  emit('progressData', 0);
  loadHighResImage(cdn(highResFile.value));
});

</script>

<template>
  <a-scene
   id="VR_scene"
   ref="SceneTag"
   class="aScene_Tour"
   embedded
   loading-screen="enabled:false"
   vr-mode-ui="enabled:false"
   device-orientation-permission-ui="enabled: false"
   renderer="colorManagement:true;sortObjects:true;maxCanvasWidth:1920;maxCanvasHeight:1920;"
  >
    <!-- Assets (Images and Hotspot Icons) -->
    <a-assets>
      <!-- Thumbnail image (low-res 360) -->
      <img
        id="thumbnailSky"
        :src="cdn(thumbnail)"
        crossorigin="anonymous"
        @load="()=>$emit('progressData',50)"
      />
      <!-- High-res image (loaded dynamically) -->
      <img
        v-if="highResDataLoaded"
        id="highResSky"
        :src="highResDataLoaded"
        crossorigin="anonymous"
      />
      <!-- Hotspot icon -->
      <img
        id="defaultHotspotIcon"
        crossorigin="anonymous"
        :src="defaultHotspotIcon"
        alt="defaultHotspotIcon"
      />
    </a-assets>

    <!-- Mouse Cursor (cursor & raycaster for interactive el) -->
    <a-entity id="mouseCursor" cursor="rayOrigin: mouse;fuse:false;" raycaster="objects: [data-raycastable];" ></a-entity>

    <a-entity id="rig" configure>
          <!-- Player (Camera) -->
        <a-entity id="player" camera="far:10000;near:0.5;fov:100" look-controls position="0 0 1e-5"></a-entity>
          <!-- A-Sky - starts with thumbnail, switches to high-res when loaded -->
          <a-sky
            :src="highResDataLoaded ? '#highResSky' : '#thumbnailSky'"
            @materialtextureloaded="()=>{console.log('Texture loaded:', highResDataLoaded ? 'high-res' : 'thumbnail'); isTextureLoaded = true; if(highResDataLoaded) emit('progressData', false)}"
          />
    </a-entity>

    <!-- Hotspots -->
    <a-entity v-if="sceneData?.links && Object.keys(sceneData?.links).length > 0">
          <a-image v-for="link, id in sceneData?.links" :key="id" @click="emit('hotspotClick', link)" look-at="[camera]" src="#defaultHotspotIcon" :position="link.position" opacity="0.5" scale="0.8 0.8" :data-Id="id" :data-isNewHotspot="false" data-raycastable :data-isDragging="false">
              <a-entity
                class="hotspot-label"
                position="0 0.9 0"
                visible="false"
              >
                  <a-plane
                    width="3"
                    height="0.7"
                    material="color: white;"
                    position="0 0 0"
                  />
                  <a-text
                    :value="link.text"
                    align="center"
                    width="6.9"
                    color="black"
                    position="0 0 0"
                    font="kelsonsans"
                    scale="1.9 1.9"
                  />
                </a-entity>
          </a-image>
    </a-entity>

  </a-scene>
</template>

<style scoped>
.hotspot {
  cursor: pointer;
}

.sample {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure hotspots are always visible */
.hotspot a-sphere {
  pointer-events: auto;
}
</style>
