<script setup>
import { FwbCard, FwbP, Fwb<PERSON>utton } from 'flowbite-vue';
import { thousandSeparator } from '../../../helpers/helper';
defineProps({
  title: {type: String, default: ""},
  name: {type: String, default: ""},
  minBedroom: {type: Number, default: 0},
  maxBedroom: {type: Number, default: 0},
  currency: {type: String, default: ""},
  price: {type: Number, default: 0},
  image: {type: String, default: ''},
});
defineEmits(['clickToExplore']);
</script>

<template>
  <div class="container w-fit h-fit">
    <fwb-card
      :img-alt="title"
      :img-src="image"
      variant="horizontal"
      class="h-fit card !border-gray-200 !flex-row !bg-gray-800"
    >
      <div class="p-4 flex flex-col gap-2">
        <div class="flex flex-col gap-1 w-48">
          <div class="text-white text-base font-bold leading-tight whitespace-nowrap overflow-hidden text-ellipsis w-full">
            <!-- Noteworthy technology acquisitions 2021 -->
            {{ title }}
          </div>
          <fwb-p class="font-normal !text-gray-400 mb-0 whitespace-nowrap overflow-hidden text-ellipsis w-full">
            <!-- Here are the biggest enterprise technology acquisitions of 2021 so far, in reverse chronological order. -->
            {{ name }}
          </fwb-p>
          <div class="inline-flex gap-4">
            <div class="flex gap-2 leading-[18px]">
              <span>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11.3335 6H9.3335M12.0002 8.66667H9.3335M12.0002 11.3333H9.3335"
                    stroke="#9CA3AF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                  />
                  <path
                    d="M4 14.6666V2.13331C4 1.61588 4.31399 1.33331 4.8 1.33331C5.91481 1.33331 6.47221 1.33331 6.9386 1.40718C9.50593 1.81381 11.5195 3.82735 11.9261 6.39471C12 6.86111 12 7.41851 12 8.53331V14.6666"
                    stroke="#9CA3AF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </span>
              <fwb-p class="text-center !text-gray-400 text-xs font-medium mb-0">
                Apartments
              </fwb-p>
            </div>
            <div class="flex gap-2 leading-[18px]">
              <span>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14.6668 11.6667H1.3335"
                    stroke="#9CA3AF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M14.6668 14V10.6667C14.6668 9.4096 14.6668 8.78107 14.2763 8.39053C13.8858 8 13.2572 8 12.0002 8H4.00016C2.74308 8 2.11454 8 1.72402 8.39053C1.3335 8.78107 1.3335 9.4096 1.3335 10.6667V14"
                    stroke="#9CA3AF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M7.33333 8V6.80893C7.33333 6.55515 7.2952 6.47027 7.0998 6.37025C6.693 6.16195 6.1991 6 5.66667 6C5.13423 6 4.64037 6.16195 4.2335 6.37025C4.03814 6.47027 4 6.55515 4 6.80893V8"
                    stroke="#9CA3AF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                  />
                  <path
                    d="M11.9998 8V6.80893C11.9998 6.55515 11.9617 6.47027 11.7663 6.37025C11.3595 6.16195 10.8656 6 10.3332 6C9.8007 6 9.30684 6.16195 8.90004 6.37025C8.70464 6.47027 8.6665 6.55515 8.6665 6.80893V8"
                    stroke="#9CA3AF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                  />
                  <path
                    d="M14 8V4.90705C14 4.44595 14 4.21541 13.8719 3.99769C13.7438 3.77997 13.5613 3.66727 13.1963 3.44189C11.7246 2.53319 9.93287 2 8 2C6.06711 2 4.27543 2.53319 2.80372 3.44189C2.43869 3.66727 2.25618 3.77997 2.12809 3.99769C2 4.21541 2 4.44595 2 4.90705V8"
                    stroke="#9CA3AF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                  />
                </svg>
              </span>
              <fwb-p class="text-center !text-gray-400 text-xs font-medium mb-0">
                <!-- 2 to 4 BR -->
                {{ minBedroom }} to {{ maxBedroom }} BR
              </fwb-p>
            </div>
          </div>
          <fwb-p class="!text-gray-400 text-xs font-medium mb-0 leading-normal whitespace-nowrap overflow-hidden text-ellipsis w-full">
            Starting <span class="text-white capitalize">{{ currency }}</span> <span class="text-white text-lg">{{ thousandSeparator(price) }}</span>
          </fwb-p>
        </div>
        <div class="w-full">
          <fwb-button
            color="default"
            class="[&>span]:flex [&>span]:gap-2 [&>span]:justify-center [&>span]:items-center [button] w-full bg-blue-600"
            @click="$emit('clickToExplore')"
          >
            <span class="text-sm font-medium leading-[21px]">Explore</span>
            <span>
              <svg
                width="13"
                height="13"
                viewBox="0 0 13 13"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_676_93)">
                  <path
                    d="M11.8871 6.21017L11.8872 6.21029C11.9391 6.26454 11.9768 6.33641 11.9923 6.41795C12.0078 6.49956 11.9997 6.58392 11.9698 6.65948L12.4348 6.84337L11.9694 6.66059C11.9498 6.71039 11.9217 6.75425 11.8876 6.79022C11.8874 6.79039 11.8873 6.79055 11.8871 6.79071L8.45919 10.3806L8.45914 10.3806L8.45326 10.387C8.41908 10.424 8.37948 10.4522 8.33744 10.4711C8.29546 10.49 8.25124 10.4996 8.20714 10.5C8.16304 10.5004 8.11873 10.4916 8.07657 10.4736C8.03434 10.4555 7.9944 10.4281 7.95973 10.3918C7.92501 10.3554 7.89637 10.3109 7.8766 10.2603C7.85681 10.2096 7.84659 10.1546 7.84706 10.0986C7.84752 10.0427 7.85865 9.98791 7.87923 9.93773C7.8998 9.88758 7.9291 9.84373 7.9643 9.80812L7.96435 9.80817L7.97035 9.80189L9.93591 7.74345L10.7431 6.89815H9.57429H1.3572C1.27011 6.89815 1.18164 6.86214 1.11269 6.78992C1.04298 6.71692 1 6.61318 1 6.50045C1 6.38771 1.04298 6.28398 1.11269 6.21098C1.18164 6.13876 1.27011 6.10274 1.3572 6.10274H9.57429H10.7431L9.93591 5.25744L7.9695 3.19811L7.96955 3.19806L7.96344 3.19188C7.92824 3.15628 7.89895 3.11242 7.87838 3.06227C7.85779 3.01209 7.84667 2.95732 7.8462 2.90136C7.84574 2.84539 7.85595 2.79037 7.87574 2.73972C7.89552 2.6891 7.92415 2.64459 7.95887 2.60823C7.99354 2.57192 8.03348 2.54453 8.07571 2.52644C8.11787 2.50837 8.16218 2.49961 8.20628 2.50001C8.25038 2.50041 8.29461 2.50998 8.33658 2.52887C8.37862 2.54778 8.41823 2.57598 8.4524 2.61303L8.45235 2.61309L8.45834 2.61936L11.8871 6.21017Z"
                    fill="white"
                    stroke="white"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_676_93">
                    <rect
                      width="12"
                      height="12"
                      fill="white"
                      transform="translate(0.5 0.5)"
                    />
                  </clipPath>
                </defs>
              </svg>
            </span>
          </fwb-button>
        </div>
      </div>
    </fwb-card>
  </div>
</template>

<style lang="scss">
.container{
    .card>img{
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        border-top-right-radius: 0px !important;
        border-bottom-right-radius: 0px !important;
        height:178px !important;
        width:138px !important;
    }
}
</style>
