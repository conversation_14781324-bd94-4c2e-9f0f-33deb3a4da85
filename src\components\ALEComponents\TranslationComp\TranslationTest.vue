<template>
  <span v-if="resultText">{{ resultText }}</span>
  <div
    v-else
    :class="[
      'skeleton-loader',
      {
        'skeleton-circle': circle,
        'skeleton-rectangular': variant === 'rectangular',
        'skeleton-text': variant === 'text',
      }
    ]"
    :style="dynamicStyles"
  />
</template>

<script setup>
import { computed, ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { fetchTranslationFromGoogleAPI } from "../../../helpers/translation.js";
import { creationToolStore } from "../../../store/index";
const props = defineProps({
  text: { type: String, default: "" },
  variant: {
    type: String,
    default: "text",
    validator: (value) => ["text", "rectangular", "circle"].includes(value),
  },
  circle: { type: Boolean, default: false },
  width: { type: [String, Number], default: null },
  height: { type: [String, Number], default: null },
});

const resultText = ref("");

const dynamicStyles = computed(() => {
  const styles = {};

  if (props.width) {
    styles.width = typeof props.width === "number" ? `${props.width}px` : props.width;
  }

  if (props.height) {
    styles.height = typeof props.height === "number" ? `${props.height}px` : props.height;
  } else if (props.circle) {
    styles.height = "40px";
    styles.width = "40px";
  } else {
    styles.height = "20px";
  }

  return styles;
});

const route = useRoute();
const organizationId = route.params?.organizationId;
const language = route.params?.language;

const Store = creationToolStore();

async function transComp (lowerCaseText) {
  const translatedText = await fetchTranslationFromGoogleAPI(
    lowerCaseText,
    language,
    organizationId,
  );

  resultText.value = translatedText;

  if (!Store.translationMap[lowerCaseText]) {
    Store.translationMap[lowerCaseText] = {};
  }

  Store.translationMap[lowerCaseText][language] = translatedText;
}

onMounted(async () => {
  if (!language || route.params?.language ==="en" ) {
    resultText.value = props.text;
    return;
  }
  const text = props.text.toString();
  const lowerCaseText = text.toLowerCase();

  if (
    Store.translationMap[lowerCaseText] &&
    Store.translationMap[lowerCaseText][language]
  ) {
    resultText.value = Store.translationMap[lowerCaseText][language];
    console.log("Translation from store:", resultText.value);
    return;
  }

  console.log("Translation from API");
  await transComp(lowerCaseText);
});
</script>

  <style scoped>
  .skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
  }

  .skeleton-circle {
    border-radius: 50%;
  }

  .skeleton-rectangular {
    border-radius: 4px;
  }

  .skeleton-text {
    border-radius: 4px;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
  </style>
