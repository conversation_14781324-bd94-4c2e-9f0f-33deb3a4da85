<script setup>
import { ref } from 'vue';
import { FwbDropdown, FwbButton } from 'flowbite-vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { creationToolStore } from '../../../store';
const Store = creationToolStore();
defineEmits(['selectOption']);
defineProps({
  text: { type: String, default: '' },
  list: { type: Object, default: () => ({}) },
  placement: { type: String, default: 'top' },
  defaultValue: { type: String, default: '' },
  type: { type: String, default: '' },
  objectKey: { type: String, default: '' },
  alignToEnd: { type: Boolean, default: false },
});

const dropdownboxRef = ref(null);

</script>

<template>
  <div class="dropdownContainer w-fit">
    <FwbDropdown
      :placement="placement"
      :align-to-end="alignToEnd"
    >
      <template #trigger>
        <span class="bg-secondary p-2 dropdown transition-transform  duration-300 rounded-md flex justify-center items-center text-secondaryText  ">
          <TranslationComp
            :key="defaultValue"
            :text="defaultValue ? (type.toLowerCase() === 'object' ? defaultValue[objectKey] : defaultValue) : text "
          />
          <svg
            data-v-2d4adfeb=""
            :class="!dropdownboxRef && 'rotate-180'"
            class="w-4 h-4 ml-2 transition-transform  duration-300 '"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          ><path
            data-v-2d4adfeb=""
            d="M19 9l-7 7-7-7"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          /></svg>
        </span>
      </template>
      <div
        ref="dropdownboxRef"
        class="hoverAction min-w-52 max-w-fit bg-secondary flex flex-col !rounded-lg overflow-auto !max-h-[18.5rem] visibleScroll"
        :class="Store.isLandscape ? '!max-h-[8.5rem] overflow-y-auto' :''"
      >
        <FwbButton
          v-if="text"
          disabled
          class="![&>span]:text-red-300 ![&>span]:text-sm !px-0 !font-normal bg-transparent leading-[21px] !w-fit !ring-0"
        >
          <TranslationComp
            :text="text "
          />
        </FwbButton>
        <FwbButton
          v-for="(item, index) in list"
          :key="index"
          :class="[type.toLowerCase() === 'object' ? item[objectKey] === defaultValue[objectKey] ? 'bg-primary text-primaryText' : 'bg-secondary text-secondaryText' : item === defaultValue ? 'bg-primary text-primaryText' : 'bg-secondary text-secondaryText',
                   index === 0 ? 'hover:rounded-t-lg hover:rounded-b-none' : list.length - 1 === index ? 'hover:rounded-b-lg hover:rounded-t-none' : 'rounded-none',
                   // Active states
                   item[objectKey] === defaultValue[objectKey] ? index === 0 ? 'rounded-t-lg rounded-b-none' : list.length - 1 === index ? 'rounded-b-lg rounded-t-none' : 'rounded-none' : 'rounded-none',
                   // Single-item list
                   list.length === 1 && item[objectKey] === defaultValue[objectKey] ? 'rounded-lg hover:rounded-lg' : '']"
          class="capitalize ![&>span]:text-sm !px-0 !font-normal leading-[21px] !ring-0 hover:bg-primary hover:text-primaryText "
          @click="$emit('selectOption', item)"
        >
          <TranslationComp
            :text="type.toLowerCase() === 'object' ? item[objectKey] : item "
          />
        </FwbButton>
      </div>
    </FwbDropdown>
  </div>
</template>

<style lang="scss">
.dropdownContainer {
  .dropdown>div:nth-child(2) {
    background: none !important;
    border-radius: 10px solid black !important;
  }
  .dropdown>div>button>div:nth-child(2) {
    margin-left: 0px !important;
  }
}
.hoverAction > button {
  display: flex;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  text-wrap: nowrap !important;
}

.dropdownContainer div button > div > svg{
  transition: transform linear 200ms ;
}
.dropdownContainer div >div:nth-child(2) {
  background: none !important;
}

.visibleScroll::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.visibleScroll::-webkit-scrollbar-track {
  background: var(--secondary);
}

.visibleScroll::-webkit-scrollbar-thumb {
  background: var(--primary);

}
</style>
