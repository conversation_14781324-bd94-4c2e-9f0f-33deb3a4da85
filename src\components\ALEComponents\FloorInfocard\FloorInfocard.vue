<template>
  <div class="w-fit relative bg-black backdrop-blur-lg rounded-lg p-2 flex gap-4 md:flex-col">
    <div>
      <div class="text-white font-normal pb-2">
        {{ floor }}th floor
      </div>
      <div class="flex flex-row gap-2 md:flex-col">
        <div class="flex ">
          <div class=" flex gap-1 text-white text-xs font-normal ">
            <svg
              class="h-4 w-4"
              viewBox="0 0 16 15"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_858_3975)">
                <path
                  d="M3.44444 1.5H7.27778V6.27778H2.5V2.44444C2.5 1.92614 2.92614 1.5 3.44444 1.5ZM14.5 2.44444V6.27778H9.72222V1.5H13.5556C14.0739 1.5 14.5 1.92614 14.5 2.44444ZM2.5 12.5556V8.72222H7.27778V13.5H3.44444C2.92614 13.5 2.5 13.0739 2.5 12.5556ZM13.5556 13.5H9.72222V8.72222H14.5V12.5556C14.5 13.0739 14.0739 13.5 13.5556 13.5Z"
                  stroke="#6B7280"
                /></g>
              <defs> <clipPath id="clip0_858_3975"> <rect
                width="15"
                height="15"
                fill="white"
                transform="translate(0.75)"
              />
              </clipPath></defs>
            </svg>
            {{ units }} Units
          </div>
        </div>
        <div class="flex gap-4">
          <div class="flex gap-1 text-white text-xs font-normal">
            <svg
              class="h-4 w-4"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.19144 3.91587C2.93114 4.15868 2.52329 4.1445 2.28048 3.8842L0.970282 2.47961C0.738772 2.23142 0.7394 1.84622 0.971719 1.59879L2.28192 0.203358C2.52557 -0.0561489 2.93347 -0.0689986 3.19297 0.174657C3.45248 0.418313 3.46533 0.826207 3.22167 1.08571L2.8985 1.42992H10.1841L9.86271 1.08538C9.6199 0.825083 9.63408 0.417233 9.89438 0.174425C10.1547 -0.0683822 10.5625 -0.0542016 10.8053 0.206099L12.1155 1.61069C12.347 1.85888 12.3464 2.24408 12.1141 2.49151L10.8039 3.88694C10.5602 4.14645 10.1524 4.1593 9.89285 3.91564C9.63334 3.67199 9.62049 3.26409 9.86414 3.00459L10.1849 2.66294H2.90411L3.22311 3.00492C3.46592 3.26522 3.45174 3.67307 3.19144 3.91587ZM10.3399 6.41113H2.27344C1.99729 6.41113 1.77344 6.63499 1.77344 6.91113V14.9626C1.77344 15.2387 1.9973 15.4626 2.27344 15.4626H10.3399C10.616 15.4626 10.8399 15.2387 10.8399 14.9626V6.91113C10.8399 6.63499 10.616 6.41113 10.3399 6.41113ZM2.27344 5.41113C1.44501 5.41113 0.773438 6.0827 0.773438 6.91113V14.9626C0.773438 15.791 1.44501 16.4626 2.27344 16.4626H10.3399C11.1683 16.4626 11.8399 15.791 11.8399 14.9626V6.91113C11.8399 6.08271 11.1683 5.41113 10.3399 5.41113H2.27344ZM13.3592 6.4818C13.0989 6.72461 13.0847 7.13246 13.3275 7.39276C13.5703 7.65306 13.9782 7.66724 14.2385 7.42444L14.5834 7.10271V14.763L14.2408 14.4413C13.9813 14.1976 13.5734 14.2105 13.3297 14.47C13.086 14.7295 13.0989 15.1374 13.3584 15.381L14.7538 16.6912C15.0013 16.9235 15.3865 16.9242 15.6347 16.6927L17.0392 15.3825C17.2995 15.1396 17.3137 14.7318 17.0709 14.4715C16.8281 14.2112 16.4203 14.197 16.16 14.4398L15.8164 14.7603L15.8164 7.10257L16.1577 7.423C16.4172 7.66666 16.8251 7.65381 17.0687 7.3943C17.3124 7.13479 17.2995 6.7269 17.04 6.48324L15.6446 5.17304C15.3972 4.94072 15.012 4.9401 14.7638 5.17161L13.3592 6.4818Z"
                fill="#6B7280"
              />
            </svg>{{ area }}
          </div>
        </div>
        <div class="flex gap-4 ">
          <div class="flex gap-1 text-white text-xs font-normal">
            <svg
              class="h-4 w-4"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.625 5.0625H1.6875V3.375C1.6875 3.22582 1.62824 3.08274 1.52275 2.97725C1.41726 2.87176 1.27418 2.8125 1.125 2.8125C0.975816 2.8125 0.832742 2.87176 0.727252 2.97725C0.621763 3.08274 0.5625 3.22582 0.5625 3.375V14.625C0.5625 14.7742 0.621763 14.9173 0.727252 15.0227C0.832742 15.1282 0.975816 15.1875 1.125 15.1875C1.27418 15.1875 1.41726 15.1282 1.52275 15.0227C1.62824 14.9173 1.6875 14.7742 1.6875 14.625V12.375H16.3125V14.625C16.3125 14.7742 16.3718 14.9173 16.4773 15.0227C16.5827 15.1282 16.7258 15.1875 16.875 15.1875C17.0242 15.1875 17.1673 15.1282 17.2727 15.0227C17.3782 14.9173 17.4375 14.7742 17.4375 14.625V7.875C17.4375 7.12908 17.1412 6.41371 16.6137 5.88626C16.0863 5.35882 15.3709 5.0625 14.625 5.0625ZM1.6875 6.1875H6.75V11.25H1.6875V6.1875ZM7.875 11.25V6.1875H14.625C15.0726 6.1875 15.5018 6.36529 15.8182 6.68176C16.1347 6.99822 16.3125 7.42745 16.3125 7.875V11.25H7.875Z"
                fill="#6B7280"
              />
            </svg>{{ minBedrooms }} - {{ maxBedrooms }}
          </div>
        </div>
      </div>
    </div>
    <button
      class="bg-white text-black text-base rounded-md font-medium capitalize px-6 py-2 md:p-2"
      @click="moveToScene"
    >
      Enter
    </button>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";
defineProps({
  floor: {type: Number, default: 0},
  units: {type: Number, default: 0},
  area: {type: String, default: ""},
  minBedrooms: {type: Number, default: 0},
  maxBedrooms: {type: Number, default: 0},
});
const emit = defineEmits(['moveToScene']);

function moveToScene () {
  emit("moveToScene");
}
</script>
