<template>
  <div>
    <div id="panorama" />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { defineProps } from 'vue';

const props = defineProps({
  type: { type: String, default: "multires" },
  basePath: { type: String, default: "" },
  path: { type: String, default: "" },
  fallbackPath: { type: String, default: "" },
  extension: { type: String, default: "jpg" },
  tileResolution: { type: Number, default: 512 },
  maxLevel: { type: Number, default: 6 },
  cubeResolution: { type: Number, default: 8432 },
});
const renderer = ref(null);

onMounted(() => {
  const config = {
    autoLoad: true,
    compass: false,
    default: {
      sceneFadeDuration: 1000,
    },
    type: props.type,
    autoRotate: -2,
    hfov: 250,
  };

  if (props.type === "multires") {
    config.multiRes = {
      basepath: props.basepath,
      path: props.path,
      fallbackPath: props.fallbackPath,
      extension: props.extension,
      tileResolution: props.tileResolution,
      maxLevel: props.maxLevel,
      cubeResolution: props.cubeResolution,
    };
  } else {
    config.panorama = props.panorama;
  }

  renderer.value = window.pannellum.viewer('panorama', config);
});

onUnmounted(() => {
  if (renderer.value) {
    renderer.value.destroy();
  }
});
</script>

  <style scoped>
  * {
    margin: 0;
    padding: 0;
  }
  #panorama {
    width: 100%;
    height: 100vh;
  }
  </style>
