import UnitPlanSideBar from './UnitPlanSideBar.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/UnitPlanSideBar',
  component: UnitPlanSideBar,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {

  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-2405&mode=design&t=UdrCcVTv7farhkwN-4",
    },
  },
};
