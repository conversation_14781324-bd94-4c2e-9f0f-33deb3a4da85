<script setup>
import { creationToolStore } from "../../store";
import { ref, onMounted, defineEmits, defineProps, watch } from "vue";
import IndexLayer from "../svgOverlay/IndexLayer.vue";
import router from "../../router/index";
import {cdn, getCookie, getSplashCookie} from "../../helpers/helper";
import {useRoute} from 'vue-router';
import InfoModal from '../ALEComponents/InfoModal/InfoModal.vue';
import CloudTransition from '../ALEComponents/CloudTransition/CloudTransition.vue';
import NewAleSideBar from '../ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import ToggleButton from '../ALEComponents/ToggleButton/ToggleButton.vue';
import OpenSeadragon from "openseadragon";
import CircularLoader from "../ALEComponents/CircularLoader/CircularLoader.vue";

const emit = defineEmits(['removeLoader']);
const props = defineProps({'sceneId': {type: String, default: ""}, 'scenes': {
  type: Object,
  default () {
    return {};
  },
}, 'organizationId': {type: String, default: ""}, projectId: {type: String, default: undefined}});
const store = creationToolStore();
const showLoader = ref(true);
const layerData = ref(null);
const sceneData = ref(null);
const viewerId = ref("maincompview"); // Sea dragon id
const layerTypes = ref([]);
const closeFullscreenModal = ref(false);
const route = useRoute();
var checkAutoExit = ref(false);
const splashCookie = getSplashCookie();
if (!store.organization_thumbnail) {
  store.getOrganization(route.params.organizationId);
}

store.activeOptionId = store.SceneData[route.params.sceneId].sceneData.category;

// SceneData
sceneData.value = props.scenes[props.sceneId].sceneData;
const processSvg = (svg, groupByTypes) => {
  Object.values(svg.layers).forEach((item) => {
    const { g, ...layerData } = item;
    if (!groupByTypes[item.type]) {
      groupByTypes[item.type] = {};
      groupByTypes[item.type][item.layer_id]={"layer_data": layerData, "layer": g};
    } else {
      groupByTypes[item.type][item.layer_id]={"layer_data": layerData, "layer": g};
    }
    if (!layerTypes.value.some((type) => type === item.type)){
      layerTypes.value.push(item.type);
    }
  });
  return groupByTypes;
};
async function createViewer (viewerId, tileSources, minZoomLevel, maxZoomLevel) {
  return new Promise((resolve) => {
    window.viewer = OpenSeadragon({
      id: viewerId,
      showZoomControl: false,
      showHomeControl: false,
      showFullPageControl: false,
      showRotationControl: false,
      showSequenceControl: false,
      tileSources: [tileSources], // TileSources
      preserveViewport: true,
      visibilityRatio: 1.0,
      constrainDuringPan: true,
      homeFillsViewer: true,
      minZoomLevel: minZoomLevel,
    });
    window.viewer.gestureSettingsMouse.clickToZoom = false;
    if (window.viewer.iOSDevice) {
      window.viewer.viewport.maxZoomLevel = maxZoomLevel;
      window.viewer.zoomPerScroll = 1.5; // Increase zoom speed
      window.viewer.springStiffness = 20; // Increase panning speed
      window.viewer.animationTime = 0.5; // Reduce animation time for faster panning,
    } else {
      window.viewer.viewport.maxZoomLevel = maxZoomLevel;
      window.viewer.zoomPerScroll = 1.5; // Increase zoom speed
      window.viewer.springStiffness = 10; // Increase panning speed
      window.viewer.animationTime = 0.5; // Reduce animation time for faster panning,
    }

    // Zoom Handler
    window.viewer.addHandler("zoom", (e) => {
      store.currentZoomlevel = e.zoom;
      if (screen.width < 600){
        if (e.zoom < window.viewer.viewport.minZoomLevel) {
          window.viewer.viewport.zoomTo(window.viewer.viewport.minZoomLevel);
        }
      }
    });
    // After Ready
    window.viewer.addHandler("open", function () {
      resolve();
    });
  });
}
// OnMounted
onMounted(() => {
  // Create a new viewer
  createViewer(viewerId.value, cdn(sceneData.value.background.high_resolution), sceneData.value.minZoomLevel?sceneData.value.minZoomLevel:1, sceneData.value.maxZoomLevel?sceneData.value.maxZoomLevel:10)
    .then(() => {
      window.viewer.viewport.minZoomLevel =
          window.viewer.viewport.getHomeZoom(); // Set the minZoomLevel
      // Layers
      const groupByTypes = {};
      Object.values(props.scenes[props.sceneId].svgData).forEach((svg) => {
        layerData.value = processSvg(svg, groupByTypes);
      });
      if (sceneData.value){
        setTimeout(() => {
          if (!store.showInfoModal.includes(sceneData.value._id)) {
            store.showInfoModal.push(sceneData.value._id);
          }
        }, 4000);
      }
      showLoader.value = false;
      emit("removeLoader");
    });
});
watch(props, () => {
  sceneData.value = props.scenes[props.sceneId].sceneData;
  store.destroyViewer();
  showLoader.value=true;
  layerData.value=null;
  // Document.getElementsByClassName('openseadragon-canvas')[0].remove()
  // Create a new viewer
  createViewer(viewerId.value, cdn(sceneData.value.background.high_resolution), sceneData.value.minZoomLevel?sceneData.value.minZoomLevel:1, sceneData.value.maxZoomLevel?sceneData.value.maxZoomLevel:10)
    .then(() => {
      window.viewer.viewport.minZoomLevel =
          window.viewer.viewport.getHomeZoom(); // Set the minZoomLevel
      // Layers
      const groupByTypes = {};
      Object.values(props.scenes[props.sceneId].svgData).forEach((svg) => {
        layerData.value = processSvg(svg, groupByTypes);
      });
      showLoader.value = false;
      emit("removeLoader");
    });
});
function setFullScreenCookie () {
  if (!getCookie('fullscreen')) {
    const expiryTime = new Date(Date.now() + (30 * 60000));
    document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
    closeFullscreenModal.value = true;
  }
}
const onClickButton = (id) => {
  if (id === 'radius'){
    router.push({ name: route.fullPath.includes('masterscene') ? 'masterScene' : 'projectScene', query: { ...route.query, [id]: route.query[id] === undefined ? true : route.query[id] === 'false' ? true : false } });
    if (route.query[id] === 'true') {
      sceneData.value.type==='deep_zoom'?document.querySelector('.openseadragon-canvas canvas').classList.remove("opacity-50"):document.getElementById("svg_highres_image")?document.getElementById("svg_highres_image").classList.remove("opacity-50"):document.getElementById("svg_lowres_image")?.classList.remove("opacity-50");
    } else {
      sceneData.value.type==='deep_zoom'?document.querySelector('.openseadragon-canvas canvas').classList.add("opacity-50"):document.getElementById("svg_highres_image")?document.getElementById("svg_highres_image").classList.add("opacity-50"):document.getElementById("svg_lowres_image")?.classList.add("opacity-50");
    }
  }
  if (id === 'fullscreen') {
    setFullScreenCookie();
    if (!document.fullscreenElement &&
      !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.mozRequestFullScreen) {
        document.documentElement.mozRequestFullScreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
      } else if (document.documentElement.msRequestFullscreen) {
        document.documentElement.msRequestFullscreen();
      }
      checkAutoExit.value = false;
      store.isFullScreen = !store.isFullScreen;
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
      checkAutoExit.value = true;
      store.isFullScreen = !store.isFullScreen;
    }

  }
};
function moveToLocation (id, type){
  if (type==='masterscene'){
    router.push({name: 'masterScene', params: {sceneId: id} });
  } else if (type==='earth'){
    router.push({name: 'globeScene'});
  } else if (type==='projectscene') {
    router.push({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    router.push({ name: 'unitplansviewer'});
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push( { name: 'galleryview'} );
  }
}
</script>

<template>
  <!-- Loader  -->
  <!-- <LoaderComponent
    v-if="showLoader && (getCookie('splashLoader') || (store.projectCardData[route.params.projectId]?store.projectCardData[route.params.projectId].projectSettings.ale && (!store.projectCardData[route.params.projectId].projectSettings.ale.welcome_video || !store.projectCardData[route.params.projectId].projectSettings.ale.welcome_thumbnail):false))"
    class="z-10"
    :logo="store.logo === 'light' ? store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo : store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo_dark"
  /> -->
  <CircularLoader
    v-if="showLoader && (splashCookie[route.params.projectId] || (store.projectCardData[route.params.projectId]?store.projectCardData[route.params.projectId].projectSettings.ale && (!store.projectCardData[route.params.projectId].projectSettings.ale.welcome_video || !store.projectCardData[route.params.projectId].projectSettings.ale.welcome_thumbnail):false))"
  />
  <InfoModal
    v-if="!store.showInfoModal.includes(sceneData._id) && sceneData.root"
    :info-text="sceneData.info_text"
    :info-icon="store.logo === 'light' ? 'https://storagecdn.propvr.tech/CreationtoolAssets%2Fsiteassets%2Fclick_white.svg?alt=media' : 'https://storagecdn.propvr.tech/CreationtoolAssets%2Fsiteassets%2Fblack_click.svg?alt=media'"
  />
  <CloudTransition
    v-if="sceneData.clouds &&!showLoader"
    class="z-[1]"
  />
  <div
    v-if="!showLoader"
    class="absolute top-[50%] z-[4]"
  >
    <NewAleSideBar
      :sidebarList="store.sidebarOptions[route.params.projectId]"
      @select-option="moveToLocation"
    />
  </div>
  <!-- Viewer -->
  <div
    :id="viewerId"
    class="w-full h-screen"
  />
  <!-- Index Layer -->
  <IndexLayer
    v-if="layerData"
    :sceneType="sceneData.type"
    :layerData="layerData"
  />
  <div
    v-if="!store.isMobile && !showLoader && !store.isLandscape"
    class="fixed bottom-10 right-5 z-10"
  >
    <ToggleButton
      v-if="layerData && layerData['radius']"
      :label="'Radius'"
      :active="route.query['radius'] === 'true'"
      @on-toggle="onClickButton(&quot;radius&quot;)"
    />
  </div>
  <portal-target
    v-for="name in layerTypes"
    :key="name"
    :name="name"
  >
    <slot />
  </portal-target>
</template>

<style>
.openseadragon {
  height: auto;
  width: auto;
  pointer-events: none;
}
.openseadragon g{
  pointer-events: all;
}

.yRnIS3.sold,
.yRnIS3.onhold,
.yRnIS3.reserved {
  display: none !important;
}

</style>
