
<template>
  <div
    class="relative flex justify-center items-center  h-auto "
    @click="handleClick"
  >
    <div class="gradient  bg-white absolute h-full  w-full  rounded-2xl " />
    <img
      class="bg-cover object-cover rounded-2xl w-full h-36 "
      :src="link"
      alt=""
    >
    <div class="absolute w-fit h-fit flex bg-white items-center justify-center rounded-[8px]  px-4 py-1 whitespace-nowrap cursor-pointer ">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
      >

        <path
          d="M2.5 14.1648L6.85832 9.08007C7.19395 8.68851 7.80106 8.69272 8.13122 9.08891L11.0825 12.6304C11.3962 13.0069 11.9654 13.0327 12.3119 12.6862L13.5083 11.4898C13.8595 11.1386 14.438 11.1706 14.7483 11.5585L17.5 14.9981"
          stroke="#262626"
          stroke-width="1.4"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M16.6667 2.5H3.33333C2.8731 2.5 2.5 2.8731 2.5 3.33333V16.6667C2.5 17.1269 2.8731 17.5 3.33333 17.5H16.6667C17.1269 17.5 17.5 17.1269 17.5 16.6667V3.33333C17.5 2.8731 17.1269 2.5 16.6667 2.5Z"
          stroke="#262626"
          stroke-width="1.4"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M11.6667 6.66668C11.6667 6.20644 12.0398 5.83334 12.5001 5.83334C12.9603 5.83334 13.3334 6.20644 13.3334 6.66668C13.3334 7.12691 12.9603 7.50001 12.5001 7.50001C12.0398 7.50001 11.6667 7.12691 11.6667 6.66668Z"
          stroke="#262626"
          stroke-width="1.4"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>

      <span class="block text-xs ml-1">{{ title }}</span>
    </div>
    <div class="absolute text-[white] bottom-2 text-base font-medium">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
const props = defineProps({
  title: {type: String, default: ""},
  link: {type: String, default: ""},
});
const emits = defineEmits(['imageClick']);

const handleClick = () => {
  emits('imageClick', props.data);
};
</script>

<style scoped>
.gradient {
    background: linear-gradient(180deg, rgba(2, 0, 36, 0) 0%, rgba(9, 9, 121, 0.781) 77%, rgb(0, 81, 255) 100%);
}
</style>
