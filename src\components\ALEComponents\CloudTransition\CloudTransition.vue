<script setup>
import { ref, onMounted } from 'vue';

const topClouds = ref([]);
const bottomClouds = ref([]);

const randomXPosition = () => {
  return Math.random() * window.innerWidth;
};

const initializeClouds = () => {
  topClouds.value = [
    { x: 0, y: -180, imageSrc: '/assets/Icons/cloud/cloud_1.png', opacity: 0.75 },
    // { x: 0, y: -140, imageSrc: '/assets/Icons/cloud/cloud_2.png', opacity: 1 },
    { x: window.innerWidth-500, y: -120, imageSrc: '/assets/Icons/cloud/cloud_3.png', opacity: 0.75},
    // { x: 1000, y: -100, imageSrc: '/assets/Icons/cloud/cloud_4.png', opacity: 0.65 },
  ];

  bottomClouds.value = [
    { x: randomXPosition(), y: -120, imageSrc: '/assets/Icons/cloud/cloud_1.png', opacity: 0.5 },
    { x: randomXPosition(), y: -80, imageSrc: '/assets/Icons/cloud/cloud_2.png', opacity: 0.55 },
    // { x: randomXPosition(), y: 80, imageSrc: '/assets/Icons/cloud/cloud_3.png', opacity: 0.65 },
    // { x: randomXPosition(), y: 120, imageSrc: '/assets/Icons/cloud/cloud_4.png', opacity: 0.6 },
  ];
};

const updateClouds = () => {
  topClouds.value.forEach((cloud) => {
    cloud.x += 1;
    if (cloud.x > window.innerWidth) {
      cloud.x = -100;
      // Cloud.opacity = 1;
    } else {
      // Cloud.opacity -= 0.001;
    }
  });

  bottomClouds.value.forEach((cloud) => {
    cloud.x += 1;
    if (cloud.x > window.innerWidth) {
      cloud.x = -100;
      // Cloud.opacity = 1;
    } else {
      // Cloud.opacity -= 0.001;
    }
  });
};

onMounted(() => {
  initializeClouds();
  setInterval(() => {
    updateClouds();
  }, 150);
});
</script>

<template>
  <div class="absolute left-0 top-0 w-full h-full overflow-hidden  pointer-events-none">
    <div
      class="absolute left-0 h-[300px] w-full right-0 top-clouds"
      style="top:0px"
    >
      <div
        v-for="(cloud, index) in topClouds"
        :key="index"
        class="absolute left-0 w-full h-[300px] w-full top-0 topclouds"
      >
        <img
          :src="cloud.imageSrc"
          alt=""
          style="visibility: visible;"
        >
      </div>
    </div>

    <div
      class="absolute left-0 h-[300px] w-full right-0 bottom-clouds"
      style="bottom:0px"
    >
      <div
        v-for="(cloud, index) in bottomClouds"
        :key="index"
        class="absolute left-0 w-full h-[300px] w-full top-0 bottomclouds"
      >
        <img
          :src="cloud.imageSrc"
          alt=""
          style="visibility: visible;"
        >
      </div>
    </div>
  </div>
</template>

<style>
@keyframes topCloud {
  0%
    {
      transform:translateX(calc(-675px));
      opacity:0;
    }
  10%
    {
      opacity:0.7;
    }
  20%
    {
      opacity:0.2
    }
  30%
    {
      opacity:0
    }

  100%
    {
      transform:translateX(calc(100vw));
      opacity: 0;
    }
}

@keyframes BottomCloud{
  0%
    {
      transform:translateX(calc(-975px));
      opacity:1;
    }
  20%
    {
      opacity:0.5
    }
  40%
    {
      opacity:0
    }

  100%
    {
      transform:translateX(calc(100vw));
      opacity:0
    }
}
.bottomclouds {
  animation-duration: 108s;
  animation-delay: 5s;
  transform:translateX(calc(-975px));
  animation-name: BottomCloud;
  transition-timing-function: linear;
}
.topclouds {
  animation-duration: 120s;
  transform:translateX(calc(-975px));
  animation-name: topCloud;
  transition-timing-function: linear;
}
</style>
