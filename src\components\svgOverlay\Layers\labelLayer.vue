<script setup>
import { ref, defineProps, watch, onMounted } from 'vue';
import router from '../../../router';
import { useRoute } from 'vue-router';
import { creationToolStore } from '../../../store/index';
import { LandmarkCategories } from '../../../config/masterdata';
const route = useRoute();
import LandmarkLabel from '../../ALEComponents/LandmarkLabel/LandmarkLabel.vue';
import NearByFloatingButton from '../../ALEComponents/NearByFloatingButton/NearByFloatingButton.vue';
import {addSVGDeepZoom, cdn} from '../../../helpers/helper';
const props = defineProps({
  layerData: {
    type: Object,
    default () {
      return {};
    },
  },
  sceneType: {
    type: String,
    default: '',
  },
});
const Store = creationToolStore();
const isLandmarkNotPresent = ref(false);
const show_modal = ref(false), labelSVGElem= ref({});
const position=ref(false);
const positionDirection=ref(null);
const fillValue = ref(null), title = ref();
const currentCategory = ref(route.query.category);
const activeCategory = ref(route.query.category || 'Show All');
watch(() => {
  return route.query.category;
}, (newPath) => {
  currentCategory.value = newPath;
  Object.values(props.layerData).forEach((layer) => {
    if (props.sceneType==='deep_zoom'){
      if (layer.layer_data.category===currentCategory.value || currentCategory.value===undefined){
        labelSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!opacity-100');
        labelSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!hidden');
      } else {
        labelSVGElem.value[layer.layer_data.layer_id].g.classList.add('!opacity-100');
        labelSVGElem.value[layer.layer_data.layer_id].g.classList.add('!hidden');
      }
    }
  });
});

const namedColors = {
  "red": [255, 0, 0],
  "green": [0, 128, 0],
  "yellow": [255, 255, 0],
  "indigo": [75, 0, 130],
  "purple": [255, 0, 0],
  "pink": [255, 192, 203],
  "dark": [0, 0, 0],
};

const getDistance = (color1, color2) => {
  return Math.sqrt(
    Math.pow(color1[0] - color2[0], 2) +
    Math.pow(color1[1] - color2[1], 2) +
    Math.pow(color1[2] - color2[2], 2),
  );
};

const parseRgbString = (rgbString) => {
  const result = rgbString.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
  if (result) {
    const r = parseInt(result[1]);
    const g = parseInt(result[2]);
    const b = parseInt(result[3]);
    return [r, g, b];
  }
  return null;
};

const closestNamedColor = (rgbString) => {
  const rgb = parseRgbString(rgbString);
  if (!rgb) {
    return null;
  }

  let closestColor = "";
  let minDistance = Infinity;

  for (const [name, colorRgb] of Object.entries(namedColors)) {
    const distance = getDistance(rgb, colorRgb);
    if (distance < minDistance) {
      minDistance = distance;
      closestColor = name;
    }
  }

  return closestColor;
};

function placeModal (layer_id, event, name) {
  title.value = name;
  const svgGroup = document.getElementById(layer_id);
  const rect = svgGroup.getBoundingClientRect();
  let xPos = rect.x + rect.width + 10;
  const yPos = rect.y - 3;
  let posDirection= "right";
  if (xPos + 200 > window.innerWidth) {
    xPos = rect.x ;
    posDirection= "left";
  }
  positionDirection.value = posDirection;
  position.value = { x: xPos, y: yPos };
  show_modal.value = true;

  const clickedElement = event.target;
  // If (clickedElement.tagName === 'path') {
  const paths = props.sceneType!=='deep_zoom'?Array.from(clickedElement.parentNode.getElementsByTagName('path')):Array.from(clickedElement.getElementsByTagName('path'));
  const largestPath = paths.reduce((largest, current) => {
    const largestArea = largest.getBoundingClientRect().width * largest.getBoundingClientRect().height;
    const currentArea = current.getBoundingClientRect().width * current.getBoundingClientRect().height;
    return largestArea > currentArea ? largest : current;
  });
  const computedStyle = window.getComputedStyle(largestPath);
  const closestColor = closestNamedColor(computedStyle.getPropertyValue('fill'));
  fillValue.value = closestColor;
}

function closeModal (e){
  if (e.target.getAttribute("clickaway")==="true"){
    show_modal.value=false;
  }
}
// Watch for category changes to update label visibility
watch(() => currentCategory.value, () => {
  if (props.sceneType === 'deep_zoom') {
    Object.values(props.layerData).forEach((layer) => {
      if (labelSVGElem.value[layer.layer_data.layer_id]) {
        if (layer.layer_data.category === currentCategory.value || currentCategory.value === undefined) {
          labelSVGElem.value[layer.layer_data.layer_id].g.classList.remove('!hidden');
        } else {
          labelSVGElem.value[layer.layer_data.layer_id].g.classList.add('!hidden');
        }
      }
    });
  }
});

const setActiveButton = (value) => {
  if (value === null || value.value === 'Show All') {

    if (route.path.includes('masterscene')) {
      activeCategory.value = value.value;
      router.push({ name: 'masterScene', query: { radius: route.query.radius } });
    } else {
      activeCategory.value = value.value;
      router.push({ name: 'projectScene', query: { radius: route.query.radius } });
    }
  } else {
    if (route.path.includes('masterscene')) {
      activeCategory.value = value.value;
      router.push({ name: 'masterScene', query: { category: value.value, radius: route.query.radius } });
    } else {
      activeCategory.value = value.value;
      router.push({ name: 'projectScene', query: { category: value.value, radius: route.query.radius } });
    }
  }
};

let filteredCategory;
let hasLandmark = false;
if (Store.SceneData) {
  let svgData;
  for (const key in Store.SceneData) {
    if (Store.SceneData[key].sceneData._id === route.params.sceneId) {
      svgData = JSON.parse(JSON.stringify(Store.SceneData[key].svgData));
    }
  }
  const landmarkCategoires = [];
  for (const key in svgData) {
    for (const a in svgData[key].layers) {
      const layer = svgData[key].layers[a];
      if (layer.type === 'landmark') {
        hasLandmark = true;
      }
      // Here Landmarks are excluded
      if (
        Object.hasOwn(layer, 'category') &&
        layer.type === 'label' &&
        layer.category !== 'landmark'
      ) {
        if (!landmarkCategoires.includes(layer.category)) {
          landmarkCategoires.push(layer.category);
        }
      }
    }
  }

  const categoryNames = landmarkCategoires;

  // Step 2: Filter the category object
  filteredCategory = Object.keys(LandmarkCategories.category)
    .filter((key) => categoryNames.includes(key) || key === 'All')
    .reduce((obj, key) => {
      let displayName = LandmarkCategories.category[key].value;
      if (key === 'health') {
        displayName = 'healthcare';
      }
      obj[key] = {
        ...LandmarkCategories.category[key],
        displayName: displayName,
      };
      return obj;
    }, {});

  isLandmarkNotPresent.value = !hasLandmark;
}

if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        obj.svgElement.style.cursor= 'pointer';
        labelSVGElem.value[item.layer_data.layer_id] = {'g': obj.svgElement};
        if (item.layer_data.category===currentCategory.value || currentCategory.value===undefined){
          obj.svgElement.classList.remove('!hidden');
        } else {
          obj.svgElement.classList.add('!hidden');
        }
        obj.svgElement.addEventListener("mouseenter", (e) =>
          placeModal(item.layer_data.layer_id, e, item.layer_data.title),
        );
        obj.svgElement.addEventListener("mouseleave", (e) =>
          closeModal(e),
        );
      });
    }
  });
}
</script>

<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="layer in layerData"
    v-show="props.sceneType!=='deep_zoom' && (layer.layer_data.category==currentCategory || currentCategory==undefined)"
    :id="layer.layer_data.layer_id"
    :key="layer"
    clickaway="true"
    :class="props.sceneType!=='deep_zoom'?[layer.layer.getAttribute('class') + ' ' + layer.layer_data.type + 'hover:fill-pill-fill cursor-pointer']:''"
    @mouseover="(event) => {placeModal(layer.layer_data.layer_id,event,layer.layer_data.title); }"
    @mouseleave="closeModal"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
  <portal to="label">
    <div
      v-if="show_modal"
      :class="[positionDirection === 'left' ? 'flex justify-end' : '', 'flex justify-start']"
      class="rect_overlay slide-animation fixed top-0 left-0 w-fit h-fit z-20 "
      :style="{ 'top': position.y + 'px', 'left': position.x + 'px' }"
    >
      <div class="absolute text-white w-max  z-1">
        <LandmarkLabel
          class="-top-1"
          :name="title"
          :color="fillValue"
        />
      </div>
    </div>
  </portal>
  <div
    v-if="Object.values(filteredCategory).length > 2 && isLandmarkNotPresent"
    class="fixed w-full flex justify-center z-10"
    :class="Store.isMobile ? 'bottom-24' : Store.isLandscape? 'bottom-3': 'bottom-16'"
  >
    <NearByFloatingButton
      v-if="(!Store.isMobile && !Store.isLandscape) || (Store.isMobile && !show_modal) || (Store.isLandscape && !show_modal)"
      class="flex"
      :class="Store.isMobile || Store.isLandscape ? 'w-full' : 'sm:w-[49%] md:w-[59%] lg:w-[69%] xl:[70%]'"
      :itemsList="Object.values(filteredCategory)"
      :active="activeCategory ? Object.values(filteredCategory).findIndex((e) => e.value === activeCategory) : 0"
      :sliderButton="false"
      :objectNameKey="`displayName`"
      :objectIconKey="`icon`"
      @button-clicked="setActiveButton"
    />
  </div>
</template>
<style scoped>
.slide-animation
{
  pointer-events: none;
  animation: slide 0.3s ease-out both;
}

@keyframes slide {
  0%
  {
    transform:translateX(-40px);
    opacity:0

  }
  100%
  {
    transform: translateX(0px);
    opacity:1
  }
}
</style>
