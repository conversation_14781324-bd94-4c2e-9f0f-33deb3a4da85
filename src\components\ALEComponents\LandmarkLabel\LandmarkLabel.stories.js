import LandmarkLabel from './LandmarkLabel.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/LandmarkLabel',
  component: LandmarkLabel,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    name: 'Holy Apostles Church',
    color: 'red',
  },
  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1127-9961&mode=dev",
    },
  },
};
