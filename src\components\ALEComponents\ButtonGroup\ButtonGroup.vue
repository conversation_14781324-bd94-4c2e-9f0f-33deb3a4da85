<template>
  <div
    class="fixed sm:bottom-8 bottom-[80%] left-0  sm:overflow-hidden overflow-auto sm:w-fit w-full sm:left-1/2 sm:-translate-x-1/2 h-fit   p-0  flex flex-col items-center gap-1"
  >
    <!-- Floor Selector -->
    <div
      ref="floorList"
      class="w-full  mx-2"
      :class="Store.isMobile ? 'flex justify-center items-center':''"
    >
      <div class="flex flex-row space-x-2 h-fit items-center">
        <button
          v-for="(floor_id, index) in floorNames"
          :key="floor_id"
          :class="[
            ' px-4 py-2 rounded-full text-center ',
            {
              'border-gray-600': index === 0,
              'border-gray-600': index === floorNames.length - 1,
              '!bg-primary text-primaryText duration-500': activeButton === floor_id,
              'bg-primaryText text-secondaryText sm:bg-opacity-60 backdrop-blur-[20px] hover:bg-primary hover:text-primaryText': activeButton !== floor_id,
              'hover:bg-primary  hover:text-primaryText': activeButton === floor_id,
              'cursor-default': activeButton === floor_id,
              '': index !== 0 && index !== floorNames.length - 1
            }
          ]"
          @click="setActiveButton(floor_id)"
        >
          <p class="sm:relative  sm:z-[150] sm:left-auto left-4 sm:bottom-auto bottom-[1.4rem] text-nowrap whitespace-nowrap">
            <TranslationComp
              :text="floorData[floor_id].name"
            />
          </p>
        </button>
      </div>
    </div>

    <!-- Down Arrow -->
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { creationToolStore } from '../../../store';
const Store = creationToolStore();
const emit = defineEmits(['setCurrentFloor']);
const props = defineProps({
  floorNames: {
    type: Array,
    required: true,
  },
  floorData: {
    type: Object,
    required: true,
  },
  activeTab: {
    type: String,
    required: true,
  },
});

const activeButton = ref(props.activeTab);

const setActiveButton = (button) => {
  activeButton.value = button;
  emit('setCurrentFloor', button);
};
watch(() => props.activeTab, (newVal) => {
  activeButton.value = newVal;
});

const floorList = ref(null);

</script>

<style scoped>
::-webkit-scrollbar {
  display: none;
}

.floor-selector {
  @apply sm:min-h-fit sm:h-[10rem] h-fit overflow-y-scroll;
}

.first-letter {
  @apply sm:text-[0px] text-[1.2rem] font-semibold;
}

.first-letter::first-letter {
  @apply font-medium sm:text-base;
}
</style>
