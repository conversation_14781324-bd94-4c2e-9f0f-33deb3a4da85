<script setup>
import { onMounted, ref} from 'vue';
import { creationToolStore } from '../../../store';
import OverflowSlider from '../OverflowSlider/OverflowSlider.vue';

const Store = creationToolStore();

const props = defineProps({
  projectsData: {
    type: Array,
    required: true,
  },
});
const slidePerView = ref(5);
const bulletsPerView = ref(3);
onMounted(() => {
  if (window.innerWidth <= 1024){
    slidePerView.value = 3;
  }
  if (window.innerWidth <= 768){
    slidePerView.value = 2;
    bulletsPerView.value = 2;
  }
});
const projectList = ref();

onMounted(() => {
  projectList.value = ( Object.keys(props.projectsData).length % slidePerView.value);
  console.log("projectList", projectList.value);
});

const handleNext = () => {
  const swiperContainer = document.getElementById('overflowFloorSwitchSliderRef').swiper;
  swiperContainer.slideNext();
};
const handlePrev = () => {
  const swiperContainer = document.getElementById('overflowFloorSwitchSliderRef').swiper;
  swiperContainer.slidePrev();
};

const injectPaginationBulletStyles = [
  `
      background-color: white !important;
      width:13px !important;
      height:13px !important;
      min-width:13px !important;
      min-height:13px !important;
  `,
];
const injectPaginationDivStyles = [
  ` 
    position:absolute !important;
    bottom:0 !important;
    right:0 !important;
    left:0 !imporatant;
    // width:105px !important;
    height:6% !important;
    margin-left:auto !important;
    margin-right:auto !important;
    // max-width:7.5% !important;
    max-height:6% !important;
  `,
];
const injectSwipperStyles = [
  `
  position:relative !important;
  left:0 !important;
  `,
];

</script>

<template>
  <div
    v-if="!Store.isMobile"
    class=" flex flex-col h-full w-full justify-between items-center mx-auto  "
  >
    <div class="w-full h-full relative scrollbar justify-center ">
      <OverflowSlider
        id="overflowFloorSwitchSliderRef"
        direction="horizontal"
        :slides-per-view="slidePerView"
        :slides-per-group="slidePerView"
        space-between="20"
        pagination="true"
        dynamic-bullets="true"
        :inline-wrapper-styles="injectSwipperStyles"
        :inline-indicators-styles="injectPaginationBulletStyles"
        :inline-indicators-wrapper-styles="injectPaginationDivStyles"
        :bullets-per-view="bulletsPerView"
        class="w-full h-full relative "
        @swiper-slide-change="handleSwiperSlideChange"
      >
        <template
          #options
        >
          <swiper-slide
            v-for="item in props.projectsData"
            :key="item"
            class="w-auto min-w-[250px] h-[80%] rounded-[10px] cursor-pointer flex  items-end overflow-hidden"
          >
            <router-link :to="`${item.organization_id}/projectscene/${item._id}`">
              <div
                class="absolute inset-0 bg-cover bg-center"
              >
                <img
                  :src="`${item.project_thumbnail}`"
                  alt="Background"
                  class="w-full h-full object-cover opacity-60"
                >
              </div>
              <div class="h-[10vh] min-h-[10vh] w-[85%] mx-auto flex flex-col gap-2 absolute bottom-0 left-3">
                <div class="w-full min-h-[44%] ">
                  <p class="!text-white text-xl lg:text-lg font-normal relative capitalize">
                    {{ item.name }}
                  </p>
                  <!-- <p class="!text-white text-xs font-light text-pretty relative overflow-hidden break-words">
                    Business Bay
                  </p> -->
                </div>
                <div class="flex items-center w-full gap-4 relative right-[5px]">
                  <div class="flex flex-row w-15 items-center gap-1 h-full">
                    <div class="w-auto h-4">
                      <svg
                        width="16"
                        height="12"
                        viewBox="0 0 16 17"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g id="building-01">
                          <path
                            id="Subtract"
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M3.3999 1.46V16.5H12.9999V13.2503H9.8001C9.38589 13.2503 9.0501 12.9145 9.0501 12.5003C9.0501 12.0861 9.38589 11.7503 9.8001 11.7503H12.9999V10.0503H9.8001C9.38589 10.0503 9.0501 9.71452 9.0501 9.3003C9.0501 8.88609 9.38589 8.5503 9.8001 8.5503H12.9995C12.9977 7.59227 12.9858 7.04436 12.9113 6.57367C12.9026 6.51888 12.8933 6.4643 12.8834 6.40993C12.7655 6.66966 12.5039 6.8503 12.2001 6.8503H9.8001C9.38589 6.8503 9.0501 6.51452 9.0501 6.1003C9.0501 5.68609 9.38589 5.3503 9.8001 5.3503H12.2001C12.3725 5.3503 12.5313 5.40846 12.6579 5.50621C11.84 2.94611 9.64112 1.01865 6.92622 0.58864C6.36656 0.5 5.69767 0.5 4.3599 0.5C3.77669 0.5 3.3999 0.83908 3.3999 1.46Z"
                            fill="white"
                          />
                        </g>
                      </svg>
                    </div>
                    <span class="relative top=[-10px]">
                      <p class="!text-white text-xs font-light text-pretty relative overflow-hidden break-words capitalize">
                        {{ item.units?.type ? item.units.type : 'Apartments' }}
                      </p>
                    </span>
                  </div>
                  <div class="flex flex-row w-15 items-center gap-1 h-full">
                    <div class="w-auto h-5">
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g id="bed-double">
                          <path
                            id="Vector"
                            d="M21.9995 17.9999H1.99951"
                            stroke="#D1D5DB"
                            stroke-width="2.25"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector_2"
                            d="M21.9995 21.5V16.5C21.9995 14.6144 21.9995 13.6716 21.4137 13.0858C20.8279 12.5 19.8851 12.5 17.9995 12.5H5.99951C4.11389 12.5 3.17108 12.5 2.5853 13.0858C1.99951 13.6716 1.99951 14.6144 1.99951 16.5V21.5"
                            stroke="white"
                            stroke-width="2.25"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector_3"
                            d="M11 12.5V10.7134C11 10.3327 10.9428 10.2054 10.6497 10.0554C10.0395 9.74292 9.29865 9.5 8.5 9.5C7.70135 9.5 6.96055 9.74292 6.35025 10.0554C6.05721 10.2054 6 10.3327 6 10.7134V12.5"
                            stroke="white"
                            stroke-width="2.25"
                            stroke-linecap="round"
                          />
                          <path
                            id="Vector_4"
                            d="M18.0005 12.5V10.7134C18.0005 10.3327 17.9433 10.2054 17.6502 10.0554C17.04 9.74292 16.2992 9.5 15.5005 9.5C14.7018 9.5 13.961 9.74292 13.3508 10.0554C13.0577 10.2054 13.0005 10.3327 13.0005 10.7134V12.5"
                            stroke="white"
                            stroke-width="2.25"
                            stroke-linecap="round"
                          />
                          <path
                            id="Vector_5"
                            d="M21 12.5V7.86057C21 7.16893 21 6.82311 20.8079 6.49653C20.6157 6.16995 20.342 6.00091 19.7944 5.66283C17.5869 4.29978 14.8993 3.5 12 3.5C9.10067 3.5 6.41314 4.29978 4.20558 5.66283C3.65804 6.00091 3.38427 6.16995 3.19213 6.49653C3 6.82311 3 7.16893 3 7.86057V12.5"
                            stroke="white"
                            stroke-width="2.25"
                            stroke-linecap="round"
                          />
                        </g>
                      </svg>
                    </div>
                    <span class="relative top=[-10px]">
                      <p class="!text-white text-xs font-light text-pretty relative overflow-hidden break-words">
                        2 to 4 BR
                      </p>
                    </span>
                  </div>
                </div>
              </div>
            </router-link>
          </swiper-slide>
        </template>
      </OverflowSlider>
      <div
        v-if="Object.keys(props.projectsData).length > 2"
        class="w-full h-[8%] flex justify-center absolute bottom-[5px] z-10"
      >
        <div className="h-full xl:w-[18%] lg:w-[25%] sm:w-[30%] justify-between items-center gap-5 inline-flex">
          <div
            className="w-10 h-10 bg-[#1f2a37] rounded-full justify-center items-center flex cursor-pointer"
            @click="handlePrev"
          >
            <div className="w-5 h-5 relative">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16 5C16 5 9.00001 10.1554 9 12C8.99999 13.8447 16 19 16 19"
                  stroke="white"
                  stroke-width="4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>

          <div
            className="w-10 h-10 bg-[#1f2a37] rounded-full justify-center items-center flex cursor-pointer"
            @click="handleNext"
          >
            <div className="w-5 h-5 relative">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8 5C8 5 15 10.1554 15 12C15 13.8447 8 19 8 19"
                  stroke="white"
                  stroke-width="4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    v-if="Store.isMobile"
    class="flex h-full w-full flex-col gap-3"
  >
    <div
      v-for="item in projectsData"
      :key="item"
      class=" min-h-[150px] h-[200px] w-full rounded-[10px] cursor-pointer flex flex-col items-end relative overflow-hidden my-auto "
      @click="emitProjectId(item._id,item.organization_id)"
    >
      <div
        class="absolute inset-0 bg-cover bg-center opacity-60"
        :style="{ backgroundImage: `url(${item.project_thumbnail})` }"
      />
      <div class="min-h-[10vh] w-full mx-auto flex flex-col gap-2  absolute bottom-[-5px]">
        <div class="w-[90%] min-h-[44%] mx-auto mt-[5px] flex flex-col">
          <div>
            <p class="!text-white text-base font-normal relative capitalize">
              {{ item.name }}
            </p>
          </div>

          <div class="flex w-full h-5 gap-4">
            <div class="flex flex-row w-15 items-center gap-1 h-full">
              <div class="w-auto">
                <svg
                  width="16"
                  height="14"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="map-pin">
                    <path
                      id="Vector"
                      d="M8.00028 3.0505e-06C6.78483 -0.00113653 5.59223 0.317031 4.55168 0.920036C3.51113 1.52304 2.66232 2.38789 2.09719 3.42088C1.53207 4.45388 1.27218 5.61563 1.34569 6.78028C1.4192 7.94493 1.8233 9.06806 2.51422 10.028C2.54187 10.0802 2.57541 10.1293 2.61422 10.1744L2.71423 10.2912C2.80756 10.4072 2.9034 10.5192 2.98591 10.6112L7.35692 15.7104C7.4353 15.8012 7.53361 15.8743 7.64484 15.9243C7.75606 15.9744 7.87744 16.0002 8.00028 16C8.12347 16.0001 8.24513 15.9739 8.35652 15.9234C8.46791 15.8729 8.56626 15.7993 8.64448 15.708L12.888 10.744C13.0599 10.5666 13.2207 10.3796 13.3697 10.184L13.4755 10.06C13.5158 10.013 13.55 9.96141 13.5772 9.9064C14.2358 8.94108 14.6102 7.82278 14.6609 6.66989C14.7116 5.51699 14.4366 4.37239 13.8651 3.35723C13.2936 2.34206 12.4469 1.49411 11.4144 0.903113C10.382 0.312119 9.20233 7.25263e-05 8.00028 3.0505e-06ZM8.00028 4C8.49476 4 8.97813 4.14076 9.38927 4.40447C9.80041 4.66819 10.1209 5.04302 10.3101 5.48156C10.4993 5.9201 10.5488 6.40266 10.4523 6.86822C10.3559 7.33377 10.1178 7.76141 9.76812 8.09706C9.41848 8.4327 8.973 8.66128 8.48803 8.75389C8.00306 8.84649 7.50037 8.79896 7.04353 8.61731C6.5867 8.43566 6.19624 8.12805 5.92152 7.73337C5.64681 7.33869 5.50018 6.87468 5.50018 6.4C5.50018 5.76348 5.76358 5.15303 6.23244 4.70295C6.7013 4.25286 7.33721 4 8.00028 4Z"
                      fill="#9CA3AF"
                    />
                  </g>
                </svg>
              </div>
              <span class="relative top=[-10px]">
                <p class="!text-gray-400  text-sm font-normal text-pretty relative overflow-hidden break-words">
                  {{ item.country }}
                </p>
              </span>
            </div>
            <div class="flex flex-row w-15 items-center gap-1 h-full">
              <div class="w-auto">
                <svg
                  width="16"
                  height="17"
                  viewBox="0 0 16 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="building-01">
                    <path
                      id="Subtract"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M3.3999 1.46V16.5H12.9999V13.2503H9.8001C9.38589 13.2503 9.0501 12.9145 9.0501 12.5003C9.0501 12.0861 9.38589 11.7503 9.8001 11.7503H12.9999V10.0503H9.8001C9.38589 10.0503 9.0501 9.71452 9.0501 9.3003C9.0501 8.88609 9.38589 8.5503 9.8001 8.5503H12.9995C12.9977 7.59227 12.9858 7.04436 12.9113 6.57367C12.9026 6.51888 12.8933 6.4643 12.8834 6.40993C12.7655 6.66966 12.5039 6.8503 12.2001 6.8503H9.8001C9.38589 6.8503 9.0501 6.51452 9.0501 6.1003C9.0501 5.68609 9.38589 5.3503 9.8001 5.3503H12.2001C12.3725 5.3503 12.5313 5.40846 12.6579 5.50621C11.84 2.94611 9.64112 1.01865 6.92622 0.58864C6.36656 0.5 5.69767 0.5 4.3599 0.5C3.77669 0.5 3.3999 0.83908 3.3999 1.46Z"
                      fill="#9CA3AF"
                    />
                  </g>
                </svg>
              </div>
              <span class="relative top=[-10px]">
                <p class="!text-gray-400 text-sm font-light text-pretty relative overflow-hidden break-words capitalize">
                  {{ item.units?.type ? item.units.type : 'Apartments' }}
                </p>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
::-webkit-scrollbar {
    display: none;
}
.slot svg path{
  stroke:white ;
}

</style>
