<template>
  <span
    v-if="props.price && selectedCurrency"
    :class="Store.isMobile ? 'ml-1' : ''"
  >
    {{ props.unitCurrencyType && orgBaseCurrency && props.unitCurrencyType.toUpperCase() !== orgBaseCurrency
      ? convertMisMatchCurrency(price)
      : thousandSeparator(convertCurrency(price)) }}
  </span>
  <span
    v-else
    :class="Store.isMobile ? 'ml-1' : ''"
  >
    {{ thousandSeparator(props.price) }}
  </span>
</template>

<script setup>
import { ref, defineProps, watch } from 'vue';
import { creationToolStore } from '../../../store/index';
import { getCookie, thousandSeparator } from '../../../helpers/helper';

const props = defineProps({
  price: {
    type: Number,
    required: true,
  },
  unitCurrencyType: {
    type: String,
    required: true,
  },
});

const Store = creationToolStore();
const selectedCurrency = ref(Store.currencyData.currency || getCookie('selectedCurrency'));
const selectedExchangeRatio = ref(Store.currencyData.exchangeRatio);
const orgBaseCurrency = ref(Store.organizationDetails.baseCurrency);
const projectBaseCurrency = ref(props.unitCurrencyType);

watch(() => Store.currencyData, (newCurrency) => {
  selectedCurrency.value = newCurrency.currency;
},
{ deep: true });

function convertCurrency (price) {
  if (selectedCurrency.value && price && (selectedExchangeRatio.value.length > 0)) {
    if (selectedCurrency.value === Store.organizationDetails.baseCurrency) {
      return price * 1;
    }
    const rateData = selectedExchangeRatio.value.find((item) => item.currency === selectedCurrency.value);
    return rateData ? Number(price * rateData.rate).toFixed(0) : null;
  }
  return null;
}

function crossConvertCurrency (amount, fromCurrency, toCurrency, rates = selectedExchangeRatio.value) {
  const fromRateObj = rates.find((rate) => rate.currency.toUpperCase() === fromCurrency.toUpperCase());
  const toRateObj = rates.find((rate) => rate.currency.toUpperCase() === toCurrency.toUpperCase());

  if (!fromRateObj || !toRateObj) {
    console.warn(`Missing rate for ${!fromRateObj ? fromCurrency : toCurrency}`);
    return amount;
  }

  const amountInOrgBase = amount * fromRateObj.rate;

  const convertedAmount = amountInOrgBase / toRateObj.rate;

  return convertedAmount;
}

function convertMisMatchCurrency (price) {
  try {
    const projectBase = projectBaseCurrency?.value?.toUpperCase();
    const orgBase = Store.organizationDetails?.baseCurrency?.toUpperCase();

    if (!price || !projectBase || !orgBase || !selectedExchangeRatio?.value) {
      return thousandSeparator(price); // fallback
    }

    const fromOrgBase = crossConvertCurrency(price, projectBase, orgBase);

    const toTargetCurrency = convertCurrency(fromOrgBase);

    return thousandSeparator(toTargetCurrency);
  } catch (err) {
    console.error("Currency conversion error:", err);
    return thousandSeparator(price); // fallback
  }
}

</script>
