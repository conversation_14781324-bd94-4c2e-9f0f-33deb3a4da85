<script setup>
import { defineProps, ref, watch, defineEmits } from 'vue';
import svgOverlay from '../components/svgOverlay/svgOverlay.vue';
import { creationToolStore } from '../store/index';
import { useRoute } from 'vue-router';
// Import { fetchImageURL } from '../helpers/API';
// Import { cdn } from '../helpers/helper';
const emit = defineEmits(['removeLoader', 'removeOverlay', 'loadothers']);
const route = useRoute();
const Store = creationToolStore();
const props = defineProps({'sceneId': {type: String, default: ""}, 'scenes': {
  type: Object,
  default () {
    return {};
  },
}, 'organizationId': {type: String, default: ""}, projectId: {type: String, default: undefined}});
const vh = window.innerHeight * 0.01;
document.documentElement.style.setProperty('--vh', `${vh}px`);
const containerRef = ref(null);
const scene_data = ref(props.scenes[props.sceneId]);
Store.activeOptionId = Store.SceneData[route.params.sceneId].sceneData.category;
watch(props, (newprops) => {
  scene_data.value = props.scenes[newprops.sceneId];
  Store.$patch({breadCrumb: []});
  Store.activeOptionId = Store.SceneData[newprops.sceneId].sceneData.category;
});
if (Object.values(Store.amenityCardData).length===0) {
  Store.getListofAmenities(route.params.projectId, route.params.organizationId);
}
if (!Store.galleryList) {
  Store.getGallery(route.params.organizationId, route.params.projectId);
}
</script>

<template>
  <svgOverlay
    ref="containerRef"
    style="height:100%;width:100%"
    :data="scene_data"
    bucket-u-r-l="propvr-in-31420.appspot.com"
    replace-u-r-l="storagecdn.propvr.ai"
    @remove-loader="emit('removeLoader')"
    @loadothers="emit('loadothers')"
    @remove-overlay="(val)=>emit('removeOverlay',val)"
  />
</template>

<style>
svg {
  height: 100%;
  width: 100%;
}

.svgLayer{
  /* width: auto; */
  height: 100%;
  display: block;
  overflow: auto;
}

@media only screen and (min-width:991px){
    .svgLayer{
      width: 100%;
    }
}

@media only screen and (min-height:700px) {
  .svgLayer{
      height: 100%;
    }
}

.areaSVGCls,
.mediaSvgCls,
.landmarkSvgCls,
.locationSvgCls{
  cursor: pointer;
}

.landmarkSvgCls:hover>path {
  fill: blue;
}

.mediaSvgCls:hover>path {
  fill: green;
}

.areaSVGCls:hover>path {
  fill: red;
}

.routeSvgCls>polyline,
.routeSvgCls>path {
  stroke-dasharray: 10000;
  stroke-dashoffset: 10000;
}

.routeSvgCls.active>polyline,
.routeSvgCls.active>path {
  animation: dash 5s linear forwards;
}

@keyframes dash {
  to {
    stroke-dashoffset: 0;
  }
}
</style>
