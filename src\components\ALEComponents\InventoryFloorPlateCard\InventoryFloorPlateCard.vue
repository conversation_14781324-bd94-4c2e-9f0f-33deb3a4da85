<script setup>
import { thousandSeparator } from "../../../helpers/helper";
import { GetCurrencySymbol } from "../../../helpers/API";
import StatusPill from "../StatusPill/StatusPill.vue";
import { defineEmits } from "vue";
defineProps({
  name: {type: String, default: ""},
  status: {type: String, default: ""},
  tower: {type: String, default: ""},
  floor: {type: Number, default: 0},
  type: {type: String, default: ""},
  price: {type: String, default: ""},
  currency: {type: String, default: ""},
  measurement: {type: String, default: ""},
  measurementType: {type: String, default: ""},
  bedrooms: {type: Number, default: 0},
  unitId: {type: String, default: ""},
  hideStatus: {type: String, default: "false"},
  favUnits: {type: Array, default: () => []},
});
const emit = defineEmits('emitEvent', 'toggleButton');
</script>

<template>
  <div class="md:w-full flex w-max">
    <div :class="['h-[8.44rem] md:w-fit border relative border-InventoryFloorPlateCardBorder bg-InventoryFloorPlateCardBg bg-blur-[10px]' , 'p-4 flex flex-col gap-6 justify-between items-center opacity-80 bg-blend-luminosity backdrop-blur-[3.125rem]  rounded-lg  hover:cursor-pointer   hover:border-white']">
      <div
        class="w-[290px] flex flex-col gap-3 justify-start items-start"
        @click="emit('emitEvent')"
      >
        <!-- Title -->
        <div class="flex  items-center w-full gap-2">
          <h2 class="text-white text-2xl font-medium text-ellipsis overflow-hidden whitespace-nowrap w-[55%]">
            Unit: {{ name }}
          </h2>

          <div class="flex justify-center items-center mr-3 gap-3">
            <StatusPill
              v-if="!hideStatus"
              :availability-status="status"
              :showAvailableUnits="false"
            />
          </div>
        </div>

        <!-- tower,floor and type tiles -->
        <div class="flex">
          <p class="text-white text-sm font-medium capitalize">
            {{ tower }}
          </p>
          <div class="w-0.5 h-5 text- bg-gray-500 mx-3 place-self-center" />
          <p class="text-white  text-sm font-medium capitalize">
            Floor {{ floor }}
          </p>
          <div class="w-0.5 h-5 bg-gray-500 mx-3 place-self-center" />
          <p class="text-white  text-sm font-medium capitalize">
            {{ type }}
          </p>
        </div>

        <!-- Price,sqft and bedrooms tiles -->
        <div :class="['flex w-max justify-start items-start','w-full flex gap-3 ']">
          <div
            v-show="price !== '' && price !== '0'"
            class="gap-1 flex justify-between items-center"
          >
            <slot name="currencySymbol" />
            <p

              class="uppercase text-white text-md font-normal flex justify-start items-center gap-1"
            >
              <span
                v-if="price && currency"
                class="inline-block  whitespace-nowrap overflow-hidden min-w-[auto]"
              >{{ GetCurrencySymbol(currency) }} {{ thousandSeparator(price) }} </span>  {{ currency }}
            </p>
          </div>

          <div
            v-if="measurement"
            class="gap-2 flex justify-between items-center flex-nowrap"
          >
            <div class=" opacity-70  ">
              <svg
                class="w-5 h-5"
                viewBox="0 0 24 24"
                fill="white"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M5.45516 7.06704C5.16183 7.34066 4.70223 7.32468 4.42861 7.03135L2.95217 5.44853C2.69128 5.16885 2.69199 4.73478 2.95379 4.45595L4.43023 2.88346C4.7048 2.59102 5.16445 2.57654 5.45689 2.85112C5.74932 3.12569 5.7638 3.58534 5.48923 3.87777L5.12498 4.26571H13.3348L12.9733 3.87808C12.6996 3.58475 12.7156 3.12515 13.009 2.85154C13.3023 2.57792 13.7619 2.5939 14.0355 2.88723L15.5119 4.47004C15.7728 4.74972 15.7721 5.1838 15.5103 5.46263L14.0339 7.03512C13.7593 7.32755 13.2997 7.34203 13.0072 7.06746C12.7148 6.79289 12.7003 6.33324 12.9749 6.04081L13.3369 5.65519H5.13143L5.49085 6.04049C5.76446 6.33382 5.74848 6.79342 5.45516 7.06704ZM14.0783 9.74978H3.85767C3.78863 9.74978 3.73267 9.80574 3.73267 9.87478V20.0785C3.73267 20.1476 3.78863 20.2035 3.85767 20.2035H14.0783C14.1473 20.2035 14.2033 20.1476 14.2033 20.0785V9.87478C14.2033 9.80574 14.1473 9.74978 14.0783 9.74978ZM3.85767 8.74978C3.23635 8.74978 2.73267 9.25346 2.73267 9.87478V20.0785C2.73267 20.6998 3.23635 21.2035 3.85767 21.2035H14.0783C14.6996 21.2035 15.2033 20.6998 15.2033 20.0785V9.87478C15.2033 9.25346 14.6996 8.74978 14.0783 8.74978H3.85767ZM16.9167 9.95788C16.6233 10.2315 16.6074 10.6911 16.881 10.9844C17.1546 11.2778 17.6142 11.2937 17.9075 11.0201L18.2963 10.6575V19.2898L17.9107 18.9278C17.6183 18.6532 17.1586 18.6677 16.884 18.9601C16.6095 19.2525 16.6239 19.7122 16.9164 19.9868L18.4889 21.4632C18.7677 21.725 19.2018 21.7257 19.4815 21.4648L21.0643 19.9884C21.3576 19.7148 21.3736 19.2552 21.1 18.9618C20.8263 18.6685 20.3667 18.6525 20.0734 18.9261L19.6858 19.2877V10.6575L20.0703 11.0185C20.3627 11.2931 20.8223 11.2786 21.0969 10.9862C21.3715 10.6937 21.357 10.2341 21.0646 9.9595L19.4921 8.48306C19.2132 8.22126 18.7792 8.22055 18.4995 8.48144L16.9167 9.95788Z"
                  fill=""
                />
              </svg>
            </div>
            <p
              v-if="measurement && measurementType"
              class="text-white text-md font-normal whitespace-nowrap"
            >
              {{ thousandSeparator(measurement) }} {{ measurementType }}
            </p>
          </div>

          <div
            v-if="bedrooms"
            class="gap-2 flex justify-between items-center flex-nowrap"
          >
            <div class="themesvg-secondary-fill opacity-70  ">
              <svg
                class="w-5 h-5"
                viewBox="0 0 24 24"
                fill="white"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19.5 6.75H2.25V4.5C2.25 4.30109 2.17098 4.11032 2.03033 3.96967C1.88968 3.82902 1.69891 3.75 1.5 3.75C1.30109 3.75 1.11032 3.82902 0.96967 3.96967C0.829018 4.11032 0.75 4.30109 0.75 4.5V19.5C0.75 19.6989 0.829018 19.8897 0.96967 20.0303C1.11032 20.171 1.30109 20.25 1.5 20.25C1.69891 20.25 1.88968 20.171 2.03033 20.0303C2.17098 19.8897 2.25 19.6989 2.25 19.5V16.5H21.75V19.5C21.75 19.6989 21.829 19.8897 21.9697 20.0303C22.1103 20.171 22.3011 20.25 22.5 20.25C22.6989 20.25 22.8897 20.171 23.0303 20.0303C23.171 19.8897 23.25 19.6989 23.25 19.5V10.5C23.25 9.50544 22.8549 8.55161 22.1516 7.84835C21.4484 7.14509 20.4946 6.75 19.5 6.75ZM2.25 8.25H9V15H2.25V8.25ZM10.5 15V8.25H19.5C20.0967 8.25 20.669 8.48705 21.091 8.90901C21.5129 9.33097 21.75 9.90326 21.75 10.5V15H10.5Z"
                  fill=""
                />
              </svg>
            </div>
            <p class="text-white text-md font-normal capitalize whitespace-nowrap">
              {{ bedrooms }}
            </p>
          </div>
        </div>
      </div>
      <button
        class=" absolute right-2.5 h-8 w-8 p-2  bg-InventoryFloorPlateCardIconBg bg-opacity-60 backdrop-blur-sm rounded-full flex justify-start items-center gap-2 "
        @click="emit('toggleButton')"
      >
        <svg
          id="goToFavotiteSvg"
          class="w-5 h-5 "
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10 16.6668C10 16.6668 2.5 12.4897 2.5 7.47711C2.5 2.46452 8.33333 2.0468 10 5.96867C11.6667 2.0468 17.5 2.46452 17.5 7.47711C17.5 12.4897 10 16.6668 10 16.6668Z"
            :stroke="Object.values(favUnits).some(innerObj => innerObj.unit_id === unitId)?'#FF4747':'#FFFFFF'"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
            :fill="Object.values(favUnits).some(innerObj => innerObj.unit_id === unitId)?'#FF4747':'none'"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>
.card{
  width: -webkit-fill-available;
}
</style>
