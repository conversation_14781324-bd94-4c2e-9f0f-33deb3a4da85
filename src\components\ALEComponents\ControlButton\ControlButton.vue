<template>
  <div class="div">
    <button
      :data-tooltip-target="`tooltip-${id}`"
      :data-tooltip-placement="Store.isMobile?'left':'bottom'"
      type="button"
      class="state bg-secondary rounded-full backdrop-filter backdrop-blur-[0.625rem] flex  items-center justify-center w-11 h-11  "
      :class="{'bg-primary bg-opacity-90': isHover, 'bg-secondary backdrop-blur bg-opacity-65':!active && !isHover,'!bg-primary cursor-pointer stateActive':active, '!w-10 !h-10 !p-2 ' : Store.isMobile}"
      @click="onClickButton(id) "
    >
      <div v-html="active?inactiveSVG:activeSVG" />
    </button>

    <div
      :id="`tooltip-${id}`"
      role="tooltip"
      class="absolute z-1 invisible inline-block whitespace-nowrap px-3 py-2 text-sm font-medium text-secondaryText bg-secondary rounded-lg shadow-sm opacity-0 tooltip dark:bg-secondary"
    >
      <TranslationComp
        :text="tooltiptext"
      />
      <div
        class="tooltip-arrow "
        data-popper-arrow
      />
    </div>
  </div>
</template>

<script setup>

import { defineProps, defineEmits, ref, onMounted} from 'vue';
import { creationToolStore } from '../../../store';
import { initTooltips } from 'flowbite';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const Store = creationToolStore();

defineProps({
  name: {
    type: String,
    default: "",
  },
  isHover: Boolean,
  active: Boolean,
  showTooltip: Boolean,
  activeSVG: {type: String, default: ""},
  inactiveSVG: {type: String, default: ""},
  id: {type: String, default: ""},
  tooltiptext: {type: String, default: ""},

});

const emit = defineEmits(['onClick']);

const isClicked = ref(false);

function onClickButton (id) {
  isClicked.value = !isClicked.value;
  emit('onClick', id);
}

onMounted(() => {
  initTooltips();
});
</script>
<style scoped>
.state:hover>div::v-deep>svg > path , .state:hover{
  background-color: var(--primary);
  cursor: pointer;
  stroke: var(--secondary);
}

.state>div::v-deep>svg > path{
padding: var(--35, 10px);
stroke:var(--secondaryText);
}
.stateActive>div::v-deep>svg>path {
  stroke: var(--secondary);
}

@media (max-width:425px){
/* .state>div::v-deep(.filterMobileSVG > path) {
  stroke: var(--colormix) !important;
  fill: var(--colormix) !important;
} */
}
</style>

<style lang="scss">

</style>
