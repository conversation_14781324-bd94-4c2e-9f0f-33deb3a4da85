import BedroomLegend from "./BedroomLegend.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: "Design System/ALE/BedroomLegend ",
  component: BedroomLegend,
  tags: ["autodocs"],
  argTypes: {},
};

export const Primary = {
  args: {},
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=142-18318&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
