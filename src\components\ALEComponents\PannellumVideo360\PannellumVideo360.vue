<template>
  <div>
    <video
      id="panorama"
      class="video-js vjs-default-skin vjs-big-play-centered"
      controls
      preload="none"
      :poster="poster"
      crossorigin="anonymous"
      style="width: 100%; height: 400px;"
    >
      <source
        :src="videoSrcMp4"
        type="video/mp4"
      >
      <source
        :src="videoSrcWebm"
        type="video/webm"
      >
      <p class="vjs-no-js">
        To view this video please enable JavaScript, and consider upgrading to
        a web browser that <a
          href="http://videojs.com/html5-video-support/"
          target="_blank"
        >supports HTML5 video</a>
      </p>
    </video>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { defineProps } from 'vue';
defineProps({
  videoSrcWebm: {
    type: String,
    // Default: 'https://vjs.zencdn.net/v/oceans.webm'
  },
  videoSrcMp4: {
    type: String,
    // Default: 'https://bitmovin-a.akamaihd.net/content/playhouse-vr/progressive.mp4'
  },
  poster: {
    type: String,
    // Default: '/360.jpg'
  },
});

const player = ref(null);

onMounted(() => {
  player.value = window.videojs('panorama', {
    controls: true,
    autoplay: true,
    loop: true,
    preload: 'auto',
    plugins: {
      pannellum: {
        exampleOption: true,
        clickToToggle: true,
        clickAndDrag: true,
        showNotice: true,
        NoticeMessage: 'Drag to look around',
        autoHideControlBar: false,
        backToVerticalCenter: false,
        backToHorizonCenter: false,
      },
    },
  });
});

onUnmounted(() => {
  if (player.value) {
    player.value.dispose();
  }
});
</script>

<style scoped>
/* Add any additional styling here */
</style>
