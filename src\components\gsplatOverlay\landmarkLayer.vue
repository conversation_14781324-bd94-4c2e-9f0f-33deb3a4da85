<script setup>
import { ref, defineProps, watch, onMounted, defineEmits } from 'vue';
import router from '../../router';
import { useRoute } from 'vue-router';
import {creationToolStore} from '../../store/index';
import LandMarkCard from '../ALEComponents/LandMarkCard/LandMarkCard.vue';
import NearByFloatingButton from '../ALEComponents/NearByFloatingButton/NearByFloatingButton.vue';
import { landmark_icon } from '../../config/masterdata';
import { cdn } from '../../helpers/helper';
import { LandmarkCategories } from '../../config/masterdata';

const props = defineProps({layer: {
  type: Object,
  default () {
    return {};
  },
}});
const show_modal = ref(false), position=ref(false), selectedData=ref({landmark: false});
const Store = creationToolStore();
const route = useRoute();
const currentCategory = ref(route.query.category);
const landmarkLayer = ref(null);
const activeCategory = ref(route.query.category || 'Show All');
const emit = defineEmits(['appendObject']);
function placeModal (id, landmarkId){
  position.value={x: 2, y: 2};
  router.push({ name: 'projectScene', query: { ...route.query, landmarkId: id, category: Store.landmarkData[landmarkId].category } });
}
onMounted(async () => {
  const url =  cdn(props.layer.svg_url);
  var svgString;
  if (props.layer.svg_url !== undefined && props.layer.svg_url !== ""){
    const requestOptions = {
      method: "GET",
      redirect: "follow",
    };
    const response = await fetch(url, requestOptions);
    svgString = await response.text();
  } else {
    svgString = landmark_icon[props.layer.type].icon;
  }
  const div = document.createElement('div');
  div.id=props.layer.layer_id;
  div.innerHTML=svgString;
  div.setAttribute('class', 'amenityIcons');
  landmarkLayer.value=div;
  document.body.appendChild(div);
  document.getElementById(props.layer.layer_id).addEventListener( 'click', (event) => placeModal(props.layer.layer_id, props.layer.landmark.landmark_id, event) );
  emit('appendObject', landmarkLayer.value);
});
watch(() => {
  return route.query;
}, (newPath) => {
  currentCategory.value = newPath.category;
  if (newPath.landmarkId){
    selectedData.value={
      landmark: props.layer.landmark.landmark_id,
    };
    show_modal.value=true;
  } else {
    selectedData.value.landmark = false;
    show_modal.value=false;
  }

});

function closeModal (e){
  if ((e===undefined || e) && (e.target.getAttribute("clickaway")==="true")){
    show_modal.value=false;
    selectedData.value.landmark = false;
    const currentQuery = { ...route.query };
    delete currentQuery.landmarkId;
    delete currentQuery.category;
    router.push({ name: 'projectScene'});
  }
}

const setActiveButton = (value) => {
  if (value === null) {
    activeCategory.value = null;
    router.push({ name: 'projectScene' });
  } else if (value === 'Show All') {
    activeCategory.value = value.value;
    router.push({ name: 'projectScene' });
  } else {
    activeCategory.value = value.value;
    router.push({ name: 'projectScene', query: { category: value.value } });
  }
};

// Function HoverListener () {
//   Const SVGLayer = document.getElementById('SVGLayer');

//   Object.values(SVGLayer.children).forEach((child) => {

//     If (child.tagName === 'g') {
//       Child.addEventListener('mouseover', () => {
//         Child.classList.add("opacity-100");

//         Object.values(SVGLayer.children).forEach((otherChild) => {
//           If (otherChild.id && otherChild.id !== child.id && otherChild.tagName !== 'image') {
//             OtherChild.classList.add("opacity-50");
//           }
//         });
//       });

//       Child.addEventListener('mouseleave', () => {
//         Child.classList.remove("opacity-100");

//         Object.values(SVGLayer.children).forEach((otherChild) => {
//           If (otherChild.id && otherChild.id !== child.id && otherChild.tagName !== 'image') {
//             OtherChild.classList.remove("opacity-50");
//           }
//         });
//       });

//     }
//     If (child.tagName === 'g'||child.tagName === 'path') {
//       Child.addEventListener('click', () => {
//         Child.classList.add("opacity-50");
//         Object.values(SVGLayer.children).forEach((otherChild) => {
//           If (otherChild.id && otherChild.id !== child.id && otherChild.tagName !== 'image') {
//             OtherChild.classList.add("opacity-50");
//           }
//         });
//       });
//     }
//   });
// }

// OnMounted(() => {
//   HoverListener();
// });

</script>
<template>
  <div
    v-if="show_modal && Store.landmarkData"
    class="fixed top-0 left-0 w-screen h-full flex justify-center z-20"
    clickaway="true"
    @click="closeModal"
  >
    <LandMarkCard
      class="absolute slide-animation md:bottom-4 md:left-4 bottom-4 left-auto p-4 "
      :name="Store.landmarkData[selectedData['landmark']].name "
      :description="Store.landmarkData[selectedData['landmark']].description"
      :distance="Store.landmarkData[selectedData['landmark']].distance"
      :car-timing="Store.landmarkData[selectedData['landmark']].car_timing"
      :transit-timing="Store.landmarkData[selectedData['landmark']].transit_timing"
      :walk-timing="Store.landmarkData[selectedData['landmark']].walk_timing"
      :thumbnail="Store.landmarkData[selectedData['landmark']].thumbnail"
      :data="Store.landmarkData[selectedData['landmark']]"
      @close-card="closeModal"
      @go-to-project="handleGoToLandmark"
    />
  </div>
  <div
    v-if="!show_modal && Store.landmarkData"
    class="fixed w-full flex justify-center"
    :class="Store.isMobile ? 'bottom-24' : 'bottom-10'"
  >
    <NearByFloatingButton
      class="flex"
      :class="Store.isMobile ? 'w-full' : 'sm:w-[49%] md:w-[59%] lg:w-[69%] xl:[70%]'"
      :itemsList="Object.values(LandmarkCategories.category)"
      :active="activeCategory ? Object.values(filteredCategory).findIndex((e) => e.value === activeCategory) : 0"
      :sliderButton="false"
      :objectNameKey="`value`"
      :objectIconKey="`icon`"
      @button-clicked="setActiveButton"
    />
  </div>
</template>
<style scoped>
  .routeCls{
    stroke-dasharray: 10000;
    stroke-dashoffset: 10000;
     animation: dash 15s linear forwards;
  }

  @keyframes dash {
        to {
          stroke-dashoffset: 0;
        }
      }

.slide-animation
{
  animation:slide 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}
@keyframes slide
{
  0%
  {
    left :-24em;
  }
  50%
  {
    left:6em;
  }
  100%
  {
    @apply md:left-16 ;

  }
}
</style>
