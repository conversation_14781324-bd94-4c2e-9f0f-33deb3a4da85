import EnquiryButton from './EnquiryButton.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/EnquiryButton',
  component: EnquiryButton,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    message: " Make an Enquiry",
    SVG: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.4021 4.61879L7 7.41739L12.5979 4.61879C12.5772 4.2621 12.4209 3.92683 12.161 3.68165C11.9011 3.43646 11.5573 3.29992 11.2 3.29999H2.8C2.44271 3.29992 2.09891 3.43646 1.83902 3.68165C1.57914 3.92683 1.42282 4.2621 1.4021 4.61879V4.61879Z" fill="white"/>
    <path d="M12.6 6.18262L7.00002 8.98262L1.40002 6.18262V10.3C1.40002 10.6713 1.54752 11.0274 1.81007 11.29C2.07263 11.5525 2.42872 11.7 2.80002 11.7H11.2C11.5713 11.7 11.9274 11.5525 12.19 11.29C12.4525 11.0274 12.6 10.6713 12.6 10.3V6.18262Z" fill="white"/>
    </svg>`,
  },
};
