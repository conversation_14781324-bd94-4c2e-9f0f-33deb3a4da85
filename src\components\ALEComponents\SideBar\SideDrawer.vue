<template>
  <div
    ref="dropDownWrapperRef"
    class="absolute flex top-0 h-full z-20 items-center overflow-hidden"
    :class="[right ? 'right-0 flex-row' : 'left-0 flex-row-reverse']"
  >
    <button
      ref="dropdownRef"
      class=" h-[63px] w-[30px] flex-0 shrink-0 bg-white text-center focus:outline-none pr-1 pl-2  rounded-tl-[20px] rounded-bl-[20px] backdrop-blur-lg justify-center items-center inline-flex"
      @click.prevent="toggle"
    >
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        :class="[open ? 'rotate-0' : '-rotate-180', ' transition-rotate duration-300 ' ]"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.85645 16.25L7.34152 9.25L1.85645 2.25L3.78928 2.25L9.27436 9.25L3.78928 16.25L1.85645 16.25Z"
          fill="#151515"
          fill-opacity="0.5"
        />
        <path
          d="M7.45703 16.25L12.9421 9.25L7.45703 2.25L9.38987 2.25L14.8749 9.25L9.38987 16.25L7.45703 16.25Z"
          fill="#ACACAC"
        />
      </svg>
    </button>

    <div
      class="drawer-proj-list text-white"
      :class="[open ? 'dopen' : 'dclose']"
    >
      <div class="proj-title">
        <p>{{ title }}</p>
        <button
          class="themesvg-secondary-stroke"
          @click.prevent="toggle"
        >
          <svg
            class="w-6 h-6"
            width="27"
            height="27"
            viewBox="0 0 27 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.25 20.25L6.75 6.75"
              stroke=""
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M20.25 6.75L6.75 20.25"
              stroke=""
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <slot name="content">
        <!--     <AmenityCatergories :data="amenitiesData"  :categories="categories"/>  -->
      </slot>
    </div>
  </div>

<!--     <transition name="fade">

      <div v-if="dimmer && open" @click="toggle" class="flex-1 bg-gray-400 bg-opacity-75 active:outline-none z-10">
      </div>
    </transition> -->
</template>

<script setup>
import { ref, onUnmounted, defineProps, defineEmits, watch} from 'vue';
// Import AmenityCatergories from '../AmenityCatergories/AmenityCatergories.vue';

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  isOpen: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['toggle']);
// Project Data
// Const data = {
//   Name: 'Prestige Park Grove',
//   Units: 12,
//   Amenities: 15,
//   Floor: 26,
//   Location: "Bangalore",
//   _id: "####",
//   Thumbnail: "https://images.adsttc.com/media/images/649c/670f/5921/183e/3b97/89cc/medium_jpg/london-architecture-city-guide-20-modern-and-contemporary-attractions-to-explore-in-uks-cultural-and-financial-powerhouse_1.jpg?1687971619",
// };
// Amenities Data
/* Const amenitiesData = {
  "65ae73c47acabc2ec44f9fbf":
  {"_id": "65ae73c47acabc2ec44f9fbf", "project_id": "659f886821bd11d5d9218378", "name": "Gym ", "category": "indoor_amenities", "community_id": "null", "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/amenitiesthumbnail%2FRustomjee_cleon_thumbnail.jpg?alt=media", "media": [{"_id": "65ae73c77acabc2ec44f9fc3", "media_type": "360_image", "file": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FFcxAht%2Fprojects%2F659f886821bd11d5d9218378%2Famenities%2F65ae73c47acabc2ec44f9fbf%2FRustomjee_cleon_amenity_gym_view_01.jpg?alt=media"}], "__v": 1, "description": "Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons. Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons."},
  "66029c6d1584e4822d2a630f":
  {"_id": "66029c6d1584e4822d2a630f", "project_id": "659f886821bd11d5d9218378", "name": "Swimming Pool", "category": "outdoor_amenities", "community_id": "65a10fb91d969d608f7fd1a2", "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FFcxAht%2Fprojects%2F659f886821bd11d5d9218378%2Famenities%2F66029c6d1584e4822d2a630f%2F1710307528559-Daylight_03.jpg?alt=media", "media": [{"_id": "66029c6e1584e4822d2a6313", "media_type": "embed_link", "link": "https://showcase.propvr.tech/?m=zJ9Mimkmanb"}], "__v": 1},
  "3243242":
  {"_id": "3243242", "project_id": "659f886821bd11d5d9218378", "name": "Gym ", "category": "indoor_amenities", "community_id": "null", "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/amenitiesthumbnail%2FRustomjee_cleon_thumbnail.jpg?alt=media", "media": [{"_id": "65ae73c77acabc2ec44f9fc3", "media_type": "360_image", "file": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FFcxAht%2Fprojects%2F659f886821bd11d5d9218378%2Famenities%2F65ae73c47acabc2ec44f9fbf%2FRustomjee_cleon_amenity_gym_view_01.jpg?alt=media"}], "__v": 1, "description": "Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons. Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons."},
};

const categories = [{'category': 'indoor_amenities', 'count': '03'}, {'category': 'outdoor_amenities', 'count': '01'}];
 */
const open = ref(props.isOpen);
watch(() => props.isOpen, () => {
  open.value=props.isOpen;
});
const right = ref(true); /* Made by kMehul (Initial) () */

const dropdownRef = ref(null); // Button
const dropDownWrapperRef = ref(null); // Parent  wrapper

const toggle = () => {
  open.value = !open.value;
  emit('toggle');
};

const closeDropdownOnOutsideClick = (event) => {

  if (open.value && !dropDownWrapperRef.value.contains(event.target) ) {
    open.value = false;
  }
};

onUnmounted(() => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
});
</script>

<style scoped>

.proj-title {
@apply text-2xl font-medium leading-7 tracking-normal text-left flex items-center justify-between m-[1em];

}

.ro90 {
rotate: 90deg;

}

.ro270 {
rotate: 270deg;
}

.ro90,
.ro270 {
transition-duration: 0.5s;
}

.drawer-proj-list {
@apply transition-all h-full duration-700 flex-col-reverse overflow-y-auto overflow-x-hidden items-center justify-center;
}

.drawer-proj-list{
/*   background: rgba(54, 54, 54, 0.89); */
 /*  backdrop-filter: blur(100px);  */
  -webkit-backdrop-filter: blur(100px);
  background: rgba(0, 0, 0, 0.60);
background-blend-mode: luminosity;
backdrop-filter: blur(100px);
}

.drawer-proj-list::-webkit-scrollbar {
  @apply  w-0 h-0 md:h-2
}

.drawer-proj-list::-webkit-scrollbar-track {
background: rgb(189, 189, 189);
}

.drawer-proj-list::-webkit-scrollbar-thumb {
background: rgba(54, 54, 54, 0.411);
border-radius: 20px;

}

@media screen and (max-width: 480px) {
.proj-title {
  display: none;
}

.drawer-proj-list {
  background-color: transparent;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  justify-content: normal;
}

.dopen {
  max-height:13em;
  display: flex;
  flex-direction: row;
  overflow: scroll;
  bottom: 0;

}

.dclose {
  max-height: 0;
}
}

.fade-enter-active,
.fade-leave-active {
transition: opacity 1s ease-out;
}

.fade-enter,
.fade-leave-to {
opacity: 0;
}

.dopen {
@apply max-w-lg;
}

.dclose {
@apply max-w-0;
}

.curveRadius{
  border-radius: 20px;
}

</style>
