@echo off
setlocal

REM Define predefined folder names
echo Please select a folder name:
echo [1] ALEComponents
echo [2] Overall
echo [3] Folder3
echo.
set /p CHOICE=Enter your choice (1-3):

REM Map the user choice to a folder name
if "%CHOICE%"=="1" set "FOLDERNAME=ALEComponents"
if "%CHOICE%"=="2" set "FOLDERNAME=Overall"
if "%CHOICE%"=="3" set "FOLDERNAME=Folder3"

REM Check if a valid choice was made
if not defined FOLDERNAME (
    echo Invalid choice. Exiting.
    exit /b
)

REM Ask for the name of the project
echo Please enter the name for your project (e.g., 'MyProject'):
set /p PROJECTNAME=

REM Check if a project name was entered
if "%PROJECTNAME%"=="" (
    echo No project name entered. Exiting.
    exit /b
)

REM Create the folder structure
set FOLDER=src\components\%FOLDERNAME%\%PROJECTNAME%
mkdir "%FOLDER%"

REM Check if template.stories.txt exists
if not exist "template.stories.txt" (
    echo template.stories.txt not found. Exiting.
    exit /b
)

REM Copy the contents of template.stories.txt to the new stories.js file
copy template.stories.txt "%FOLDER%\%PROJECTNAME%.stories.js"
echo. 2>"%FOLDER%\%PROJECTNAME%.vue"


echo stories.js file created successfully.

endlocal
