<script setup>
import { ref, defineProps, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';
import { loadImageData, cdn } from '../../../helpers/helper';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
// import ProgressBar from '../../../components/ALEComponents/ProgressBar/ProgressBar.vue';
const props = defineProps({
  lowResUrl: {type: String, default: ""},
  highResUrl: {type: String, default: ""},
  hotSpots: {type: String, default: ""},
  activeNavigatorTab: {type: String, default: ""},
  currentImageId: {type: String, default: ""},
  currentFloorUnitplandId: {type: String, default: ""},
  iszoomenabled: {type: Boolean, default: true},
});

const lowResImage = ref(), loaderProgress = ref(0), highResdata = ref(false), lowResData = ref(false), activeItem = ref(props.currentImageId), localActiveNavigatorTab = ref(props.activeNavigatorTab), lowResUrlProp = ref(props.lowResUrl), highResUrlProp = ref(props.highResUrl);
const route = useRoute();
const Store = creationToolStore();
const emit = defineEmits([ 'setCurrentActive', 'activeNavigatorTabEmit', 'lowResLoaded', 'currentImageIdEmit' ]);
const localHotSpots = ref(props.hotSpots);
const imageDimensionsDiv = ref(null), circularloader = ref(false);
let currentZoom = 1;
const minZoom = 1;
const maxZoom = 3;
const stepSize = 0.1;
let isPanning = false; // Track if panning is active
let startX = 0; // Starting X coordinate of the mouse
let startY = 0; // Starting Y coordinate of the mouse
let offsetX = 0; // Current X offset of the container
let offsetY = 0; // Current Y offset of the container
// Prevent two-finger pinch zoom on `touchmove` events
let lastTouchDistance = null;
const rangeVal = ref(1);
const zoomSlider = ref(null);

// Add new refs for transition handling
const isTransitioning = ref(false);
const isHotspotsTransitioning = ref(false);
const currentImage = ref(null);
const nextImage = ref(null);
const showNextImage = ref(false);
const hotspotsVisible = ref(true);
const imageLoaded = ref(false);
const pendingHotspots = ref(null);
const nextImageEl = ref(null);
const highResVisible = ref(false);
const showLabels = ref(false);
const initialSizingLogic = ref(null);
const containerStyle = ref({});

// Helper for async sleep
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to ensure container has proper dimensions
function ensureContainerDimensions () {
  const container = document.getElementById("image-container");
  if (container && (container.clientWidth === 0 || container.clientHeight === 0)) {
    // Force a reflow to ensure dimensions are calculated
    container.style.display = 'none';
    container.offsetHeight; // Force reflow
    container.style.display = '';

    // If still no dimensions, set minimum dimensions
    if (container.clientWidth === 0 || container.clientHeight === 0) {
      container.style.width = '100%';
      container.style.height = '100%';
    }
  }
}

const setCurrentActiveEmit = (image_id) => {
  activeItem.value = image_id;
  emit("setCurrentActive", 'interior', image_id);
};

// Function to handle hotspots transition
async function handleHotspotsTransition (newHotspots) {
  // Step 1: Fade out current hotspots
  isHotspotsTransitioning.value = true;
  await sleep(300); // Wait for fade out to complete

  // Step 2: Update hotspots after fade out
  localHotSpots.value = newHotspots;

  // Step 3: Fade in new hotspots
  isHotspotsTransitioning.value = false;
}

function getImageDimensions (img) {
  if (img) {
    // Unset previous inline styles to avoid compounding
    img.style.width = '';
    img.style.height = '';
    // Only declare once!
    const container = document.getElementById("image-container");
    if (container) {
      container.style.width = '';
      container.style.height = '';
    }

    // Wait for the image to have natural dimensions
    if (img.naturalWidth === 0 || img.naturalHeight === 0) {
      console.log('Image not fully loaded yet, retrying...');
      setTimeout(() => getImageDimensions(img), 100);
      return;
    }

    const imageWidth = img.naturalWidth;
    const imageHeight = img.naturalHeight;

    // Ensure container has proper dimensions
    ensureContainerDimensions();

    // Get the container dimensions - try multiple approaches
    let containerWidth, containerHeight;

    if (container && container.clientWidth > 0 && container.clientHeight > 0) {
      containerWidth = container.clientWidth;
      containerHeight = container.clientHeight;
    } else {
      // Fallback to parent container or window dimensions
      const parentContainer = container ? container.parentElement : null;
      if (parentContainer && parentContainer.clientWidth > 0 && parentContainer.clientHeight > 0) {
        containerWidth = parentContainer.clientWidth;
        containerHeight = parentContainer.clientHeight;
      } else {
        // Final fallback to window dimensions
        containerWidth = window.innerWidth * 0.85;
        containerHeight = window.innerHeight * 0.8;
      }
    }

    console.log('Container dimensions:', containerWidth, containerHeight);
    console.log('Image natural dimensions:', imageWidth, imageHeight);

    // Calculate the aspect ratios
    const imageAspectRatio = imageWidth / imageHeight;
    const containerAspectRatio = containerWidth / containerHeight;

    let finalWidth, finalHeight;

    if (imageAspectRatio > containerAspectRatio) {
      // Image is wider than container - fit to width
      finalWidth = containerWidth;
      finalHeight = containerWidth / imageAspectRatio;
    } else {
      // Image is taller than container - fit to height
      finalHeight = containerHeight;
      finalWidth = containerHeight * imageAspectRatio;
    }

    // Set the image dimensions
    img.style.width = finalWidth + 'px';
    img.style.height = finalHeight + 'px';
    img.style.objectFit = 'contain';

    // Wait a bit for the styles to apply, then get the actual rendered dimensions
    setTimeout(() => {
      const actualWidth = img.clientWidth || img.offsetWidth || finalWidth;
      const actualHeight = img.clientHeight || img.offsetHeight || finalHeight;

      console.log('Actual rendered dimensions:', actualWidth, actualHeight);

      imageDimensionsDiv.value = {
        'width': actualWidth,
        'height': actualHeight,
      };

      // Only set container style for unitplan
      // if (localActiveNavigatorTab.value === 'unitplan') {
      containerStyle.value = {
        width: actualWidth + 'px',
        height: actualHeight + 'px',
        position: 'relative',
        maxWidth: '100%',
        maxHeight: '100%',
        overflow: 'hidden',
        margin: '0 auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      };
      // } else {
      //   containerStyle.value = {};
      // }

      console.log('Final imageDimensionsDiv:', imageDimensionsDiv.value);
    }, 50);
  }
}

function updateImageDimensions () {
  const img = lowResImage.value;
  if (img) {
    // Ensure the image is fully loaded before getting dimensions
    if (img.complete && img.naturalWidth > 0) {
      getImageDimensions(img);
    } else {
      // If image is not fully loaded, wait for it
      img.onload = () => {
        // getImageDimensions(img);
      };
    }
  }
}

// Function to preload image
async function preloadImage (url) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(url);
    img.onerror = reject;
    img.src = url;
  });
}

// Function to handle image transitions
async function startTransition (newImage) {
  // Only run transition logic for unitplan
  if (localActiveNavigatorTab.value !== 'unitplan') {
    currentImage.value = newImage;
    await nextTick();
    updateImageDimensions();
    highResVisible.value = true;
    showLabels.value = true;
    return;
  }
  // Step 1: Start hotspots fade out
  isHotspotsTransitioning.value = true;
  await sleep(300); // Wait for fade out to complete

  await preloadImage(newImage);
  nextImage.value = newImage;
  await nextTick();

  // Now show the next image, which will trigger the @load event
  showNextImage.value = true;
}

function zoomImage (direction, event) {
  const container = document.getElementById("image-container");
  const newZoom = currentZoom + (direction * stepSize);

  // Limit the zoom level to the minimum and maximum
  // values
  if (newZoom < minZoom || newZoom > maxZoom) {
    return;
  }
  // If zooming out to minimum zoom level, reset offsets to 0
  if (newZoom === minZoom) {
    currentZoom = minZoom;
    offsetX = 0;
    offsetY = 0;
  } else {
    // Calculate the mouse position relative to the container
    const rect = container.getBoundingClientRect();
    const mouseX = event.clientX - rect.left; // X position inside the container
    const mouseY = event.clientY - rect.top;  // Y position inside the container
    currentZoom = newZoom;
    // Calculate the scale change
    const scaleChange = newZoom / currentZoom;
    // Adjust the offsets to zoom towards the mouse position
    offsetX = mouseX - (scaleChange * (mouseX - offsetX));
    offsetY = mouseY -( scaleChange * (mouseY - offsetY));
  }
  // Apply the transform
  container.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${currentZoom})`;

}

function setZoomListener (container){

  container.addEventListener("wheel", function (event) {
  // Check if the Ctrl key (or Cmd key on macOS) is pressed
    if (event.ctrlKey || event.metaKey) {
      // Prevent the default browser zoom
      event.preventDefault();
    }

    // If the event targets your zoomable container, handle it
    if (event.target.closest("#image-container")) {
      event.preventDefault(); // Prevent default scrolling
      const direction = event.deltaY > 0 ? -1 : 1; // Determine zoom direction
      rangeVal.value = currentZoom;
      zoomSlider.value.style.background = `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(rangeVal.value-zoomSlider.value.min)/(zoomSlider.value.max-zoomSlider.value.min)*100}%, var(--tertiary50opacity) ${(rangeVal.value-zoomSlider.value.min)/(zoomSlider.value.max-zoomSlider.value.min)*100}%, var(--tertiary50opacity) 100%)`;
      zoomImage(direction, event); // Call your zoom function
    }
  }, { passive: false }); // passive: false allows `preventDefault` to work

  // Add event listeners for mouse events
  container.addEventListener("mousedown", (event) => {
    if (currentZoom>1){
      isPanning = true;
      startX = event.clientX - offsetX;
      startY = event.clientY - offsetY;
      container.style.cursor = "grabbing"; // Change cursor style
    }
  });

  container.addEventListener("mousemove", (event) => {
    if (currentZoom>1){
      if (!isPanning) {
        return;
      }

      offsetX = event.clientX - startX;
      offsetY = event.clientY - startY;
      // Get the container and image dimensions
      const containerRect = container.getBoundingClientRect();
      const imageRect = lowResImage.value.getBoundingClientRect();
      console.log(currentZoom);

      // Get zoomed image size
      const zoomedWidth = imageRect.width * currentZoom;
      const zoomedHeight = imageRect.height * currentZoom;
      // Define minimum threshold (prevents the image from being completely hidden)
      const MIN_PAN_LIMIT = 50; // Adjust this value as needed

      // Calculate max allowed pan values
      const maxX = Math.max((zoomedWidth - containerRect.width) / 4, MIN_PAN_LIMIT);
      const maxY = Math.max((zoomedHeight - containerRect.height) / 4, MIN_PAN_LIMIT);

      // Clamp offsetX and offsetY within range
      offsetX = Math.max(-maxX, Math.min(offsetX, maxX));
      offsetY = Math.max(-maxY, Math.min(offsetY, maxY));

      container.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${currentZoom})`;
    }
  });

  container.addEventListener("mouseup", () => {
    if (currentZoom>1){
      isPanning = false;
      container.style.cursor = "grab"; // Reset cursor style
    }
    container.style.cursor = "default";
  });

  container.addEventListener("mouseleave", () => {
    if (currentZoom>1){
      isPanning = false;
      container.style.cursor = "grab"; // Reset cursor style
    }
  });

  // Add touch events
  container.addEventListener(
    "touchstart",
    (event) => {
      if (event.touches.length === 2) {
        // Handle pinch-to-zoom
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];
        lastTouchDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
            Math.pow(touch2.clientY - touch1.clientY, 2),
        );
      } else if (event.touches.length === 1) {
        // Handle panning
        isPanning = true;
        startX = event.touches[0].clientX - offsetX;
        startY = event.touches[0].clientY - offsetY;
      }
    },
    { passive: false },
  );

  container.addEventListener(
    "touchmove",
    (event) => {
      if (event.touches.length === 2) {
        // Handle pinch-to-zoom
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];

        const distance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
            Math.pow(touch2.clientY - touch1.clientY, 2),
        );

        if (lastTouchDistance !== null) {
          const zoomDelta = distance / lastTouchDistance;

          // Update zoom level
          const newZoom = Math.min(maxZoom, Math.max(minZoom, currentZoom * zoomDelta));
          if (newZoom === minZoom) {
            // Reset position if zooming out to minZoom
            offsetX = 0;
            offsetY = 0;
          }

          currentZoom = newZoom;
        }

        lastTouchDistance = distance;
      } else if (isPanning && event.touches.length === 1 && currentZoom>1) {
        // Handle panning
        const touch = event.touches[0];
        offsetX = touch.clientX - startX;
        offsetY = touch.clientY - startY;
        const containerRect = container.getBoundingClientRect();
        const imageRect = lowResImage.value.getBoundingClientRect();
        console.log(currentZoom);

        // Get zoomed image size
        const zoomedWidth = imageRect.width * currentZoom;
        const zoomedHeight = imageRect.height * currentZoom;
        // Define minimum threshold (prevents the image from being completely hidden)
        const MIN_PAN_LIMIT = 50; // Adjust this value as needed

        // Calculate max allowed pan values
        const maxX = Math.max((zoomedWidth - containerRect.width) / 4, MIN_PAN_LIMIT);
        const maxY = Math.max((zoomedHeight - containerRect.height) / 4, MIN_PAN_LIMIT);

        // Clamp offsetX and offsetY within range
        offsetX = Math.max(-maxX, Math.min(offsetX, maxX));
        offsetY = Math.max(-maxY, Math.min(offsetY, maxY));

      }
      // Apply transform
      container.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${currentZoom})`;

      event.preventDefault(); // Prevent default behavior
    },
    { passive: false },
  );

  container.addEventListener("touchend", () => {
  // Reset states when touch ends
    lastTouchDistance = null;
    isPanning = false;
  });
}

// Add handleImageLoad function
function handleImageLoad () {
  imageLoaded.value = true;
  // Ensure container is properly set up first
  ensureContainerDimensions();
  // Wait a bit longer to ensure the image is fully rendered
  updateImageDimensions();
  const container = document.getElementById("image-container");
  if (container) {
    setZoomListener(container);
  }
}

// Update the watch for lowResUrl to use the new transition function
watch(() => props.lowResUrl, async (newVal, oldVal) => {

  if (!newVal) {
    return;
  }
  highResVisible.value = false;
  initialSizingLogic.value = null;
  showLabels.value = false;

  if (localActiveNavigatorTab.value === 'interior') {
    // For interior tab, use the simpler watch logic
    lowResData.value = false;
    highResdata.value = false;
    circularloader.value = true;
    lowResUrlProp.value = newVal;
    if (newVal) {
      loadImageData(cdn(lowResUrlProp.value), (progress) => {
        loaderProgress.value = (progress / 2);
      }, () => loaderProgress.value = 50).then((lowRes) => {
        updateImageDimensions();
        lowResData.value = lowRes;
        emit('lowResLoaded', true);
      });
    }
  } else {
    // For unitplan tab, use the transition-based logic
    try {
      circularloader.value = true;
      const lowRes = await loadImageData(cdn(newVal), (progress) => {
        loaderProgress.value = progress / 2;
      }, () => loaderProgress.value = 50);

      // Preload the image before starting transition
      await preloadImage(lowRes);

      if (oldVal === undefined) {
        lowResUrlProp.value = newVal;
        lowResData.value = lowRes;
        currentImage.value = lowRes;
        await nextTick();
        imageLoaded.value = true;
        updateImageDimensions();
        emit('lowResLoaded', true);
        circularloader.value = false;
        localHotSpots.value = props.hotSpots;
        hotspotsVisible.value = true;
      } else {
        await startTransition(lowRes);
        lowResData.value = lowRes;
        lowResUrlProp.value = newVal;
        await nextTick();
        updateImageDimensions();
        emit('lowResLoaded', true);
      }
    } catch (error) {
      console.error('Failed to load image:', error);
      circularloader.value = false;
      hotspotsVisible.value = true;
    }
  }
}, { deep: true, immediate: true });

// Update hotspots watch to handle transitions
watch(() => props.hotSpots, async (newVal) => {
  if (isTransitioning.value) {
    // If in transition, store the new hotspots to apply after image transition
    pendingHotspots.value = newVal;
  } else {
    // If not in transition, handle hotspots transition directly
    await handleHotspotsTransition(newVal);
  }
}, { deep: true });

watch(() => props.currentImageId, (newVal) => {
  activeItem.value = newVal;
  emit("currentImageIdEmit", activeItem.value);
  window.parent.postMessage(activeItem.value, '*');
});

// Wrapper for zooming via slider or buttons
function setZoom (newZoom, event = null) {
  const container = document.getElementById("image-container");

  const direction = newZoom > currentZoom ? 1 : -1;

  // Simulate a zoom event at the center of the container
  const rect = container.getBoundingClientRect();
  const centerX = (rect.left + rect.width) / 2;
  const centerY = (rect.top + rect.height) / 2;

  // If `event` is provided (e.g., from slider), use it to pass cursor coordinates
  const simulatedEvent = event || { clientX: centerX, clientY: centerY };

  // Call the existing zoomImage function
  zoomImage(direction, simulatedEvent);
}

function sliderZoom (event){
  const newZoom = parseFloat(event.target.value);
  zoomSlider.value.oninput = function () {
    this.style.background = `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(this.value-this.min)/(this.max-this.min)*100}%, var(--tertiary50opacity) ${(this.value-this.min)/(this.max-this.min)*100}%, var(--tertiary50opacity) 100%)`;
  };
  setZoom(newZoom);
}
function zoomIn (){
  const newZoom = currentZoom + stepSize;
  rangeVal.value = currentZoom;
  zoomSlider.value.style.background = `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(rangeVal.value-zoomSlider.value.min)/(zoomSlider.value.max-zoomSlider.value.min)*100}%, var(--tertiary50opacity) ${(rangeVal.value-zoomSlider.value.min)/(zoomSlider.value.max-zoomSlider.value.min)*100}%, var(--tertiary50opacity) 100%)`;
  setZoom(newZoom);
}
function zoomOut (){
  const newZoom = currentZoom - stepSize;
  rangeVal.value = currentZoom;
  zoomSlider.value.style.background = `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(rangeVal.value-zoomSlider.value.min)/(zoomSlider.value.max-zoomSlider.value.min)*100}%, var(--tertiary50opacity) ${(rangeVal.value-zoomSlider.value.min)/(zoomSlider.value.max-zoomSlider.value.min)*100}%, var(--tertiary50opacity) 100%)`;
  setZoom(newZoom);
}

async function highResAmenityLoaded () {
  loaderProgress.value = 50;
  const highRes = await loadImageData(highResUrlProp.value, (progress) => {
    loaderProgress.value = 50+(progress/2);
  }, () => loaderProgress.value=false);
  circularloader.value = false;
  highResdata.value = highRes;
  await startTransition(highRes);
  await nextTick();
  updateImageDimensions();
  const container = document.getElementById("image-container");
  setZoomListener(container);
  highResVisible.value = true;
  setTimeout(() => {
    showLabels.value = true;
  }, 500);
  // Ensure high-res image matches low-res image dimensions
  const lowResImg = lowResImage.value;
  const nextImg = nextImageEl.value;
  if (lowResImg && nextImg) {
    nextImg.style.width = `${lowResImg.clientWidth}px`;
    nextImg.style.height = `${lowResImg.clientHeight}px`;
  }
}

watch(() => props.highResUrl, async (newVal) => {
  highResUrlProp.value = newVal;
  imageDimensionsDiv.value = null;
  containerStyle.value = {};
  // If the imageId or lowResUrl is the same, just swap in high-res, no transition
  if (props.currentImageId === activeItem.value) {
    // Just swap in high-res, no transition
    loaderProgress.value = 50;
    const highRes = await loadImageData(highResUrlProp.value, (progress) => {
      loaderProgress.value = 50 + (progress / 2);
    }, () => loaderProgress.value = false);
    circularloader.value = false;
    highResdata.value = highRes;
    currentImage.value = highRes;
    highResVisible.value = true;
    await nextTick();
    updateImageDimensions();
    setTimeout(() => {
      showLabels.value = true;
    }, 500);
  } else {
    // If it's a new image, use the transition logic
    highResAmenityLoaded();
  }
}, { deep: true, immediate: true });

watch(() => Store.unitplanData[route.params.unitplanId]?.hotspots, Store.unitplanData[props.currentFloorUnitplandId]?.hotspots, (newVal) => {
  localHotSpots.value = newVal;
}, { immediate: true });

watch(() => route.query.type, (newVal) => {
  localActiveNavigatorTab.value = newVal || 'unitplan';
  emit("activeNavigatorTabEmit", localActiveNavigatorTab.value);
});

watch(() => route.query.unit_id, () => {
  highResdata.value = false;
});

onMounted(() => {
  window.addEventListener('resize', updateImageDimensions);
  if (props.highResUrl && !highResdata.value) {
    highResAmenityLoaded();
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', updateImageDimensions);
});

function handleNextImageLoad () {
  if (nextImageEl.value) {
    nextImageEl.value.style.width = '';
    nextImageEl.value.style.height = '';
    // Also clear container style
    const container = document.getElementById("image-container");
    if (container) {
      container.style.width = '';
      container.style.height = '';
    }
    setTimeout(() => {
      getImageDimensions(nextImageEl.value);
    }, 50);
  }
  showNextImage.value = true;
  isTransitioning.value = true;
  setTimeout(() => {
    currentImage.value = nextImage.value;
    showNextImage.value = false;
    isTransitioning.value = false;
    imageLoaded.value = true;
    updateImageDimensions();
    isHotspotsTransitioning.value = false;
    highResVisible.value = true;
  }, 500);
}

</script>

<template>
  <ProgressBar
    v-if="(loaderProgress!==false && loaderProgress!==null) && localActiveNavigatorTab === 'unitplan'"
    class="z-10"
    :progress="loaderProgress"
  />
  <div
    v-if="route.query.type === 'unitplan' && !Store.isLandscape && iszoomenabled"
    id="controls"
    class="hidden md:inline-flex absolute bottom-[-70px] right-8 transform  z-[2] h-9 p-2 bg-secondary rounded-lg justify-start items-center gap-3"
  >
    <div
      id="zoom-out"
      class="relative  overflow-hidden cursor-pointer"
      @click="zoomOut"
    >
      <svg
        class="w-4 h-4"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          class="fill-primary"
          d="M18.8889 9H1.11111C0.816426 9 0.533811 9.15804 0.325437 9.43934C0.117063 9.72064 0 10.1022 0 10.5C0 10.8978 0.117063 11.2794 0.325437 11.5607C0.533811 11.842 0.816426 12 1.11111 12H18.8889C19.1836 12 19.4662 11.842 19.6746 11.5607C19.8829 11.2794 20 10.8978 20 10.5C20 10.1022 19.8829 9.72064 19.6746 9.43934C19.4662 9.15804 19.1836 9 18.8889 9Z"
        />
      </svg>
    </div>
    <input
      id="zoom-slider"
      ref="zoomSlider"
      v-model="rangeVal"
      type="range"
      min="1"
      max="3"
      step="0.01"
      class="w-[114px]"
      @input="sliderZoom"
    >
    <div
      id="zoom-in"
      class=" relative  overflow-hidden cursor-pointer"
      @click="zoomIn"
    >
      <svg
        class="w-4 h-4"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          class="fill-primary"
          d="M17.1111 9.11111H10.8889V2.88889C10.8889 2.65314 10.7952 2.42705 10.6285 2.26035C10.4618 2.09365 10.2357 2 10 2C9.76425 2 9.53816 2.09365 9.37146 2.26035C9.20476 2.42705 9.11111 2.65314 9.11111 2.88889V9.11111H2.88889C2.65314 9.11111 2.42705 9.20476 2.26035 9.37146C2.09365 9.53816 2 9.76425 2 10C2 10.2357 2.09365 10.4618 2.26035 10.6285C2.42705 10.7952 2.65314 10.8889 2.88889 10.8889H9.11111V17.1111C9.11111 17.3469 9.20476 17.573 9.37146 17.7397C9.53816 17.9064 9.76425 18 10 18C10.2357 18 10.4618 17.9064 10.6285 17.7397C10.7952 17.573 10.8889 17.3469 10.8889 17.1111V10.8889H17.1111C17.3469 10.8889 17.573 10.7952 17.7397 10.6285C17.9064 10.4618 18 10.2357 18 10C18 9.76425 17.9064 9.53816 17.7397 9.37146C17.573 9.20476 17.3469 9.11111 17.1111 9.11111Z"
        />
      </svg>
    </div>
  </div>
  <div class="w-[85%] h-[80%] relative flex justify-center items-center">
    <div
      v-if="circularloader && localActiveNavigatorTab === 'interior'"
      class="absolute z-10 top-0 left-0 w-full h-full bg-secondary bg-opacity-40 backdrop-blur-[20px] rounded-none flex transition-opacity duration-300"
    >
      <div class="m-auto loader border-4 border-gray-200 border-t-4 border-t-black rounded-full w-5 h-5 animate-spin" />
    </div>
    <div
      v-show="lowResData"
      id="image-container"
      :class="localActiveNavigatorTab === 'unitplan' ? 'image-container items-center flex' : 'h-full relative'"
      :style="containerStyle"
    >
      <!-- Base image container -->
      <div
        v-if="localActiveNavigatorTab === 'unitplan'"
        class="absolute inset-0 m-auto"
      >
        <!-- Current image -->
        <img
          ref="lowResImage"
          :src="currentImage"
          alt=""
          class="absolute inset-0 m-auto w-full h-full"
          :class="{'fade-out': isTransitioning}"
          style="object-fit: contain; opacity: 1; will-change: opacity; backface-visibility: hidden; transform: translateZ(0); z-index: 1;"
          @load="handleImageLoad"
        >
        <!-- Next image overlay -->
        <img
          v-if="showNextImage"
          ref="nextImageEl"
          :src="nextImage"
          alt=""
          class="absolute inset-0 m-auto w-full h-full"
          :class="{'fade-in': isTransitioning}"
          style="object-fit: contain; opacity: 1; will-change: opacity; backface-visibility: hidden; transform: translateZ(0); z-index: 2;"
          @load="handleNextImageLoad"
        >
      </div>
      <img
        v-else-if="localActiveNavigatorTab === 'interior'"
        ref="lowResImage"
        :src="highResdata ? highResdata : lowResData"
        alt=""
        class="relative m-auto"
        style=""
        @load="() => {
          highResLoaded.value = true;
          updateImageDimensions();
          setTimeout(() => { showLabels = true; }, 500);
        }"
      >

      <!-- Hotspots container -->
      <div
        v-if="localActiveNavigatorTab === 'unitplan' && imageDimensionsDiv && showLabels"
        :style="`width: ${imageDimensionsDiv.width}px; height: ${imageDimensionsDiv.height}px`"
        class="absolute m-auto flex left-2/4 -translate-x-1/2 transition-opacity duration-300"
        :class="{ 'opacity-0': isHotspotsTransitioning }"
        style="z-index: 3;"
      >
        <div
          v-for="(item, key) in localHotSpots"
          :key="key"
          class="flex flex-col absolute bg-[#2F2E2E80] text-white backdrop-blur-xl border rounded-lg self-stretch border-none px-[0.4rem] py-[0.2rem] items-center justify-center whitespace-nowrap text-center transition-opacity duration-300"
          :class="[(item.image_id || item.label_id) && 'cursor-pointer',
                   item.scale === 'small' ? (!Store.isMobile ? 'text-sm' : 'text-[8px]') :
                   item.scale === 'medium' ? (!Store.isMobile ? 'text-lg' : 'text-xs') :
                   item.scale === 'large' ? (!Store.isMobile ? 'text-2xl' : 'text-lg !py-[0.4rem]') : (!Store.isMobile ? 'text-sm' : 'text-[8px]')]"
          :style="{
            left: `${item.x}%`,
            top: `${item.y}%`
          }"
          @click="item.image_id ? setCurrentActiveEmit(item.image_id) : (item.label_id ? setCurrentActiveEmit(item.label_id) : null)"
        >
          <TranslationComp :text="item.text.split('\\n')[0] " />
          <span
            class="text-white font-bold leading-normal"
            :class="item.scale === 'small' ? 'text-sm' : item.scale === 'medium' ? 'text-lg' : item.scale === 'large' ? 'text-2xl' : 'text-sm'"
          >

            <TranslationComp
              v-show="item.text.split('\\n')[1] "
              :text="item.text.split('\\n')[1] "
            />
          </span>
        </div>
      </div>
    </div>
    <div
      v-if="!circularloader && localActiveNavigatorTab === 'interior' && lowResData && imageDimensionsDiv"
      class="absolute"
      :style="`width: ${imageDimensionsDiv.width}px; height: ${imageDimensionsDiv.height}px`"
    >
      <div
        v-for="(item, key) in localHotSpots"
        :key="key"
        class="flex flex-col absolute"
        :class="(item.image_id || item.label_id) && 'cursor-pointer'"
        :style="{
          left: `${item.x}%`,
          top: `${item.y}%`,
        }"
        @click="item.image_id ? setCurrentActiveEmit(item.image_id) : (item.label_id ? setCurrentActiveEmit(item.label_id) : null)"
      >
        <svg
          v-if="activeItem === item.image_id || activeItem === item.label_id"
          class="w-7 h-6"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 36 47"
          fill="none"
        >
          <path
            d="M17.3333 14.8333L27.1483 38.8333H7.51838L17.3333 14.8333Z"
            fill="url(#paint0_linear_5510_7871)"
          />
          <g filter="url(#filter0_d_5510_7871)">
            <circle
              cx="18"
              cy="12.1667"
              r="12"
              fill="#1C64F2"
            />
            <circle
              cx="18"
              cy="12.1667"
              r="11"
              stroke="white"
              stroke-width="2"
            />
          </g>
          <defs>
            <filter
              id="filter0_d_5510_7871"
              x="0"
              y="0.166687"
              width="36"
              height="36"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood
                flood-opacity="0"
                result="BackgroundImageFix"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="6" />
              <feGaussianBlur stdDeviation="3" />
              <feComposite
                in2="hardAlpha"
                operator="out"
              />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_5510_7871"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect1_dropShadow_5510_7871"
                result="shape"
              />
            </filter>
            <linearGradient
              id="paint0_linear_5510_7871"
              x1="17.3333"
              y1="14.8333"
              x2="17.3333"
              y2="38.2179"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#1C64F2" />
              <stop
                offset="1"
                stop-color="#1C64F2"
                stop-opacity="0.5"
              />
            </linearGradient>
          </defs>
        </svg>
        <svg
          v-else
          class="w-4 h-4"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            fill="#1A56DB"
            fill-opacity="0.7"
          />
          <circle
            cx="12"
            cy="12"
            r="6"
            fill="white"
          />
        </svg>
      </div>
    </div>
  </div>
</template>
<style scoped>
.image-container {
  position: relative;
  height: 100%;
  width: 65%;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
}
.image-container-default {
  position: relative;
  height: 100%;
  width: 65%;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
}
#image-container img:hover {
      cursor: zoom-in;
}

#zoom-slider {
  border-radius: 8px;
  height: 0.4rem;
  outline: none;
  -webkit-appearance: none;
  background: var(--tertiary50opacity);
}

input[type='range']::-webkit-slider-thumb {
  width: 1rem;
  -webkit-appearance: none;
  height: 1rem;
  background: var(--primary);
  border-radius: 100%;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-500 {
  transition-duration: 500ms;
}

/* Add new styles for smoother transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.hotspot-enter-active,
.hotspot-leave-active {
  transition: opacity 0.3s ease;
}

.hotspot-enter-from,
.hotspot-leave-to {
  opacity: 0;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out forwards;
}

.fade-out {
  animation: fadeOut 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  will-change: opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  transition: opacity 0.5s ease-in-out;
}

.image-container img[src],
.image-container img[data-src] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
