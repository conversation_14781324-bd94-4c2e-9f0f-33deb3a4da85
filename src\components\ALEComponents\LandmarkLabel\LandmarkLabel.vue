<script setup>
import { defineProps } from 'vue';
import { FwbBadge } from 'flowbite-vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
defineProps({
  name: {type: String, default: ""},
  color: {type: String, default: ""},
});

const emit = defineEmits(['viewLandmark']);

function handleLandmark () {
  emit('viewLandmark');
}
</script>

<template>
  <fwb-badge
    class="w-fit py-1.5 px-4 text-base font-medium rounded-md"
    :type="color"
    @click="handleLandmark"
  >
    <TranslationComp
      :text="name"
    />
  </fwb-badge>
</template>

<style lang="scss">

</style>
