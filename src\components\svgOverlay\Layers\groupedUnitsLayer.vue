<!-- groupedUnitsLayer.vue -->
<script setup>
import {defineProps, ref, onMounted, watch} from 'vue';
import { useRoute } from 'vue-router';
import { addSVGDeepZoom, setActiveElem, cdn } from '../../../helpers/helper';
import {creationToolStore} from '../../../store/index';
import OpenSeadragon from 'openseadragon';
import { fetchVideo } from '../../../helpers/API';

const Store = creationToolStore();
const route = useRoute();
const props = defineProps({
  layerData: {
    type: Object,
    default () {
      return {};
    },
  },
  sceneType: {
    type: String,
    default: '',
  },
});

// const layerData = ref({});
const clickedId = ref(false);
const groupSVGElem = ref({});
const groupTextElem = ref({});

fetchVideo(props.layerData, cdn(Store.SceneData[route.params.sceneId].sceneData.background?.low_resolution));

// Enhanced click handler for deep zoom functionality
function handleSVGClick (layer, event) {
  if (props.sceneType === 'deep_zoom') {
    event.preventDefault();
    event.stopPropagation();
    return;
  }
}
function getUnitData (unit) {
  if (unit) {
    return unit === '3.5BHK' ? 'fill-unit-b3.5-fill stroke-unit-b3.5-fill' : unit === '3BHK' ? 'fill-unit-b3-fill stroke-unit-b3-fill' : unit === '2.5BHK' ? 'fill-unit-b2.5-fill stroke-unit-b2.5-fill' : unit === '2BHK' ? 'fill-unit-b2-fill stroke-unit-b2-fill' : unit === '1BHK' ? 'fill-unit-b1-fill stroke-unit-b1-fill' : unit === '4BHK' ? 'fill-unit-b4-fill stroke-unit-b4-fill' : (unit === '5BHK' || unit === 'suite') ? 'fill-unit-b5-fill stroke-unit-b5-fill' : (unit === '6BHK' || unit === 'penthouse') ? 'fill-unit-b6-fill stroke-unit-b6-fill' : (Store.allUnitCardData[unit._id]?.bedroom === 'studio' || Store.allUnitCardData[unit._id]?.bedroom === 'duplex') ? 'fill-unit-s1-fill stroke-unit-s1-fill' : (unit === '7BHK' || unit === 'townhouse')? 'fill-unit-b7-fill stroke-unit-b7-fill' : 'fill-black stroke-gray-600 opacity-40';
  }
  return null;

}
function setSVG () {
  // At the beginning of setSVG()
  Object.values(groupSVGElem.value).forEach((obj) => {
    window.viewer.removeOverlay(obj.g); // remove old SVG group
  });
  Object.values(groupTextElem.value).forEach((obj) => {
    window.viewer.removeOverlay(obj.g); // remove old text label
  });
  groupSVGElem.value = {};
  groupTextElem.value = {};
  clickedId.value = false;

  if (Object.values(props.layerData).length > 0) {
    Object.values(props.layerData).forEach(async (item) => {

      const requestOptions = {
        method: "GET",
        redirect: "follow",
      };
      const response = await fetch(cdn(item.layer), requestOptions);
      const svgString = await response.text();
      const obj = addSVGDeepZoom({
        g: svgString,
        zIndex: item.layer_data.zIndex,
        reSize: item.layer_data.reSize,
        x: item.layer_data.x,
        y: item.layer_data.y,
        width: item.layer_data.width,
        height: item.layer_data.height,
        placement: item.layer_data.placement,
        layer_id: item.layer_data.layer_id,
      }, window.viewer);

      obj.svgElement.children[0].style.cursor= 'pointer';
      obj.svgElement.children[0].classList.add('!cursor-pointer', 'opacity-30', 'stroke-[0.2rem]');

      groupSVGElem.value[item.layer_data.layer_id] = {
        'g': obj.svgElement,
        'minZoomLevel': item.layer_data.minZoomLevel,
        'maxZoomLevel': item.layer_data.maxZoomLevel,
      };
      obj.svgElement.children[0].classList.add(...getUnitData(item.layer_data.bedrooms).split(' '));
      obj.svgElement.children[0].setAttribute("label",  item.layer_data.group_name ? item.layer_data.group_name : '');
      // Text Over SVG Layer
      const svgElement = document.createElement("span");
      svgElement.classList.add("font-medium", "rounded", "z-[1]");
      svgElement.classList.add("pointer-events-none", 'text-[10px]', 'p-[3px]', 'text-secondaryText', 'bg-secondary', "opacity-75");
      svgElement.id = item.layer_data.layer_id + 'label';
      svgElement.innerHTML = item.layer_data.group_name ? item.layer_data.group_name : '';
      // Calculate the center coordinates of the existing element
      const centerX = item.layer_data.x + (item.layer_data.width / 2);
      const centerY = item.layer_data.y + (item.layer_data.height / 2);
      window.viewer.addOverlay({
        element: svgElement,
        px: centerX,
        py: centerY,
        placement: "CENTER",
        checkResize: false,
      });
      groupTextElem.value[item.layer_data.layer_id] = {
        'g': svgElement,
        'minZoomLevel': item.layer_data.minZoomLevel,
        'maxZoomLevel': item.layer_data.maxZoomLevel,
      };

      new OpenSeadragon.MouseTracker({
        element: obj.svgElement,
        clickHandler: function () {

          if (item.layer_data.layer_id !== clickedId.value) {
            if (clickedId.value) {
              groupSVGElem.value[clickedId.value].g.classList.add('opacity-30');
              groupSVGElem.value[clickedId.value].g.classList.remove('transparent', 'opacity-70', '!fill-[#ffffff00]');
            }

            clickedId.value = item.layer_data.layer_id;
            obj.svgElement.children[0].classList.add('!fill-[#ffffff00]');

            const viewport = window.viewer.viewport;
            const viewerRect = window.viewer.element.getBoundingClientRect();
            const overlayRect = obj.svgElement.getBoundingClientRect(); // Get the element position and sizings

            // Calculate the center of the overlay in screen coordinates
            const overlayCenterX = overlayRect.left + (overlayRect.width / 2); // X
            const overlayCenterY = overlayRect.top + (overlayRect.height / 2); // Y

            // Convert the screen coordinates to viewport coordinates
            const viewportPoint = viewport.pointFromPixel(
              new OpenSeadragon.Point(
                overlayCenterX - viewerRect.left,
                overlayCenterY - viewerRect.top,
              ),
            );

            // Get the current center of the viewport
            const currentViewportCenter = viewport.getCenter();
            // Round the x and y values of both points to one decimal place for comparison
            const isSamePoint = (Math.round(currentViewportCenter.x * 10) / 10 === Math.round(viewportPoint.x * 10) / 10) &&
              (Math.round(currentViewportCenter.y * 10) / 10 === Math.round(viewportPoint.y * 10) / 10);

            // Check if the clicked point is already at the center of the viewport
            if (isSamePoint) {
              if (Store.currentZoomlevel < 2.5) {
                const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
                // Zoom in to the calculated viewport point
                viewport.zoomTo(zoomLevel, viewportPoint);
              } else {
                // obj.svgElement.classList.remove ('!fill-[#ffffff00]');
                obj.svgElement.classList.remove(getUnitData(item.layer_data.bedrooms));
                // svgElement.classList.add('!hidden')
              }
            }

            // Check if zooming is required
            if (Store.currentZoomlevel < 2.5) {
              // Define the zoom level you want to zoom into
              const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2

              // Zoom in to the calculated viewport point
              viewport.zoomTo(zoomLevel, viewportPoint);
            } else {
              // Pan to the clicked point
              viewport.panTo(viewportPoint);

              // Constrain the bounds to prevent panning beyond the image
              viewport.applyConstraints();
            }
          }
        },
      });
      clickedId.value = false;

      if (item.layer_data.minZoomLevel && item.layer_data.maxZoomLevel) {
        if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel <= item.layer_data.maxZoomLevel) {
          obj.svgElement.children[0].classList.add('!visible');
          obj.svgElement.children[0].classList.remove('!hidden');
        } else {
          obj.svgElement.children[0].classList.remove('!visible');
          obj.svgElement.children[0].classList.add('!hidden');
        }
      }
      // Hover effects for main SVG element
      obj.svgElement.children[0].addEventListener("mouseenter", () => {
        if (clickedId.value !== item.layer_data.layer_id && !clickedId.value) {
          obj.svgElement.children[0].classList.remove('opacity-30');
          obj.svgElement.children[0].classList.add('stroke-[0.2rem]', 'opacity-70');
        }
      },
      );
      obj.svgElement.children[0].addEventListener("mouseleave", () => {
        if (clickedId.value !== item.layer_data.layer_id && !clickedId.value) {
          obj.svgElement.children[0].classList.add('opacity-30');
          obj.svgElement.children[0].classList.remove('transparent', 'opacity-70', '!fill-[#ffffff00]');
        }
      });
    });
  }
}

onMounted(() => {
  if (props.sceneType === 'deep_zoom') {
    setSVG();
  }
});
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(groupSVGElem.value, Store.currentZoomlevel);
  setActiveElem(groupTextElem.value, Store.currentZoomlevel);
  if (props.sceneType === 'deep_zoom' && clickedId.value && Store.currentZoomlevel < 2.5) {
    setSVG();
  }
});
</script>

<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="(layer,index) in layerData"
    :id="layer.layer_data.layer_id"
    :key="index"
    :label="layerData?.group_name"
    :class="props.sceneType!=='deep_zoom'?[
      'cursor-pointer opacity-100' ,
      'hover:opacity-100 ',
      layer.layer.getAttribute('class') +' '+layer.layer_data.type,
      clickedId==layer.layer_data.layer_id && clickedId!=false?'fill-white':'fill-black'
    ]:['pointer-events-none']"
    @click="handleSVGClick(layer, $event)"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
  <portal to="pin">
    <div />
  </portal>
</template>

<style scoped>
</style>
