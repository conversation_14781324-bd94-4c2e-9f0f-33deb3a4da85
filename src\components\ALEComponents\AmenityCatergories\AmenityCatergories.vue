<script setup>
import { onMounted, ref, defineEmits, watch } from 'vue';
import { creationToolStore } from '../../../store';
import NearByFloatingButton from '../NearByFloatingButton/NearByFloatingButton.vue';

/* State and Props */
const props = defineProps({
  data: {type: Object, default () {
    return {};
  }},
  categories: {type: Object, default () {
    return {};
  }},
  defaultValue: {type: String, default: ""},
  amenityId: {type: String, default: ""},
  scrollIntoView: {type: String, default: ""},
});

const cardSelectionRef = ref(props.amenityId);
const emit = defineEmits(['gotoTour']);
const Store = creationToolStore();
const amenityCategory = ref(true);
const leftButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.375 10.5L2.625 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7 14.875L2.625 10.5L7 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const rightButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.625 10.5L18.375 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14 14.875L18.375 10.5L14 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;

/* Methods */
const updateSelectedCard = () => {
  if (props.defaultValue) {
    const index = Object.keys(props.categories).find((key) => props.categories[key].category.toLowerCase() === props.defaultValue.toLowerCase());
    if (index) {
      cardSelectionRef.value = index;
    }
  }
};

watch(() => props.defaultValue, updateSelectedCard);
watch(() => props.amenityId, (oldState) => {
  cardSelectionRef.value=oldState;
});

onMounted(() => {
  updateSelectedCard();
});

</script>

<template>
  <div
    class="fixed w-full flex justify-center "
    :class="Store.isMobile ? 'bottom-24' :Store.isLandscape? 'bottom-2': 'bottom-10'"
  >
    <NearByFloatingButton
      class="flex descriptionAnimation items-center bottom-0  absolute"
      :class="Store.isMobile ? 'w-full' : amenityCategory ? 'w-auto' : 'sm:w-[38%] md:w-[48%] lg:w-[60%] xl:[75%]'"
      :itemsList="data"
      :sliderButton="true"
      :active="cardSelectionRef"
      :leftButton="Store.isLandscape? '' :leftButton"
      :rightButton="rightButton"
      :objectIconKey="`icon`"
      :objectNameKey="`value`"
      :amenityCategory="amenityCategory"
      @goto-tour="(dataItem) => { emit('gotoTour', dataItem) }"
    />
  </div>
</template>

<style scoped>

.active-bg-linearGradient {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0.1%, rgba(0, 0, 0, 0.00), rgba(0, 0, 0, 0.00));
}
.inactive-bg-linearGradient {
  background: linear-gradient(to top, rgba(0, 0, 0, 7.83) 5%, rgba(0, 0, 0, 0.50) 90%, rgba(0, 0, 0, 0.30) 100%);
}
.contentContainer{
  transition: height 400ms linear;
  overflow: hidden;
}
.hide{
  height: 0px !important;
}
.descriptionAnimation
{
  animation:des-popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}

@keyframes des-popup
{
  0%
  {
    bottom :-24em;
  }

  100%
  {
    @apply md:bottom-8 ;

  }
}
</style>
