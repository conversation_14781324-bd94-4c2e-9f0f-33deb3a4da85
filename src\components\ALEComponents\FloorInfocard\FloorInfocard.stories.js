import FloorInfocard from "./FloorInfocard.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: "Design System/ALE/FloorInfocard",
  component: FloorInfocard,
  tags: ["autodocs"],
};

export const Primary = {
  args: {
    floor: 35,
    units: 6,
    area: "2500-3500 Sqft",
    minBedrooms: 3,
    maxBedrooms: 5,
  },
  parameters: {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=112-11328&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
