<template>
  <div
    ref="dropdownRef"
    class="dropdown max-w-fit"
  >
    <div
      class="flex items-center  cursor-pointer"
      @click="toggleDropdown"
    >
      <div class="overflow-hidden whitespace-nowrap w-20 text-sm text-ellipsis">
        {{ selectedOption.label }}
      </div>
      <svg
        class="w-6 h-6"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 12 6"
        fill="none"
      >
        <path
          d="M0.837631 1.29554L5.50016 5.73872C5.55567 5.79146 5.6159 5.82884 5.68086 5.85085C5.74582 5.87321 5.81548 5.88459 5.88985 5.88497C5.96422 5.88535 6.03399 5.87469 6.09918 5.85299C6.16436 5.83164 6.22497 5.79488 6.28102 5.74272L11.0027 1.34752C11.1334 1.22579 11.1993 1.07313 11.2002 0.889546C11.2012 0.705958 11.1323 0.548241 10.9935 0.416394C10.8547 0.284546 10.6924 0.218147 10.5065 0.217196C10.3206 0.216246 10.1575 0.280981 10.0174 0.411403L5.89823 4.24579L1.81846 0.369481C1.68894 0.246424 1.52899 0.184408 1.33862 0.183434C1.14786 0.182459 0.982433 0.247182 0.842324 0.377603C0.702216 0.508025 0.631714 0.660658 0.63082 0.835504C0.629926 1.01035 0.698863 1.1637 0.837631 1.29554Z"
          fill="#262626"
        />
      </svg>
    </div>
    <ul
      v-if="isOpen"
      class="dropdown-list "
    >
      <slot name="heading" />
      <div
        id="scroll-style"
        class="child-dropdown-li  scroll"
      >
        <li
          v-for="option in options"
          :key="option"
          :class="{ 'selected': option.value === selectedOption.value }"
          class="flex flex-row gap-2 text-sm "
          @click="selectOption(option)"
        >
          <!-- eslint-disable vue/no-v-html -->
          <div
            v-if="option.icon"
            class="h-4 w-4 flex justify-center align-middle"
            v-html="option.icon"
          />
          <!--eslint-enable-->
          <p>{{ option.label }}</p>
        </li>
      </div>
    </ul>
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted, onUnmounted } from 'vue';

const isOpen = ref(false);
const { options, defaultValue } = defineProps({'options': {type: Array, default () {
  return [];
}}, 'defaultValue': {type: String, default: ""}});
const selectedOption = ref(defaultValue?options.filter((option) => option.value===defaultValue):options[0]);

const dropdownRef = ref(null);

const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
};

const selectOption = (option) => {
  selectedOption.value = option;
  isOpen.value = false;
};

const closeDropdownOnOutsideClick = (event) => {
  if (isOpen.value && !dropdownRef.value.contains(event.target)) {
    isOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', closeDropdownOnOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
});

</script>

<style scoped>

  p{
    @apply flex items-center ;
  }
  svg
  {
    @apply w-5 h-5;
  }
  .selected {
    @apply text-[white] rounded-[3em] hover:bg-gray-400;
    background: #262626;
  }

  .selected:hover {
    background: #262626 !important;
  }

  .dropdown {
    @apply absolute;
  }

  .dropdown-list li {
    @apply cursor-pointer px-6 py-4  hover:bg-[#f0f0f0] hover:rounded-[2em] gap-6;

  }

  .scroll {
    @apply overflow-y-auto;
  }

  #scroll-style::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    background-color: #F5F5F5;
  }

  #scroll-style::-webkit-scrollbar {
    width: 8px;
    background-color: #F5F5F5;
    border-radius: 8px;
  }

  #scroll-style::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #555;
  }
</style>
