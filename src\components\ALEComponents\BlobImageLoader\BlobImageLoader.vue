<script setup>
import { onUnmounted, ref } from 'vue';
import { FwbImg } from 'flowbite-vue';
import { loadImageData } from '../../../helpers/helper';

const props = defineProps({
  thumbnail: {
    type: String,
    required: true,
  },
  url: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
});
const emit = defineEmits(['progressData']);
var xhr = null; // Xhr reference
const blob = ref(null); // Blob reference

if (blob.value===null){
  emit('progressData', 0);
}
// Function  loadImageData (imageUrl) {
//   Return new Promise((resolve, reject) => {
//     Xhr = new XMLHttpRequest();
//     Xhr.open('GET', imageUrl, true);
//     Xhr.responseType = 'blob';

//     Xhr.onload = function () {
//       If (xhr.status === 200) {
//         Const blob = xhr.response;
//         Const reader = new FileReader();
//         Reader.onloadend = function () {
//           Resolve(reader.result);
//         };
//         Reader.readAsDataURL(blob);
//       } else {
//         Reject(new Error(`Failed to fetch image. Status: ${xhr.status}`));
//       }
//     };

//     Xhr.onerror = function () {
//       Reject(new Error('Network error occurred while fetching image.'));
//     };

//     /*     Xhr.onabort = function () {
//       Reject(new Error('Request was aborted'));
//     }; */

//     Xhr.send();
//   });
// }

function abortRequest () {
  if (xhr && xhr.status !== 200) {
    // Abort the request
    xhr.abort();
  }
}

async function loadHighResImage (url) {
  try {
    const highResDataBlob = await loadImageData(url, (progress) => emit('progressData', (50+(progress/2))), () => emit('progressData', false));
    blob.value = highResDataBlob; // Update in local reference
  } catch (error) {
    // Console.error(`Failed to load high-res image for slide`, error);
  }
}

loadHighResImage(props.url); // Initial

function lowResImageLoaded (){
  emit('progressData', 50);
}

onUnmounted(() => {
  abortRequest();
});

</script>

<template>
  <div class="h-full w-full relative flex">
    <fwb-img
      v-if="blob === null"
      class="m-auto sm:h-full sm:w-auto h-auto w-full fade-in  "
      :src="thumbnail"
      :alt="name"
      @load="lowResImageLoaded"
    />
    <fwb-img
      v-else
      class="m-auto sm:h-full sm:w-auto h-auto w-full "
      :src="blob"
      :alt="name"
    />
  </div>
</template>

<style scoped>
.fade-in {
  -webkit-animation: fade-in 1s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
          animation: fade-in 1s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}
 @-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
