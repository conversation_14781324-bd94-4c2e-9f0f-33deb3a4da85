<script setup>
import { ref, defineEmits} from 'vue';
// import { addOrdinalSuffix } from '../../../helpers/helper';
import { thousandSeparator } from '../../../helpers/helper';
// import StatusPill from '../StatusPill/StatusPill.vue';
import { FwbButton } from 'flowbite-vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';
// import router from '../../../router';
const route = useRoute();
const Store = creationToolStore();

defineProps({
  floor: {type: Number, default: 0},
  availableUnits: {type: Number, default: 0},
  availabilityStatus: {type: String, default: ""},
  unit: {type: Number, default: 0},
  minArea: {type: Number, default: 0},
  maxArea: {type: Number, default: 0},
  measurementType: {type: String, default: ""},
  hideStatus: { type: String, default: "false" },
  bedrooms: { type: String, default: "" },
  towerName: {type: String, default: ""},
  projectName: {type: String, default: ""},
});

const emits = defineEmits(['closeModal', 'moveToScene']);
const showDetails = ref(false), showDefault = ref(), cardDetails = ref([]);
if (Store.projectCardData[route.params.projectId].projectSettings.ale.unit_card_customize_type){
  cardDetails.value = Store.projectCardData[route.params.projectId].projectSettings.ale.unitcard_config;
} else {
  showDefault.value = true;
}

// function formattedNumber (floor) {
//   const specialCases = ["g", "c1", "c2", "c3", "p1", "p2", "p3"];

//   // Check if the floor is one of the special cases
//   if (specialCases.includes(floor)) {
//     return `<span class="big-number text-4xl font-medium capitalize">${floor}</span>`;
//   }
//   // Extract the numeric part and the suffix
//   const number = addOrdinalSuffix(floor);
//   const numericPart = number.match(/\d+/)[0];
//   const suffixPart = number.replace(/\d+/, '');

//   // Wrap the numeric part in a span with a class for styling
//   return `<span class="big-number text-4xl font-medium">${numericPart}</span>${suffixPart}`;
// }

const moveToScene =() => {
  emits('moveToScene');
};

// const startDrag = (event) => {
//   if (event.type === 'mousedown') {
//     startY.value = event.clientY;
//   } else if (event.type === 'touchstart') {
//     startY.value = event.touches[0].clientY;
//   }
// };
// const endDrag = (event) => {
//   let endY;
//   if (event.type === 'mouseup') {
//     endY = event.clientY;
//   } else if (event.type === 'touchend') {
//     endY = event.changedTouches[0].clientY;
//   }
//   const dragDistance = endY - startY.value;
//   if (dragDistance < 0) {
//     showDetails.value = true;
//   } else if (dragDistance > 0) {
//     showDetails.value = false;
//   }
// };
const handleClick = () => {
  showDetails.value = !showDetails.value;
};

</script>

<template>
  <div
    class="!h-fit bg-secondary backdrop-blur-sm border border-floorCardBorder rounded-lg p-[1.25rem] flex flex-col gap-4 items-center bg-blend-lighten z-50 absolute"
    :class="
      Store.isMobile
        ? 'flex flex-col !w-screen absolute bottom-0 rounded-br-none rounded-bl-none rounded-t-[1.25rem] slide-animation'
        : Store.isLandscape && Store.sidebarOptions[route.params.projectId]
          ? 'min-w-[18rem] right-0 !bottom-0'
          : Store.isLandscape
            ? 'right-20'
            : 'min-w-[18rem] max-w-fit'"
  >
    <!-- header -->
    <div class="w-full flex flex-col">
      <div
        v-if="Store.isMobile"
        class="flex w-full justify-center"
        @click="handleClick()"
      >
        <span class="w-[2.82rem] h-[0.32rem] rounded-[1.25rem] bg-secondaryText" />
      </div>

      <div
        class="flex flex-col justify-between"
        :class="showDetails?'items-start':'items-center'"
      >
        <div class=" w-full flex flex-col items-start">
          <!-- eslint-disable vue/no-v-html -->
          <p class="text-secondaryText font-semibold text-base">
            <span class="font-semibold text-base">
              <TranslationComp
                text="FLOOR"
              />
            </span>
            {{ floor }}
          </p>
          <div class="flex text-xs font-normal text-secondaryText">
            <p class="mr-1">
              <TranslationComp
                :text="towerName"
              />
            </p> <span> , </span>
            <p class="ml-1">
              <TranslationComp
                :text="projectName"
              />
            </p>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="!Store.isMobile || showDetails"
      class="flex flex-col gap-3 w-full"
    >
      <div
        class=" flex justify-between items-center"
        :class="Store.isMobile || Store.isLandscape? 'gap-16': 'gap-2'"
      >
        <!-- bedrooms -->
        <div
          class="w-fit flex"
          :class="Store.isMobile || Store.isLandscape ? 'flex-col gap-4': 'flex-col gap-2'"
        >
          <div
            v-if="bedrooms && (cardDetails.bedrooms || showDetails)"
            class="flex items-center sm:gap-1.5 gap-2 ml-[2px]"
          >
            <svg
              class="w-5 h-5"
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.6666 12.1666H1.33325"
                stroke="#6B7280"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M14.6666 14.5V11.1667C14.6666 9.9096 14.6666 9.28107 14.2761 8.89053C13.8855 8.5 13.257 8.5 11.9999 8.5H3.99992C2.74284 8.5 2.1143 8.5 1.72378 8.89053C1.33325 9.28107 1.33325 9.9096 1.33325 11.1667V14.5"
                stroke="#6B7280"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M14 8.5V5.40705C14 4.94595 14 4.71541 13.8719 4.49769C13.7438 4.27997 13.5613 4.16727 13.1963 3.94189C11.7246 3.03319 9.93287 2.5 8 2.5C6.06711 2.5 4.27543 3.03319 2.80372 3.94189C2.43869 4.16727 2.25618 4.27997 2.12809 4.49769C2 4.71541 2 4.94595 2 5.40705V8.5H14ZM7.33333 8V6.80893C7.33333 6.55515 7.2952 6.47027 7.0998 6.37025C6.693 6.16195 6.1991 6 5.66667 6C5.13423 6 4.64037 6.16195 4.2335 6.37025C4.03814 6.47027 4 6.55515 4 6.80893V8H7.33333ZM12.0001 6.80893V8H8.66675V6.80893C8.66675 6.55515 8.70488 6.47027 8.90028 6.37025C9.30708 6.16195 9.80095 6 10.3334 6C10.8659 6 11.3597 6.16195 11.7665 6.37025C11.9619 6.47027 12.0001 6.55515 12.0001 6.80893Z"
                fill="#6B7280"
              />
              <rect
                x="2"
                y="9"
                width="12"
                height="3"
                fill="#6B7280"
              />
            </svg>
            <p
              class="text-colormix text-sm capitalize whitespace-nowrap"
              :class="Store.isMobile || Store.isLandscape? 'text-wrap':''"
            >
              <TranslationComp
                :text="bedrooms"
              />
            </p>
          </div>
        </div>

        <!-- units -->
        <div
          v-if="unit && (cardDetails.units || showDefault) "
          class="flex items-center sm:gap-1 gap-2"
        >
          <svg
            class="w-6 h-6"
            viewBox="0 0 24 24"
            fill="white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M11.4962 11.2299V5.06689H6.87399C6.02659 5.06689 5.33325 5.76023 5.33325 6.60764V11.2299H11.4962ZM13.037 11.2299H19.1999V6.60764C19.1999 5.76023 18.5066 5.06689 17.6592 5.06689H13.037V11.2299ZM11.4962 12.7706H5.33325V17.3928C5.33325 18.2402 6.02659 18.9336 6.87399 18.9336H11.4962V12.7706ZM13.037 12.7706V18.9336H17.6592C18.5066 18.9336 19.1999 18.2402 19.1999 17.3928V12.7706H13.037Z"
              fill="#CCCCCC"
            />
          </svg>
          <p class="text-colormix text-sm whitespace-nowrap">
            {{ unit }}
            <TranslationComp
              :text="unit == 1 ? 'Unit' : 'Units'"
            />
          </p>
        </div>
      </div>
      <!-- area -->
      <div
        v-if="minArea && maxArea &&( cardDetails.measurement || showDefault)"
        class="flex items-center sm:gap-1 gap-2 w-max"
      >
        <div>
          <svg
            class="w-6 h-6"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M5.24458 6.42941C5.0388 6.62136 4.71638 6.61015 4.52443 6.40437L3.48866 5.29398C3.30564 5.09778 3.30614 4.79326 3.4898 4.59766L4.52557 3.4945C4.71819 3.28935 5.04065 3.27919 5.2458 3.47181C5.45095 3.66444 5.46111 3.98689 5.26849 4.19205L5.01286 4.4643H10.7728L10.5187 4.19183C10.3267 3.98606 10.3379 3.66363 10.5437 3.47168C10.7495 3.27973 11.0719 3.29094 11.2639 3.49672L12.2996 4.60711C12.4827 4.80332 12.4822 5.10783 12.2985 5.30344L11.2627 6.40659C11.0701 6.61174 10.7477 6.6219 10.5425 6.42928C10.3374 6.23666 10.3272 5.9142 10.5198 5.70905L10.7733 5.43906H5.01758L5.26962 5.70926C5.46157 5.91504 5.45036 6.23746 5.24458 6.42941ZM3.33301 8.91179C3.33301 8.19388 3.91499 7.6119 4.6329 7.6119H10.7816C11.4995 7.6119 12.0815 8.19388 12.0815 8.91179V15.0487C12.0815 15.7666 11.4995 16.3486 10.7816 16.3486H4.6329C3.91499 16.3486 3.33301 15.7666 3.33301 15.0487V8.91179ZM13.2826 8.45827C13.0768 8.65022 13.0656 8.97264 13.2575 9.17842C13.4495 9.3842 13.7719 9.39541 13.9777 9.20346L14.2503 8.94914V15.0045L13.9795 14.7503C13.7744 14.5577 13.4519 14.5678 13.2593 14.773C13.0667 14.9781 13.0768 15.3006 13.282 15.4932L14.3851 16.529C14.5808 16.7126 14.8853 16.7131 15.0815 16.5301L16.1919 15.4943C16.3976 15.3024 16.4089 14.98 16.2169 14.7742C16.025 14.5684 15.7025 14.5572 15.4967 14.7491L15.2251 15.0025V8.94899L15.4949 9.20233C15.7001 9.39495 16.0225 9.38479 16.2151 9.17964C16.4078 8.97448 16.3976 8.65203 16.1925 8.45941L15.0893 7.42364C14.8937 7.23998 14.5892 7.23948 14.393 7.4225L13.2826 8.45827Z"
              fill="#CCCCCC"
            />
          </svg>
        </div>

        <p class="text-colormix text-sm">
          {{ thousandSeparator(minArea) }}

          <TranslationComp
            :text="measurementType==='sqmt' ? 'Sq mt': 'Sq ft'"
          />
          -
          {{ thousandSeparator(maxArea) }}
          <TranslationComp
            :text="measurementType==='sqmt' ? 'Sq mt': 'Sq ft'"
          />
        </p>
      </div>
    </div>

    <!-- view floor plan -->
    <div
      v-if="Store.isMobile || Store.isTouchScreen || Store.isLandscape"
      class="flex justify-center w-full"
      @click="moveToScene()"
    >
      <!-- <SvgButton
          theme="light"
          title="Enter Floor"
          class="flex  justify-end items-center backdrop-blur px-16 py-4 w-full rounded-[50px] bg-primary text-secondary"
        /> -->

      <fwb-button
        class="w-full bg-primary text-primaryText  hover:bg-primary hover:text-primaryText"
        size="md"
      >
        <TranslationComp
          text="View Floor Plan"
        />
      </fwb-button>
    </div>
  </div>

  <!-- <StatusPill
            v-if="!hideStatus"
            :availability-status="availabilityStatus"
            :available-units="availableUnits"
          />

                  <div class="flex gap-4 items-center">
           <div class="border border-[#14D56F] rounded-lg w-fit py-1 px-2.5 text-sm availability">
                            Available &#160;{{ formatSingleDigitNumber(available) }}
                        </div>

                        <div
            v-if="Store.isMobile"
            @click="emits('closeModal')"
          >
            <svg
              width="27"
              height="27"
              viewBox="0 0 27 27"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M20.25 20.25L6.75 6.75"
                stroke="white"
                stroke-width="1.575"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M20.25 6.75L6.75 20.25"
                stroke="white"
                stroke-width="1.575"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>-->
</template>

<style scoped>

@media screen and (max-width: 430px) {
.slide-animation
{
  animation:popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}
}

@keyframes popup
{
  0%
  {
    bottom :-24em;
  }
  100%
  {
    @apply md:bottom-0 ;
  }
}

svg path,rect{
  stroke:var(--colormix);
  fill:var(--colormix) !important;
}

.measurementsvg path{
  stroke:var(--colormix);
  fill:var(--colormix) !important;
}

</style>
