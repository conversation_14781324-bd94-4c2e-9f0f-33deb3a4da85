<script setup>
import { defineProps, onBeforeUnmount, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import router from '../router';
import { creationToolStore } from '../store';
import { listenVRTourPost } from '../helpers/helper';
import * as THREE from 'three';

import Matterport from '../components/ALEComponents/Matterport/MatterportView.vue';
import Pannellum from '../components/ALEComponents/PannellumComp/PannellumComp.vue';
import NavBar from '../components/ALEComponents/NavBar/NavBar.vue';
/* import AFrameViewer from '../components/ALEComponents/AFrame360/AFrameViewer.vue';  */
import CustomTour from '../components/ALEComponents/CustomTour/CustomTour.vue';

const props = defineProps({
  tourId: {
    type: String,
    default: "",
  },
  tourIdData: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['handleBackNavigation']);

const route = useRoute();
const Store = creationToolStore();
const loading = ref(false);
const error = ref(null);
const currentTourData = ref(null);

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();

if (!Store.organization_thumbnail) {
  Store.getOrganization(route.params.organizationId);
}
Store.getTranslation(route.params.organizationId);
if (Object.keys(Store.projectCardData).length === 0){
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}

if (!Store.unitplanData[route.params.unitplanId]){
  Store.getListOfUnitplan(route.params.projectId, route.params.organizationId);
}
Store.updateSceneType(route.fullPath);

const handleBackNavigation = () => {
  if (router.currentRoute.value.name !== "unitplansviewer") {
    router.push({
      name: 'unit.Child',
      query: {
        building_id: route.query.building_id,
        floor_id: route.query.floor_id,
        unit_id: route.query.unit_id,
      },
    }).then(() => {
      router.go(0);
    }).catch((err) => {
      console.error('Navigation error:', err);
      error.value = 'Failed to navigate back. Please try again.';
    });
  } else {
    emits('handleBackNavigation');
  }
};

const extractTourDataFromList = (tourDataObject, id) => {
  if (!tourDataObject || typeof tourDataObject !== 'object') {
    return false;
  }
  return tourDataObject[id] || false;
};

const initializeTourData = async () => {
  try {
    loading.value = true;

    if (!Store.listOfVrTourData) {
      await Store.getListOfVRId(route.params.organizationId, route.params.projectId);
    }

    const tourId = route.params.tourId || props.tourId;
    // console.log(tourId);
    // console.log("tour", Store.listOfVrTourData[tourId].images);
    if (Store.listOfVrTourData) {
      Store.vrtourData = Store.listOfVrTourData[tourId];
      // console.log("vrtourData", Store.vrtourData);
    }

    if (route.params.tourId && Store.listOfVrTourData) {
      currentTourData.value = extractTourDataFromList(Store.listOfVrTourData, route.params.tourId);
      if (currentTourData.value) {
        console.log('Tour data found:', currentTourData.value.type);
      } else {
        error.value = 'Tour not found';
      }
    }
  } catch (err) {
    console.error('Failed to initialize tour data:', err);
    error.value = 'Failed to load tour data. Please refresh the page.';
  } finally {
    loading.value = false;
  }
};

const handleTourMessage = (event) => {
  console.log('Raw message received:', event);

  if (!event.data?.type) {
    console.warn('Received malformed message:', event);
    return;
  }

  const { type, data } = event.data;
  console.log(`Processing message type: ${type}`, data);

  // Don't process if this is not a simulated message in guest mode
  const urlParams = new URLSearchParams(window.location.search);
  console.log("salestoolGuest param:", urlParams.get('salestoolGuest'));
  const isGuest = urlParams.get('salestoolGuest') === 'true';
  console.log("isGuest:", isGuest);
  console.log("%%%event.data.simulate", event.data.simulate);
  if (isGuest && !event.data.simulate) {
    return;
  }

  switch (type) {
    case 'cameraRotation':
      console.log("Camera rotation received:", data);
      if (event.data.simulate) {
        console.log("Camera rotation simulation received:", data);
        const camera = document.getElementById('camera');
        if (camera) {
          // Store original position
          camera.getAttribute('rotation');

          // Disable controls
          const lookControls = camera.components['look-controls'];
          if (lookControls) {
            lookControls.pause();
          }

          // Set new rotation
          const newRotation = {
            x: data.x,
            y: data.y,
            z: data.z,
          };

          camera.setAttribute('rotation', newRotation);

          // Update the look-controls internal state
          if (lookControls) {
            lookControls.pitchObject.rotation.x = THREE.Math.degToRad(data.x);
            lookControls.yawObject.rotation.y = THREE.Math.degToRad(data.y);
            lookControls.updateRotation();

            // Resume controls with a slight delay
            setTimeout(() => {
              lookControls.play();
            }, 100);
          }

          // Prevent component from sending a new rotation update
          if (camera.components['rotation-tracker']) {
            camera.components['rotation-tracker'].lastRotation = newRotation;
          }
        }
      }
      break;
    case 'imageChange':
      console.log('Image changed:', data);
      // Update the current image
      if (currentTourData.value && currentTourData.value.type === 'custom') {
        // Update the image in your AFrame viewer
        const aframeScene = document.querySelector('a-scene');
        if (aframeScene) {
          const sky = aframeScene.querySelector('a-sky');
          if (sky) {
            sky.setAttribute('src', data.url || data.thumbnail);
          }
        }
      }
      break;

    case 'loadingStatus':
      console.log('Loading status update:', data);
      // Handle loading status if needed
      break;

    case 'hotspotClick':
      console.log('Hotspot navigation:', data);
      break;

    case 'tourLoaded':
      console.log('Tour initialization:', data);
      break;

    default:
      console.warn('Unknown message type:', type);
  }
};

onBeforeUnmount(() => {
  window.removeEventListener('message', handleTourMessage);
});

// Merge these two onMounted hooks into one
onMounted(() => {
  window.addEventListener('message', handleTourMessage);
  listenVRTourPost();
  initializeTourData();
});
</script>

<template>
  <div :style="{ fontFamily: Store.loadedFont }">
    <div class="vr-viewer-container">
      <NavBar @handle-navigation="handleBackNavigation" />

      <div
        v-if="loading"
        class="loading-state"
      >
        Loading tour data...
      </div>

      <div
        v-else-if="error"
        class="error-state"
      >
        {{ error }}
      </div>

      <template v-else-if="currentTourData">
        <Matterport
          v-if="currentTourData.type === 'matterport'"
          :space-id="currentTourData.space_id"
        />

        <Pannellum
          v-else-if="currentTourData.type === 'panellum'"
          :tour-data="currentTourData.scenes"
        />

        <!--         <AFrameViewer
          v-else-if="currentTourData.type === 'custom'"
          :tour-data="currentTourData"
        /> -->

        <CustomTour
          v-else-if="currentTourData.type === 'custom'"
          :tour-data="currentTourData"
        />

        <iframe
          v-else-if="currentTourData.type === 'external'"
          id="showcase_frame"
          class="external-frame"
          :src="currentTourData.link"
          title="External VR Tour"
          allow="fullscreen"
        />

        <div
          v-else
          class="error-state"
        >
          Unsupported tour type: {{ currentTourData.type }}
        </div>
      </template>

      <div
        v-else
        class="error-state"
      >
        No tour data available
      </div>
    </div>
  </div>
</template>

<style scoped>
.vr-viewer-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.loading-state,
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 1rem;
  text-align: center;
}

.external-frame {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
