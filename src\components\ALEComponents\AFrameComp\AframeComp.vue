<script setup>
import { ref, onMounted, computed } from 'vue';
import { cdn, loadImageData } from '../../../helpers/helper';

const emit = defineEmits(['progressData', 'hotspotClick']);

const props = defineProps({
  sceneData: {
    type: Object,
    required: true,
  },
});

const highResDataLoaded = ref(false);

// Computed properties for easier access
const thumbnail = computed(() => props.sceneData.thumbnail);
const highResFile = computed(() => props.sceneData.file);
const rotation = computed(() => props.sceneData.rotation || "0 0 0");
const links = computed(() => props.sceneData.links || {});

const loadHighResImage = (imgURL) => {
  loadImageData(imgURL, (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false)).then((highRes) => {
    highResDataLoaded.value = highRes;
  });
};

let isDragging = false;
let dragStartTime = 0;

const handleHotspotClick = (linkData) => {
  console.log('Hotspot click detected, isDragging:', isDragging);
  console.log('Link data:', linkData);

  // Only emit if it's not a drag operation
  if (!isDragging) {
    console.log('Emitting hotspot click');
    emit('hotspotClick', linkData);
  } else {
    console.log('Click ignored due to dragging');
  }
};

const handleMouseDown = () => {
  isDragging = false;
  dragStartTime = Date.now();
};

const handleMouseMove = () => {
  if (Date.now() - dragStartTime > 100) { // 100ms threshold
    isDragging = true;
  }
};

const handleMouseUp = () => {
  setTimeout(() => {
    isDragging = false;
  }, 50); // Small delay to prevent immediate clicks after drag
};

onMounted(() => {
  emit('progressData', 0);
  loadHighResImage(cdn(highResFile.value));

  // Debug: Log links data
  console.log('Scene data:', props.sceneData);
  console.log('Links:', links.value);
  console.log('Number of links:', Object.keys(links.value).length);
});

</script>

<template>
  <a-scene
    embedded
    class="h-[100vh] sample"
    style="position: absolute; top: 0;"
    loading-screen="enabled: false"
    vr-mode-ui="enabled: false"
    device-orientation-permission-ui="enabled: false"
    assetloaded=""
    rotation-reader=""
    renderer="colorManagement: true; sortObjects: true; maxCanvasWidth: 1920; maxCanvasHeight: 1920;"
    gltf-model="dracoDecoderPath: https://propvr.tech/draco/"
    keyboard-rot=""
    inspector=""
    keyboard-shortcuts=""
    screenshot=""
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
    @touchstart="handleMouseDown"
    @touchmove="handleMouseMove"
    @touchend="handleMouseUp"
  >
    <a-assets>
      <img
        v-if="!highResDataLoaded"
        id="sky"
        :src="cdn(thumbnail)"
        crossorigin="anonymous"
        @load="()=>$emit('progressData',50)"
      >
      <img
        v-else
        id="skyHigh"
        :src="highResDataLoaded"
        crossorigin="anonymous"
      >
    </a-assets>

    <!-- Mouse cursor for hotspot interaction -->
    <a-entity
      cursor="rayOrigin: mouse; fuse: false"
      raycaster="objects: .hotspot"
    />

    <a-sky
      v-if="!highResDataLoaded"
      :src="'#sky'"
    />
    <a-sky
      v-else
      :src="'#skyHigh'"
    />

    <!-- Hotspots for links -->
    <template v-for="(link, linkId) in links" :key="linkId">
      <a-entity
        :position="`${link.position.x} ${link.position.y} ${link.position.z}`"
        class="hotspot"
        data-raycastable
        @click="handleHotspotClick(link)"
      >
        <!-- Hotspot visual indicator -->
        <a-sphere
          radius="0.3"
          color="#ff0000"
          opacity="1.0"
          animation="property: scale; to: 1.5 1.5 1.5; dir: alternate; dur: 1000; loop: true"
          material="transparent: false; emissive: #ff0000; emissiveIntensity: 0.5"
        />
        <!-- Hotspot glow effect -->
        <a-sphere
          radius="0.4"
          color="#ff0000"
          opacity="0.5"
          material="transparent: true; emissive: #ff0000; emissiveIntensity: 0.3"
          animation="property: scale; to: 2.0 2.0 2.0; dir: alternate; dur: 1500; loop: true"
        />
        <!-- Hotspot text label -->
        <a-text
          :value="link.text"
          position="0 0.3 0"
          align="center"
          color="#ffffff"
          background-color="#000000"
          background-opacity="0.8"
          width="6"
          wrap-count="15"
          font="dejavu"
        />
      </a-entity>
    </template>

    <a-entity
      camera
      look-controls="enabled: false"
      orbit-controls="minDistance: 2; maxDistance: 180; initialPosition: 0 5 90; rotateSpeed: 0.5"
      :rotation="rotation"
    />
  </a-scene>
</template>

<style scoped>
.hotspot {
  cursor: pointer;
}

.sample {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure hotspots are always visible */
.hotspot a-sphere {
  pointer-events: auto;
}
</style>
