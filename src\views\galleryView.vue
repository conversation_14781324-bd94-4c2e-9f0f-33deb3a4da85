<script setup>
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import router from '../router';
import { creationToolStore } from '../store/index';
// Import FavoriteView from '../components/ALEComponents/FavoriteView/FavoriteView.vue';
import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
// Import BackTopBar from '../components/ALEComponents/BackBar/BackTopBar.vue';
import BlobImageLoader from '../components/ALEComponents/BlobImageLoader/BlobImageLoader.vue';
// Import LoaderComponent from '../components/ALEComponents/LoaderComponent/LoaderComponent.vue';
import { cdn, Googleanalytics } from '../helpers/helper';
import OverflowSlider from '../components/ALEComponents/OverflowSlider/OverflowSlider.vue';
import GalleryVideoPlayer from '../components/ALEComponents/GalleryVideoPlayer/GalleryVideoPlayer.vue';
import AframeComp from '../components/ALEComponents/AFrameComp/AframeComp.vue';
import NavBar from '../components/ALEComponents/NavBar/NavBar.vue';
// Import { fetchImageURL } from '../helpers/API';
import PDFViewer from '../components/ALEComponents/PDFViewer/PDFViewer.vue';
import PannellumMultiRes from '../components/ALEComponents/pannellumMultiRes/PannellumMultiRes.vue';
import Video360 from '../components/ALEComponents/AframeVideo360/AframeVideo360.vue';
import NearByFloatingButton from '../components/ALEComponents/NearByFloatingButton/NearByFloatingButton.vue';
import DropDown from '../components/ALEComponents/DropDown/DropDown.vue';
import ProgressBar from '../components/ALEComponents/ProgressBar/ProgressBar.vue';
import CircularLoader from '../components/ALEComponents/CircularLoader/CircularLoader.vue';
// Import { FwbBadge } from 'flowbite-vue';
const route = useRoute();
// Var showTooltip = ref([]), checkAutoExit = ref(false);
const emit = defineEmits(['onClick']);
defineProps({
  sceneId: {type: String, default: ""},
  organizationId: {type: String, default: ""},
  projectId: {type: String, default: ""},
  amenityId: {type: String, default: ""},
});
const showLoader=ref(true), circularloader=ref(false);
// Const request = null;
const Store = creationToolStore();
const showSlider = ref(false);
// Const listofCategories = ref(null);
const selectedCategory = ref(null);
const selectionId = ref(route.query.id);
// Const closeFullscreenModal = ref(false);
const categoriesFilteredList = ref({});
const currentSliderIndex = ref(null);
// Const xhrStatus = ref(null);
const categoryList = ref([]);
const initialIndex = ref(0);

Store.getTranslation(route.params.organizationId);
const leftButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.375 10.5L2.625 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7 14.875L2.625 10.5L7 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const rightButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.625 10.5L18.375 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14 14.875L18.375 10.5L14 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const loaderProgress = ref(null);
Store.hideLogo=true;

if (!Store.sidebarOptions[route.params.projectId]) {
  Store.getOptions(route.params.organizationId, route.params.projectId).then((res) => {
    Store.activeOptionId=Object.values(res).filter((item) => item.name.toLowerCase() === "gallery")[0]._id;
  });
} else {
  Store.activeOptionId=Object.values(Store.sidebarOptions[route.params.projectId]).filter((item) => item.name.toLowerCase() === "gallery")[0]._id;
}
if (!Store.organization_thumbnail) {
  Store.getOrganization(route.params.organizationId);
}
if (!Store.amenityCategories) {
  Store.getCategories(route.params.organizationId, route.params.projectId);
}
setTimeout(() => {
  showSlider.value=true;
}, 1000);

if (Object.keys(Store.projectCardData).length === 0){
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}
Store.updateSceneType(route.fullPath);

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();

const setUpReferenceValues = (categorieslist) => {
  if (!selectionId.value) {
    selectionId.value = categoriesFilteredList.value[categorieslist[0]][0]._id;
    router.push({name: 'galleryview', query: {id: selectionId.value }});
    currentSliderIndex.value =  categoriesFilteredList.value[categorieslist[0]].findIndex((item) => item._id === selectionId.value) + 1;
    selectedCategory.value = Store.galleryList[selectionId.value].category;
  } else {
    selectedCategory.value = Store.galleryList[selectionId.value].category;
    currentSliderIndex.value =  categoriesFilteredList.value[selectedCategory.value].findIndex((item) => item._id === selectionId.value) + 1;
  }
};

// List of Gallyeries
const getlist = (list) => {
  if (Object.keys(Store.galleryList).length === 0){
    Store.getGallery(route.params.organizationId, route.params.projectId).then(() => {
      Object.values(Store.galleryList).forEach((item) => {
        if (list.includes(item.category)) {
          if (! categoriesFilteredList.value[item.category]) {
            categoriesFilteredList.value[item.category] = [];
          }
          if (!categoriesFilteredList.value[item.category].filter((gallery) => gallery._id===item._id).length) {
            categoriesFilteredList.value[item.category].push(item);
          }
        }
      });

      //  A temporary object to store sorted categories
      const tempCategoriesFilteredList = {};

      list.forEach((category) => {
        if (categoriesFilteredList.value[category]) {
          tempCategoriesFilteredList[category] = categoriesFilteredList.value[category];
        }
      });

      // sorted categories
      categoriesFilteredList.value = tempCategoriesFilteredList;
      categoryList.value = Object.keys(categoriesFilteredList.value);
      setUpReferenceValues(list); // Set up the reference values [selectionId,selectedCategory and currentSliderIndex]
      showLoader.value = false;
    });
  } else {
    Object.values(Store.galleryList).forEach((item) => {
      if (list.includes(item.category)) {
        if (! categoriesFilteredList.value[item.category]) {
          categoriesFilteredList.value[item.category] = [];
        }
        if (!categoriesFilteredList.value[item.category].filter((gallery) => gallery._id===item._id).length) {
          categoriesFilteredList.value[item.category].push(item);
        }
      }
    });

    //  A temporary object to store sorted categories
    const tempCategoriesFilteredList = {};
    list.forEach((category) => {
      if (categoriesFilteredList.value[category]) {
        tempCategoriesFilteredList[category] = categoriesFilteredList.value[category];
      }
    });
    // sorted categories
    categoriesFilteredList.value = tempCategoriesFilteredList;

    categoryList.value = Object.keys(categoriesFilteredList.value);
    setUpReferenceValues(list); // Set up the reference values [selectionId,selectedCategory and currentSliderIndex]
    showLoader.value = false;
  }
};

const resetCategories = () => {
  categoriesFilteredList.value = {};
  categoryList.value = [];
  selectedCategory.value = null;
  selectionId.value = null;
  currentSliderIndex.value = null;
};

const getCategories = (projectCardData) => {
  resetCategories(); // Always reset before loading new categories

  if (projectCardData && projectCardData.projectSettings && projectCardData.projectSettings.gallery) {
    const dataEntries = Object.values(projectCardData.projectSettings.gallery)
      .filter((obj) => typeof obj.order === 'number' && obj.name); // only valid entries

    //  sort by order, then by name
    dataEntries.sort((a, b) => {
      if (a.order === b.order) {
        return a.name.localeCompare(b.name);
      }
      return a.order - b.order;
    });

    const uniqueNames = dataEntries.map((obj) => obj.name);
    getlist(uniqueNames);
  } else {
    Store.getGalleryCategories(route.params.organizationId, route.params.projectId).then((res) => {
      getlist(res.data);
    });
  }
};

getCategories(Store.projectCardData[route.params.projectId]); // Get categories initialize

// Watch for project change and reload categories
watch(
  () => route.params.projectId,
  (newProjectId, oldProjectId) => {
    if (newProjectId !== oldProjectId) {
      getCategories(Store.projectCardData[newProjectId]);
    }
  },
);

function moveToLocation (id, type){
  if (type==='masterscene'){
    router.push({name: 'masterScene', params: {sceneId: id} });
  } else if (type==='earth'){
    router.push({name: 'globeScene'});
  } else if (type==='projectscene') {
    router.push({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    router.push({ name: 'unitplansviewer'});
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push( { name: 'galleryview'} );
  }
}

const fullScreenChangeHandler = () => {
  if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
    emit('onClick');
    Store.isFullScreen = false;
  }
};

watch(() => route.query.id, (val) => {
  selectionId.value = val;
});

const handleCategoriesChange = (item ) => {
  if (item===selectedCategory.value){
    return;
  }
  showLoader.value = true;
  initialIndex.value = 0;

  currentSliderIndex.value = 1;

  setTimeout(() => {
    selectedCategory.value=item;
    router.replace({name: 'galleryview', query: {id: categoriesFilteredList.value[item][0]._id}});
    showLoader.value = false;
  }, 100);
};

const handleGalleryImageChange = (item, index) => {
  currentSliderIndex.value = index + 1;
  router.replace({name: 'galleryview', query: {id: item._id}});
};

const handleVideoLoader = () => {
  setTimeout(() => {
    circularloader.value = false;
  }, 2000);
};

// Const handlePrev = () => {
//   Document.getElementById('galleryImagesSlider').swiper.slidePrev(); // Previous
// };

// Const handleNext = () => {
//   Document.getElementById('galleryImagesSlider').swiper.slideNext(); // Next
// };

function slideTo (item) {
  initialIndex.value = categoriesFilteredList.value[selectedCategory.value].findIndex((element) => element._id === item.id);
  document.getElementById('galleryImagesSlider').swiper.slideTo(initialIndex.value, 0);
}

const movetoprevscene=() => {
  router.go(-1);
};

function NearByFloatingButtonFilterData (data) {
  const list = [];
  for (let i=0; i<data.length; i++){
    if (data[i].type === 'image'){
      list.push({
        icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5 15.4788C5.10725 16.5691 5.34963 17.303 5.89742 17.8507C6.87997 18.8333 8.46133 18.8333 11.6241 18.8333C14.7868 18.8333 16.3682 18.8333 17.3507 17.8507C18.3333 16.8682 18.3333 15.2868 18.3333 12.1241C18.3333 8.96133 18.3333 7.37997 17.3507 6.39742C16.803 5.84963 16.0691 5.60725 14.9788 5.5" stroke="#6B7280" stroke-width="1.5"/>
<path d="M1.66663 8.83335C1.66663 5.69065 1.66663 4.11931 2.64293 3.143C3.61925 2.16669 5.19059 2.16669 8.33329 2.16669C11.476 2.16669 13.0474 2.16669 14.0236 3.143C15 4.11931 15 5.69065 15 8.83335C15 11.976 15 13.5474 14.0236 14.5237C13.0474 15.5 11.476 15.5 8.33329 15.5C5.19059 15.5 3.61925 15.5 2.64293 14.5237C1.66663 13.5474 1.66663 11.976 1.66663 8.83335Z" stroke="#6B7280" stroke-width="1.5"/>
<path d="M4.16663 15.5C7.01713 11.5406 10.2205 6.28959 15 10.2278" stroke="#6B7280" stroke-width="1.5"/>
</svg>`,
        value: data[i].name,
        id: data[i]._id,
      });
    } else if (data[i].type === 'video'){
      list.push({
        icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.7421 11.205C15.4475 12.3242 14.0555 13.115 11.2714 14.6968C8.57996 16.2258 7.23429 16.9903 6.14982 16.683C5.70146 16.5559 5.29295 16.3147 4.96349 15.9822C4.16663 15.1782 4.16663 13.6188 4.16663 10.5C4.16663 7.38117 4.16663 5.82175 4.96349 5.01777C5.29295 4.68538 5.70146 4.44407 6.14982 4.31702C7.23429 4.00971 8.57996 4.77423 11.2714 6.30328C14.0555 7.88498 15.4475 8.67583 15.7421 9.795C15.8637 10.257 15.8637 10.743 15.7421 11.205Z" stroke="#6B7280" stroke-width="1.5" stroke-linejoin="round"/>
</svg>
`,
        value: data[i].name,
        id: data[i]._id,
      });
    } else if (data[i].type === '360_video'){
      list.push({
        icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.0309 10.9218C19.5182 6.33548 18.3845 3.22238 14.129 3C11.036 3.05905 7.84555 4.72552 5.29889 7.30361C3.16169 9.46717 1.05739 12.5658 1.82988 15.515C2.00045 16.1662 2.33304 16.6098 2.85842 17.0273C4.2703 18.1493 5.65613 18.248 8.32522 17.5927C11.0282 16.7714 12.704 15.534 14.117 14.2681M14.117 14.2681C14.1184 14.2668 14.1199 14.2654 14.1214 14.2641C14.124 14.2617 14.1223 14.2573 14.1187 14.2573C14.116 14.2573 14.1141 14.26 14.115 14.2625C14.1157 14.2644 14.1163 14.2663 14.117 14.2681ZM14.117 14.2681C14.4256 15.1808 14.2301 15.9953 13.6987 17.5927" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
        value: data[i].name,
        id: data[i]._id,
      });
    } else if (data[i].type === '360_image'){
      list.push({
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
  <path d="M17.0309 10.9218C19.5182 6.33548 18.3845 3.22238 14.129 3C11.036 3.05905 7.84555 4.72552 5.29889 7.30361C3.16169 9.46717 1.05739 12.5658 1.82988 15.515C2.00045 16.1662 2.33304 16.6098 2.85842 17.0273C4.2703 18.1493 5.65613 18.248 8.32522 17.5927C11.0282 16.7714 12.704 15.534 14.117 14.2681M14.117 14.2681C14.1184 14.2668 14.1199 14.2654 14.1214 14.2641C14.124 14.2617 14.1223 14.2573 14.1187 14.2573C14.116 14.2573 14.1141 14.26 14.115 14.2625C14.1157 14.2644 14.1163 14.2663 14.117 14.2681ZM14.117 14.2681C14.4256 15.1808 14.2301 15.9953 13.6987 17.5927" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
        value: data[i].name,
        id: data[i]._id,
      });
    } else {
      list.push({
        icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5 15.4788C5.10725 16.5691 5.34963 17.303 5.89742 17.8507C6.87997 18.8333 8.46133 18.8333 11.6241 18.8333C14.7868 18.8333 16.3682 18.8333 17.3507 17.8507C18.3333 16.8682 18.3333 15.2868 18.3333 12.1241C18.3333 8.96133 18.3333 7.37997 17.3507 6.39742C16.803 5.84963 16.0691 5.60725 14.9788 5.5" stroke="#6B7280" stroke-width="1.5"/>
<path d="M1.66663 8.83335C1.66663 5.69065 1.66663 4.11931 2.64293 3.143C3.61925 2.16669 5.19059 2.16669 8.33329 2.16669C11.476 2.16669 13.0474 2.16669 14.0236 3.143C15 4.11931 15 5.69065 15 8.83335C15 11.976 15 13.5474 14.0236 14.5237C13.0474 15.5 11.476 15.5 8.33329 15.5C5.19059 15.5 3.61925 15.5 2.64293 14.5237C1.66663 13.5474 1.66663 11.976 1.66663 8.83335Z" stroke="#6B7280" stroke-width="1.5"/>
<path d="M4.16663 15.5C7.01713 11.5406 10.2205 6.28959 15 10.2278" stroke="#6B7280" stroke-width="1.5"/>
</svg>`,
        value: data[i].name,
        id: data[i]._id,
      });
    }
  }
  return list;
}

const handleBackNavigation =() => {
  if (route.query.islocate === 'true'){
    router.go(-1);
  } else {
    router.go(-2);
  }
};

const callGoogleanalytics = () => {

  Googleanalytics("experience_start", {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  Store.isExperienceStartEventSent = true;
};

onMounted(() => {
  document.addEventListener('fullscreenchange', fullScreenChangeHandler);
  document.addEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('MSFullscreenChange', fullScreenChangeHandler);

  watch(
    () => [Store.organizationDetails, Store.projectCardData],
    ([newOrgDetails, newProjectCardData]) => {
      if (
        newOrgDetails && Object.keys(newOrgDetails).length > 0 &&
        newProjectCardData && Object.keys(newProjectCardData).length > 0
      ) {
        if (!Store.isExperienceStartEventSent && !Store.isNavigatedFromMasterScene) {
          callGoogleanalytics();
        }
      }
    },
    { immediate: false, deep: true },
  );
});

onUnmounted(() => {
  // Cleanup event listener when component is destroyed
  document.removeEventListener('fullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('MSFullscreenChange', fullScreenChangeHandler);
  Store.hideLogo=false;

//   Const projectId = route.params.projectId;
//   Const projectCardData = Store.projectCardData[projectId];
//   If (projectCardData) {
//     ProcessGalleryData(projectCardData.projectSettings.gallery);
//   } else {
//     FetchGalleryData();
//   }
});

</script>

<template>
  <CircularLoader />

  <div :style="{ fontFamily: Store.loadedFont }">
    <NavBar
      v-if="!Store.isMobile"
      @handle-navigation="handleBackNavigation"
    />
    <ProgressBar
      v-if="loaderProgress!==false && loaderProgress!==null"
      :progress="loaderProgress"
      class="z-30"
    />
    <div class="absolute top-[50%] z-[6]">
      <NewAleSideBar
        v-if="categoriesFilteredList && selectedCategory && (Store.isMobile? categoriesFilteredList[selectedCategory].filter((item) => item._id === selectionId)[0]?.type == 'pdf'?false:true:true)"
        :sidebarList="Store.sidebarOptions[route.params.projectId]"
        @select-option="moveToLocation"
      />
    </div>
    <div
      class="absolute w-full top-0 z-[5] sm:hidden"
    >
      <NavBar
        @handle-navigation="movetoprevscene"
      />
    </div>

    <!-- PopUpMessageBox -->
    <!--   <PopUpMessageBox
    v-if="!closeFullscreenModal && !getCookie('fullscreen')"
    message="Enter to full screen mode for best experience"
    button="Enter"
    @close-x="setFullScreenCookie"
    @enter="onClickButton('fullscreen')"
  /> -->

    <!-- Categories -->
    <!-- Mobile view -->
    <!-- <div
    v-if="listofCategories && Object.keys(Store.galleryList).length > 0"
    class="sm:hidden block py-3 font-medium w-full fixed top-12 z-10"
  >
    <OverflowSlider
      slides-per-view="auto"
      direction="horizontal"
      :mousewheel="true"
      space-between="12"
      class="w-full pl-3"
      :initial-slide="selectedCategory ? listofCategories.findIndex((e)=>e==selectedCategory) : 0"
    >
      <template #options>
        <swiper-slide
          v-for="item,index in listofCategories"
          :key="index"
          :class="['rounded-[50px] h-11 px-4 text-[14px] w-fit  flex justify-center items-center bg-black/opacity-40 backdrop-blur-[20px] font-medium capitalize hover:cursor-pointer border border-white',(!selectedCategory ? listofCategories[0] === item ? 'bg-white' : 'bg-black bg-opacity-40 backdrop-blur-[20px] text-green-800' : selectedCategory === item ? 'bg-white' : 'bg-black bg-opacity-40 backdrop-blur-[20px] text-red-800')]"
          @click="() => {if(item==selectedCategory) router.rereturn; place({name:'galleryview',query:{category:item}}); selectedCategory=item; showLoader=true}"
        >
          {{ `${!isNaN(item.split("")[0]) ? `${item.split("")[0]} ${item.slice(1)}` : item}` }}
        </swiper-slide>
      </template>
    </OverflowSlider>
  </div> -->

    <!-- System view -->
    <!-- <div
    v-if="Store.projectCardData[route.params.projectId] || Store.galleryCategories"
    :class="['hidden sm:block sm:w-[60%] h-[61px] sm:-translate-x-[-33%] w-full left-0 transition-all z-20 transition-timing-function:cubic-bezier(0.45, 0.05, 0.55, 0.95); duration-200', 'sm:bottom-4 bottom-auto' , 'fixed']"
  >
    <div
      v-if="categoryList.length > 0"
      class=" font-medium w-full flex justify-center items-center"
    >
      <OverflowSlider
        slides-per-view="auto"
        direction="horizontal"
        :mousewheel="true"
        space-between="10"
        class="w-auto"
        :initial-slide="selectedCategory ? categoryList.findIndex((e)=>e==selectedCategory) : 0"
      >
        <template #options>
          <swiper-slide
            v-for="item,index in categoryList"
            :key="index"
            :class="['rounded-[50px] h-11 px-4 text-[14px] w-fit flex items-center justify-center capitalize hover:cursor-pointer border',(selectedCategory == item ? ' border-white bg-white text-black bg-primary ' : 'border-transparent text-orange-600 bg-black bg-opacity-40 backdrop-blur-[20px]')]"
            @click="handleCategoriesChange(item)"
          >
            {{ `${!isNaN(item.split("")[0]) ? `${item.split("")[0]} ${item.slice(1)}` : item}` }}
          </swiper-slide>
        </template>
      </OverflowSlider>
    </div>
  </div> -->

    <div
      v-if="!showLoader && Object.keys(categoriesFilteredList).length > 0 && selectedCategory"
      id="maincompview"
      class=""
    >
      <OverflowSlider
        v-if="selectedCategory"
        id="galleryImagesSlider"
        slidesPerView="1"
        direction="horizontal"
        space-between="3"
        class=" absolute top-0 left-0 w-full h-full "
        :initial-slide="selectionId ? categoriesFilteredList[selectedCategory].findIndex((item) => item._id === selectionId) : 0"
        Keyboard="true"
        allow-touch-move="false"
        @swiper-slide-change="(val) => handleGalleryImageChange(categoriesFilteredList[selectedCategory][val.detail[0].realIndex],val.detail[0].realIndex)"
      >
        <template #options>
          <swiper-slide
            v-for="item,index in categoriesFilteredList[selectedCategory]"
            :key="index"
            class="relative"
          >
            <!-- Image -->
            <div
              v-if="item.type === 'image'"
              class="relative overflow-hidden bg-no-repeat text-center h-full w-full bg-cover"
              :style="{ backgroundImage: `url('${cdn(item.thumbnail)}')` }"
            >
              <div
                class="h-full w-full bg-black bg-opacity-40 backdrop-blur-[20px]"
              >
                <div
                  v-if="index === (currentSliderIndex - 1)"
                  class="flex justify-center items-center h-full w-full relative"
                >
                  <!-- <div class="absolute sm:top-5 top-20 z-10">
                  <fwb-badge
                    size="lg"
                    class="bg-white h-10 text-gray-800 rounded-full px-4 text-sm shadow-[rgb(38, 57, 77) 0px 20px 30px -10px] "
                  >
                    {{ item.name }}
                  </fwb-badge>
                </div> -->
                  <BlobImageLoader
                    :name="item.name"
                    :thumbnail="cdn(item.thumbnail)"
                    :url="cdn(item.url)"
                    @progress-data="(progress)=>{loaderProgress=progress;}"
                  />
                </div>
              </div>
            </div>

            <!-- video -->
            <div
              v-if="item.type === 'video'"
              class="h-full w-full flex items-center justify-center"
            >
              <div
                v-if="(currentSliderIndex - 1) === index"
                class="h-full w-full flex items-center justify-center relative"
              >
                <div
                  v-if="circularloader"
                  class="z-10 absolute top-0 left-0 h-full w-full bg-black bg-opacity-40 backdrop-blur-[20px] flex"
                >
                  <div class="m-auto loader border-4 border-gray-200 border-t-4 border-t-black rounded-full w-12 h-12 animate-spin" />
                </div>

                <div class="h-full w-full flex items-center justify-center">
                  <GalleryVideoPlayer
                    :url="item.url"
                    @handle-loading-complete="handleVideoLoader"
                  />
                </div>
              </div>
            </div>

            <!-- 360-video -->
            <div
              v-if="item.type === '360_video'"
              class="h-full w-full flex items-center justify-center"
            >
              <div
                v-if="(currentSliderIndex - 1) === index"
                class="h-full w-full flex items-center justify-center relative"
              >
                <Video360
                  :videoSrc="item.url"
                  :thumbnailSrc="item.thumbnail"
                />
              </div>
            </div>

            <!-- 360-image -->
            <div
              v-if="item.type==='360_image'"
              class="overflow-hidden bg-cover bg-no-repeat text-center h-full sm:mt-0"
            >
              <div v-if="(currentSliderIndex - 1) === index">
                <AframeComp
                  :thumbnail="item.thumbnail"
                  :highRes="item.url"
                  @progress-data="(progress)=>{loaderProgress=progress;}"
                />
              </div>
            </div>
            <!-- multires -->
            <div
              v-if="item.type === 'multires'"
              class="h-full w-full"
            >
              <div
                v-if="(currentSliderIndex - 1) === index"
                class="h-full w-full"
              >
                <PannellumMultiRes
                  :type="item.type"
                  :basepath="item.multi_res.basepath"
                  :path="item.multi_res.path"
                  :fallbackPath="item.multi_res.fallbackPath"
                  :extension="item.multi_res.extension"
                  :tileResolution="item.multi_res.tileResolution"
                  :maxLevel="item.multi_res.maxLevel"
                  :cubeResolution="item.multi_res.cubeResolution"
                />
              </div>
            </div>

            <!-- embed-link -->
            <div
              v-if="item.type === 'embed_link'"
              class="h-full w-full"
            >
              <div
                v-if="(currentSliderIndex - 1) === index"
                class="h-full w-full"
              >
                <iframe
                  id="showcase_frame"
                  class="w-full h-screen"
                  :src="item.link"
                  frameborder="0"
                  allowfullscreen
                  allow="xr-spatial-tracking"
                />
              </div>
            </div>

            <!-- PDF -->
            <div
              v-if="item.type==='pdf'"
              class="h-full w-full flex"
            >
              <PDFViewer :url="item.url" />
            </div>
          </swiper-slide>
        </template>
      </OverflowSlider>

      <!-- <div class=" flex flex-row fixed right-[50%] bottom-[10%] translate-x-[50%] -translate-y-[50%] sm:transform-none sm:right-5 sm:bottom-8 items-center gap-1 justify-start z-10 ">
      <button
        :disabled="(currentSliderIndex - 1) === 0 ? true : false"
        class="h-11 w-11 border-transparent text-yellow-300 bg-black bg-opacity-40 backdrop-blur-[20px] flex justify-center items-center rounded-[50%] cursor-pointer  disabled:opacity-15 disabled:cursor-default"
        @click="handlePrev"
      >
        <svg
          class="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M21 12L3 12"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M8 17L3 12L8 7"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <div class="h-11 w-11 bg-black flex justify-center items-center rounded-[50%] cursor-pointer text-lime-400 text-sm">
        {{ currentSliderIndex }}/{{ categoriesFilteredList[selectedCategory].length }}
      </div>

      <button
        :disabled="currentSliderIndex === categoriesFilteredList[selectedCategory].length ? true : false"
        class="h-11 w-11  flex justify-center items-center rounded-[50%] cursor-pointer border-transparent text-blue-500 bg-black bg-opacity-40 backdrop-blur-[20px] disabled:opacity-50 disabled:cursor-default"
        @click="handleNext"
      >
        <svg
          class="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3 12L21 12"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M16 7L21 12L16 17"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div> -->
      <div
        v-if="!showLoader && categoryList && categoryList.length > 1"
        :class="[
          'fixed',
          Store.isMobile
            ? (categoriesFilteredList[selectedCategory].filter((item) => item._id === selectionId)[0]?.type === 'pdf'
              ? 'right-3 z-[2] bottom-24'
              : 'right-3 z-[2] bottom-[9rem]'
            )
            : Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
              ? 'bottom-14 right-24 z-[6]'
              : Store.isLandscape
                ? 'bottom-14 right-0 z-[6]'
                : 'left-10 z-[6] bottom-10'
        ]"
      >
        <DropDown
          placement="top"
          :list="categoryList"
          :defaultValue="selectedCategory"
          type="Array"
          :align-to-end="Store.isMobile || Store.isLandscape ? true : false"
          @select-option="handleCategoriesChange"
        />
      </div>
      <div
        v-if="!showLoader && selectedCategory"
        class="fixed w-full flex justify-center z-[3]"
        :class="(Store.isMobile ? selectionId ? categoriesFilteredList[selectedCategory].filter((item) => item._id === selectionId)[0]?.type !== 'pdf' ? 'bottom-24' : 'bottom-10' : categoriesFilteredList[selectedCategory][0]?.type !== 'pdf' ? 'bottom-24' : 'bottom-10' : Store.isLandscape ? 'bottom-2' : 'bottom-10')"
      >
        <NearByFloatingButton
          v-if="NearByFloatingButtonFilterData(categoriesFilteredList[selectedCategory])?.length > 1"
          class="flex"
          :class="Store.isMobile || Store.isLandscape ? 'w-full' : 'sm:w-[38%] md:w-[48%] lg:w-[60%] xl:[75%]'"
          :itemsList="NearByFloatingButtonFilterData(categoriesFilteredList[selectedCategory])"
          :sliderButton="true"
          :active="selectionId ? categoriesFilteredList[selectedCategory].findIndex((item) => item._id === selectionId) : 0"
          :leftButton="leftButton"
          :rightButton="rightButton"
          :objectIconKey="`icon`"
          :objectNameKey="`value`"
          @button-clicked="slideTo"
        />
      </div>
    </div>
  </div>
</template>

<style>
/* html.a-fullscreen .a-canvas{
  height:100vh !important;

} */

.sample .a-canvas{
  height:100vh !important;
  position:absolute !important;
}
</style>
