import FloorSwitchMobile from './FloorSwitchMobile.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/FloorSwitchMobile',
  component: FloorSwitchMobile,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    numberOfFloors: {
      "1": {
        "floor_id": 1,
        "floor_name": "Floor 1",
        "units": [],
      },
      "2": {
        "floor_id": 2,
        "floor_name": "Floor 2",
        "units": [],
      },
      "3": {
        "floor_id": 3,
        "floor_name": "Floor 3",
        "units": [],
      },
      "4": {
        "floor_id": 4,
        "floor_name": "Floor 4",
        "units": [
          "65a12ec646c8b45224e3a341",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
      "5": {
        "floor_id": 5,
        "floor_name": "Floor 5",
        "units": [
          "65a12ec646c8b45224e3a355",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
      "6": {
        "floor_id": 6,
        "floor_name": "Floor 6",
        "units": [
          "65a12ec646c8b45224e3a361",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
      "7": {
        "floor_id": 7,
        "floor_name": "Floor 7",
        "units": [
          "65a12ec646c8b45224e3a36b",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
      "8": {
        "floor_id": 8,
        "floor_name": "Floor 8",
        "units": [
          "65a12ec646c8b45224e3a379",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
      "9": {
        "floor_id": 9,
        "floor_name": "Floor 9",
        "units": [],
      },
      "10": {
        "floor_id": 10,
        "floor_name": "Floor 10",
        "units": [],
      },
      "11": {
        "floor_id": 11,
        "floor_name": "Floor 11",
        "units": [],
      },
      "12": {
        "floor_id": 12,
        "floor_name": "Floor 12",
        "units": [],
      },
      "13": {
        "floor_id": 13,
        "floor_name": "Floor 13",
        "units": [],
      },
      "14": {
        "floor_id": 14,
        "floor_name": "Floor 14",
        "units": [],
      },
      "15": {
        "floor_id": 15,
        "floor_name": "Floor 15",
        "units": [],
      },
      "16": {
        "floor_id": 16,
        "floor_name": "Floor 16",
        "units": [],
      },
      "17": {
        "floor_id": 17,
        "floor_name": "Floor 17",
        "units": [],
      },
      "18": {
        "floor_id": 18,
        "floor_name": "Floor 18",
        "units": [],
      },
      "19": {
        "floor_id": 19,
        "floor_name": "Floor 19",
        "units": [],
      },
      "20": {
        "floor_id": 20,
        "floor_name": "Floor 20",
        "units": [],
      },
      "21": {
        "floor_id": 21,
        "floor_name": "Floor 21",
        "units": [],
      },
      "22": {
        "floor_id": 22,
        "floor_name": "Floor 22",
        "units": [],
      },
      "23": {
        "floor_id": 23,
        "floor_name": "Floor 23",
        "units": [],
      },
      "24": {
        "floor_id": 24,
        "floor_name": "Floor 24",
        "units": [],
      },
      "25": {
        "floor_id": 25,
        "floor_name": "Floor 25",
        "units": [],
      },
      "26": {
        "floor_id": 26,
        "floor_name": "Floor 26",
        "units": [],
      },
      "27": {
        "floor_id": 27,
        "floor_name": "Floor 27",
        "units": [],
      },
      "28": {
        "floor_id": 28,
        "floor_name": "Floor 28",
        "units": [],
      },
      "29": {
        "floor_id": 29,
        "floor_name": "Floor 29",
        "units": [],
      },
      "30": {
        "floor_id": 30,
        "floor_name": "Floor 30",
        "units": [],
      },
      "31": {
        "floor_id": 31,
        "floor_name": "Floor 31",
        "units": [],
      },
      "32": {
        "floor_id": 32,
        "floor_name": "Floor 32",
        "units": [],
      },
      "33": {
        "floor_id": 33,
        "floor_name": "Floor 33",
        "units": [],
      },
      "34": {
        "floor_id": 34,
        "floor_name": "Floor 34",
        "units": [],
      },
      "35": {
        "floor_id": 35,
        "floor_name": "Floor 35",
        "units": [],
      },
      "36": {
        "floor_id": 36,
        "floor_name": "Floor 36",
        "units": [],
      },
      "37": {
        "floor_id": 37,
        "floor_name": "Floor 37",
        "units": [],
      },
      "38": {
        "floor_id": 38,
        "floor_name": "Floor 38",
        "units": [],
      },
      "39": {
        "floor_id": 39,
        "floor_name": "Floor 39",
        "units": [],
      },
      "40": {
        "floor_id": 40,
        "floor_name": "Floor 40",
        "units": [],
      },
      "41": {
        "floor_id": 41,
        "floor_name": "Floor 41",
        "units": [],
      },
      "42": {
        "floor_id": 42,
        "floor_name": "Floor 42",
        "units": [],
      },
      "43": {
        "floor_id": 43,
        "floor_name": "Floor 43",
        "units": [],
      },
      "44": {
        "floor_id": 44,
        "floor_name": "Floor 44",
        "units": [],
      },
      "45": {
        "floor_id": 45,
        "floor_name": "Floor 45",
        "units": [],
      },
      "46": {
        "floor_id": 46,
        "floor_name": "Floor 46",
        "units": [],
      },
      "47": {
        "floor_id": 47,
        "floor_name": "Floor 47",
        "units": [],
      },
      "48": {
        "floor_id": 48,
        "floor_name": "Floor 48",
        "units": [],
      },
      "49": {
        "floor_id": 49,
        "floor_name": "Floor 49",
        "units": [],
      },
      "50": {
        "floor_id": 50,
        "floor_name": "Floor 50",
        "units": [],
      },
    },
    perSlideView: 4,
    defaultId: "5",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1140-1135&mode=dev",
    },
  },
};
