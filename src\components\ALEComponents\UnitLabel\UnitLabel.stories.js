import UnitLabel from './UnitLabel.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/UnitLabel',
  component: UnitLabel,
  tags: ['autodocs'],
  argTypes: {
  },
};

export const Primary = {
  args: {
    label: "AYK/25/2508",
  },
  parameters:
  {
    design:
    {
      type: 'figma',
      url: 'https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1463-1274&mode=dev',
    },
  },
};
