<template>
  <FwbNavbar
    class="logo flex px-4  flex-col justify-center items-center gap-2 flex-shrink-0 rounded-lg "
    :class="Store.isMobile || Store.isLandscape? '!p-0 bg-transparent': ' w-[8.125rem] bg-secondary backdrop-filter backdrop-blur-[0.625rem]'"
  >
    <template #logo>
      <FwbNavbarLogo
        class="logo_img"
        alt="Logo"
        :image-url="image"
        link="#"
      />
    </template>
  </FwbNavbar>
</template>

<script setup>
import {
  FwbNavbar,
  FwbNavbarLogo,
} from 'flowbite-vue';

import { defineProps } from 'vue';
import { creationToolStore } from '../../../store';
const Store = creationToolStore();

defineProps({
  image: {type: String, default: ""},
});

</script>

<style lang="scss">
.logo {
  background-color:none;
  div{
    justify-content: center;
    button{
      display: none;
    }
  }
  .logo_img{
  img{
    object-fit: contain;
    margin-right: 0px;
  }
}
}
</style>
