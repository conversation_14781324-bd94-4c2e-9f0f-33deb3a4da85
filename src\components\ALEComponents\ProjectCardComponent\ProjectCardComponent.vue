<script setup>
import { defineProps } from 'vue';
import {thousandSeparator} from '../../../helpers/helper';

const props = defineProps(
  {
    name: {type: String, default: ""},
    units: {type: Number, default: 0},
    amenities: {type: Number, default: 0},
    floor: {type: Number, default: 0},
    location: {type: String, default: ""},
    id: {type: String, default: ""},
    thumbnail: {type: String, default: ""},
    positionX: {type: Number, default: 0},
    positionY: {type: Number, default: 0},
  },
);

// Const emit = defineEmits(['onExplore']);

// Function handleExplore (project_id) {
//   Emit('onExplore', project_id);
// }

// Function gotToProject(project_id){
//     // router.push({name:"project",params:{project_id:project_id}})
// }

</script>

<template>
  <div
    class="m-4 md:w-[22.7rem] h-32 object-cover object-[center_center] absolute overflow-hidden rounded-lg w-60 container1 bg-cover bg-no-repeat"
    :style="`background-image:linear-gradient(90deg, rgba(255, 255, 0, 0)0%, rgb(100, 117, 134)64%),url(${thumbnail});`"
  >
    <!-- <img class="absolute h-full object-cover object-[center_center] w-full" :src="ProjectCardImage" /> -->
    <div class="CardDetails">
      <div class="h-fit md:w-fit text-projectCardCompWhite md:mr-4 md:absolute md:right-0 flex flex-col gap-5">
        <p class="card-title ">
          {{ name }}
        </p>
        <div class="flex gap-y-4 gap-x-2 font-medium">
          <div class="flex flex-col gap-3">
            <div class="row-flex">
              <svg
                class="h-4 w-4"
                viewBox="0 0 15 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g
                  id="window_black_24dp 1"
                  clip-path="url(#clip0_262_35810)"
                >
                  <g id="Group">
                    <g id="Group_2">
                      <path
                        id="Vector"
                        d="M7.02778 6.77778V1H2.69444C1.9 1 1.25 1.65 1.25 2.44444V6.77778H7.02778ZM8.47222 6.77778H14.25V2.44444C14.25 1.65 13.6 1 12.8056 1H8.47222V6.77778ZM7.02778 8.22222H1.25V12.5556C1.25 13.35 1.9 14 2.69444 14H7.02778V8.22222ZM8.47222 8.22222V14H12.8056C13.6 14 14.25 13.35 14.25 12.5556V8.22222H8.47222Z"
                        fill="white"
                      />
                    </g>
                  </g>
                </g>
                <defs>
                  <clipPath id="clip0_262_35810">
                    <rect
                      width="15"
                      height="15"
                      fill="white"
                    />
                  </clipPath>
                </defs>
              </svg>
              <p class="card-text flex items-center">
                <span class="max-w-[2.2rem]">{{ props.units && thousandSeparator(units) }} </span>&#160;{{ units == 1 ? 'Unit' : 'Units' }}
              </p>
            </div>
            <div class="row-flex">
              <svg
                class="h-4 w-4"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame 1707479067">
                  <g id="Group 1707478899">
                    <path
                      id="Rectangle 69029"
                      d="M2 12.5337C2 12.055 2.38802 11.667 2.86667 11.667H16.7333V16.0003H2.86667C2.38802 16.0003 2 15.6123 2 15.1337V12.5337Z"
                      fill="white"
                    />
                    <path
                      id="Rectangle 69030"
                      d="M5.4668 8.19967C5.4668 7.72103 5.85482 7.33301 6.33346 7.33301H16.7335V11.6663H5.4668V8.19967Z"
                      fill="white"
                    />
                    <path
                      id="Rectangle 69031"
                      d="M8.93359 3.86667C8.93359 3.38802 9.32161 3 9.80026 3H16.7336V7.33333H8.93359V3.86667Z"
                      fill="white"
                    />
                  </g>
                </g>
              </svg>
              <p class="card-text flex items-center">
                <span class="max-w-[2.2rem]">{{ props.units && thousandSeparator(floor) }} </span>&#160;{{ floor == 1 ? 'Floor' : 'Floors' }}
              </p>
            </div>
          </div>
          <div class="flex flex-col gap-3">
            <div class="row-flex">
              <svg
                class="h-4 w-4"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g
                  id="fi_2991027"
                  clip-path="url(#clip0_262_35820)"
                >
                  <g id="Group">
                    <path
                      id="Vector"
                      d="M7.53107 12.0536V15.0629H3.6665V16H12.3327V15.0629H8.46819V12.0536H7.53107Z"
                      fill="white"
                    />
                    <path
                      id="Vector_2"
                      d="M5.81509 8.48351L6.47773 7.82087L7.53125 8.87439V6.81942L5.81512 5.10329L6.47776 4.44065L7.53128 5.49413V4.0791H8.46841V7.18428L9.52193 6.13079L10.1846 6.79343L8.46844 8.50956V12.0528H12.1993C13.677 12.0528 14.8791 10.8507 14.8791 9.37303C14.8791 8.76524 14.6699 8.16995 14.2899 7.69689C14.0282 7.37095 13.6926 7.11093 13.3162 6.93772C13.4181 6.60316 13.4695 6.25568 13.4695 5.89963C13.4695 5.04578 13.1634 4.21933 12.6075 3.57255C12.1395 3.02811 11.5198 2.63849 10.8347 2.45206C10.7465 1.8379 10.4622 1.27137 10.0139 0.827859C9.47417 0.293977 8.75892 0 7.99981 0C7.24071 0 6.52543 0.293977 5.9858 0.827797C5.53748 1.27131 5.25325 1.83783 5.16506 2.452C4.4799 2.63842 3.86021 3.02805 3.39227 3.57249C2.83636 4.21926 2.5302 5.04572 2.5302 5.89957C2.5302 6.25561 2.58162 6.6031 2.68352 6.93766C2.30717 7.11087 1.97152 7.37089 1.70978 7.69682C1.32984 8.16992 1.12061 8.76518 1.12061 9.37297C1.12061 10.8506 2.32275 12.0528 3.80039 12.0528H7.53128V10.1996L5.81509 8.48351Z"
                      fill="white"
                    />
                  </g>
                </g>
                <defs>
                  <clipPath id="clip0_262_35820">
                    <rect
                      width="16"
                      height="16"
                      fill="white"
                    />
                  </clipPath>
                </defs>
              </svg>
              <p class="card-text flex items-center">
                <span class="max-w-[2.2rem]">{{ props.units && thousandSeparator(amenities) }} </span>&#160;{{ amenities==1 ? 'Amenity' : 'Amenities' }}
              </p>
            </div>
            <div class="row-flex">
              <svg
                class="h-4 w-4"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="typcn:location">
                  <path
                    id="Vector"
                    d="M13.2426 2.81956C10.8995 0.393479 7.10055 0.393479 4.75745 2.81956C4.20114 3.39165 3.75909 4.07503 3.45728 4.82952C3.15547 5.58401 3 6.3944 3 7.21304C3 8.03169 3.15547 8.84207 3.45728 9.59657C3.75909 10.3511 4.20114 11.0344 4.75745 11.6065L8.99963 16L13.2426 11.6065C13.7989 11.0344 14.2409 10.3511 14.5427 9.59657C14.8445 8.84207 15 8.03169 15 7.21304C15 6.3944 14.8445 5.58401 14.5427 4.82952C14.2409 4.07503 13.7989 3.39165 13.2426 2.81956ZM8.99963 9.28939C8.4986 9.28939 8.02834 9.08412 7.67357 8.71148C7.32237 8.34102 7.12513 7.83903 7.12513 7.31568C7.12513 6.79232 7.32237 6.29033 7.67357 5.91987C8.02759 5.54723 8.4986 5.34196 8.99963 5.34196C9.50065 5.34196 9.97167 5.54723 10.3257 5.91987C10.6769 6.29033 10.8741 6.79232 10.8741 7.31568C10.8741 7.83903 10.6769 8.34102 10.3257 8.71148C9.97167 9.08412 9.50065 9.28939 8.99963 9.28939Z"
                    fill="white"
                  />
                </g>
              </svg>
              <p class="card-text flex items-center">
                <span class="max-w-[5rem]">{{ location }} </span>
              </p>
            </div>
          </div>
        </div>
        <!-- <div class="flex flex-col justify-center items-center">
                    <button @click="handleExplore(_id)"
                        class=" w-full middle  text-projectCardCompBlack text-base none center rounded-lg active:bg-projectCardCompActiveButton bg-projectCardCompButtonBackground hover:bg-projectCardCompButtonHover h-10 px-6  font-medium text- shadow-md shadow-white-500/20 transition-all hover:shadow-lg hover:shadow-white-500/40 focus:opacity-[0.85] focus:shadow-none active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                        data-ripple-light="true">
                        Explore
                    </button>
                </div> -->
      </div>
    </div>
  </div>
</template>

<style scoped>

.CardDetails {
@apply w-full h-full flex justify-center items-center;

}

.row-flex {
@apply flex text-center;
}

.card-title {
@apply font-medium text-base overflow-hidden whitespace-nowrap text-ellipsis inline-block max-w-[13.5rem];
}

.row-flex span{
@apply overflow-hidden whitespace-nowrap text-ellipsis inline-block;
}

.card-text {
@apply text-xs ml-2 text-ellipsis;
}
</style>
