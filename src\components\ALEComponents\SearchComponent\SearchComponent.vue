<script setup>
import { defineProps, ref, computed, onMounted, defineEmits } from 'vue';
import InventoryFloorPlateCard from '../InventoryFloorPlateCard/InventoryFloorPlateCard.vue';

const props =  defineProps({
  properties: {
    type: Array,
    default () {
      return [];
    },
  },
  data: {
    type: Object,
    default () {
      return {};
    },
  },
  buildingData: {
    type: Object,
    default () {
      return {};
    },
  },
  favUnits: {
    type: Array,
    default: () => [],
  },
  hideStatus: {
    type: String,
    default: "false",
  },
});
const emit = defineEmits(['gotoUnit', 'toggleButton']);
const totalUnits = Object.keys(props.data).length;

const searchedUnit = ref('');
const list = ref();
const showListView = ref(false);

function outsideClickHandler (event) {
  const div = list.value;
  if (div && !div.contains(event.target)) {
    showListView.value = false;
  }
}

const filteredUnits = computed(() => {
  if (!searchedUnit.value.trim()) {
    return props.data;
  }

  const searchTerm = searchedUnit.value.trim().toLowerCase();
  return Object.values(props.data).filter((unit) => {
    for (const key in unit) {
      if (Object.prototype.hasOwnProperty.call(unit, key)) {
        if (unit[key].toString().toLowerCase().includes(searchTerm)) {
          return unit;
        }
      }
    }
    return false;
  });
});

const clearSearch = () => {
  searchedUnit.value = '';
};

onMounted(() => {
  document.addEventListener('click', outsideClickHandler);
});
const emitEvent = (unit_id, unitplan_id) => {
  emit("gotoUnit", unit_id, unitplan_id);
};
</script>

<!-- <template>
  <div
    ref="list"
    class="relative"
  >
    <div
      class="w-fit md:w-52 h-9 p-2 bg-neutral-800 bg-opacity-75 rounded-lg backdrop-blur-[7.50px] justify-center items-center gap-1.5 inline-flex"
      @click="showList()"
    >
      <div class="w-5 h-5 relative flex-col justify-start items-start flex">
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.33333 14.1667C11.555 14.1667 14.1667 11.555 14.1667 8.33333C14.1667 5.11167 11.555 2.5 8.33333 2.5C5.11167 2.5 2.5 5.11167 2.5 8.33333C2.5 11.555 5.11167 14.1667 8.33333 14.1667Z"
            stroke="#D8DBDF"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M12.5 12.5L17.5 17.5"
            stroke="#D8DBDF"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <input
        v-model="searchInput"
        type="text"
        placeholder="Search for properties"
        class="w-10 h-full text-gray-300 text-xs font-normal  bg-transparent outline-none md:w-full"
        @change="showList()"
      >
    </div>

    <div v-if="showListView">
      <div
        class="w-64 min-h-[30px] max-h-44 mt-5 -left-8 md:-left-2  cursor-pointer absolute  flex flex-col justify-center items-center gap-2.5 bg-neutral-800 bg-opacity-60 rounded-lg border border-gray-400 backdrop-blur-[19rem] z-40"
        style="align-items: baseline;"
      >
        <div class="w-full overflow-y-auto scroll-auto snap-always no-scrollbar">
          <div
            v-for="(option, index) in filteredProperties"
            :key="'filtered_' + index"
            class="flex flex-row justify-between items-center w-full p-2 px-4 hover:bg-white hover:bg-opacity-[0.1] dropdown-list cursor-pointer"
            @click="emitEvent(option._id)"
          >
            <div class=" w-28 text-white  text-base font-medium cursor-pointer whitespace-nowrap overflow-hidden capitalize text-ellipsis">
              {{ option.name }}
            </div>
            <div
              v-if="option.status !== ''"
              class="pill_container"
            >
              <StatusPill
                :availability-status="option.status"
                :available-units="option.units"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template> -->

<template>
  <div class="flex flex-row relative -mt-5 bg-transparent">
    <div
      class="w-auto h-full p-2.5 "
    >
      <!-- header -->
      <div class=" flex h-10 gap-3">
        <div class="flex justify-between items-center gap-32">
          <div>
            <p class="text-white text-base not-italic font-normal leading-[normal]">
              Showing <span>{{ searchedUnit.length?filteredUnits.length:totalUnits }}</span> Units
            </p>
          </div>
        </div>
      </div>
      <!-- body -->
      <div class="body w-full overflow-y-scroll no-scrollbar flex flex-col  mt-[0.5px] gap-3">
        <div
          v-for="(value, _id) in filteredUnits"
          :key="_id"
          class="flex flex-col"
        >
          <InventoryFloorPlateCard
            :name="value.name"
            :status="value.status"
            :tower="buildingData[value.building_id]?buildingData[value.building_id].name:''"
            :floor="value.floor_id"
            :type="value.type"
            :price="value.price"
            :currency="value.currency"
            :measurement="value.measurement"
            :measurementType="value.measurement_type"
            :bedrooms="value.bedroom"
            :favUnits="favUnits"
            :unitId="value._id"
            :hideStatus="hideStatus"
            @emit-event="emitEvent(value._id,value.unitplan_id)"
            @toggle-button="emit('toggleButton',value)"
          />
        </div>
      </div>
      <!-- footer -->
      <div class=" flex justify-between items-end px-1 h-14 w-full ">
        <div class="w-fit md:w-52 h-9 p-2 px-5 bg-neutral-800 bg-opacity-75 rounded-[2.56rem] backdrop-blur-[7.50px] justify-center items-center gap-1 inline-flex">
          <input
            v-model="searchedUnit"
            type="text"
            placeholder="Search for Units"
            class="w-12 h-full text-gray-300 text-xs font-normal  bg-transparent outline-none md:w-full"
          >
          <div class="w-5 h-5 relative flex-col justify-start items-start flex">
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.33333 14.1667C11.555 14.1667 14.1667 11.555 14.1667 8.33333C14.1667 5.11167 11.555 2.5 8.33333 2.5C5.11167 2.5 2.5 5.11167 2.5 8.33333C2.5 11.555 5.11167 14.1667 8.33333 14.1667Z"
                stroke="#D8DBDF"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.5 12.5L17.5 17.5"
                stroke="#D8DBDF"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div
          class="w-9 h-9 shrink-0 backdrop-blur-[3.8px] p-2.5 rounded-3xl flex justify-center items-center"
          @click="clearSearch()"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_93_8859)">
              <path
                d="M0.0820308 9.99609C0.0820311 15.4648 4.53125 19.9141 10 19.9141C12.1797 19.9141 14.25 19.1992 15.9531 17.9219L15.9531 18.9297C15.9531 19.4766 16.3984 19.9219 16.9453 19.9219C17.4922 19.9219 17.9375 19.4766 17.9375 18.9297L17.9375 14.957C17.9375 14.4102 17.4922 13.9648 16.9453 13.9648L12.9766 13.9648C12.4297 13.9648 11.9844 14.4102 11.9844 14.957C11.9844 15.5039 12.4297 15.9492 12.9766 15.9492L15.2344 15.9492C13.793 17.2148 11.9531 17.9336 10 17.9336C5.625 17.9336 2.06641 14.375 2.06641 10C2.06641 5.625 5.625 2.06641 10 2.06641C14.375 2.06641 17.9336 5.625 17.9336 10C17.9336 10.5469 18.3789 10.9922 18.9258 10.9922C19.4727 10.9922 19.918 10.5469 19.918 10C19.918 4.52734 15.4687 0.0781243 10 0.0781246C4.53125 0.0781248 0.0820306 4.52734 0.0820308 9.99609Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_93_8859">
                <rect
                  width="20"
                  height="20"
                  fill="white"
                  transform="translate(0 20) rotate(-90)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

.dropdown-list:last-child{
border-bottom-left-radius: 0.5rem;
border-bottom-right-radius: 0.5rem;
}

.dropdown-list:first-child{
border-top-left-radius: 0.5rem;
border-top-right-radius: 0.5rem;
}

/* Hide scrollbar */
.no-scrollbar::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
}

.no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
}

.pill_container{
   font-size: 12px !important;
}

.body{
  min-height: calc(100vh - 170px) ;
  max-height: calc(100vh - 170px);
}

@media only screen and (max-width: 1330px) and (max-height:660px){
  .body{
  min-height: calc(100vh - 260px) ;
  max-height: calc(100vh - 260px);
}}

</style>
