<script setup>
import { ref, defineProps, reactive, onMounted } from 'vue';
import PictureCard from '../../ALEComponents/PictureCard/PictureCard.vue';
import router from '../../../router';
import {creationToolStore} from '../../../store/index';
import { useRoute } from 'vue-router';
import { fetchVideo } from '../../../helpers/API';
import { addSVGDeepZoom, cdn } from '../../../helpers/helper';
import { availUnitOrgs } from '../../../config/masterdata';
import { onClickOutside } from '@vueuse/core';
import OpenSeadragon from 'openseadragon';
const route = useRoute();
const Store = creationToolStore();

const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
}});
fetchVideo(props.layerData, cdn(Store.SceneData[route.params.sceneId].sceneData.background?.low_resolution));

const show_modal = ref(false);
const communityRefs = reactive({});
// Function to set community refs properly
const setCommunityRef = (el, index) => {
  if (el) {
    communityRefs[index] = el;
  }
};
const position=ref(false), communityId=ref(), sceneId=ref(), layer=ref(null), clickedLayerId = ref(false);
const projectId = ref(route.params.projectId);
const pictureCardRef = ref(null), communitySVGElem = ref({});

function placeModal (layer_id){
  var svgGroup = document.getElementById(layer_id);
  var rect = svgGroup.getBoundingClientRect();
  layer.value = layer_id;
  communityId.value= props.layerData[layer_id].layer_data.community_id;
  sceneId.value=props.layerData[layer_id].layer_data.scene_id;
  router.push({name: "projectScene", query: {community_id: props.layerData[layer_id].layer_data.community_id, status: availUnitOrgs.includes(route.params.organizationId)?'available':""}});
  position.value={x: rect.x, y: rect.y+30};
  show_modal.value=true;
}

function toggleOpacity (layer_id, isIncreaseOpacity) {
  var svgGroup = document.getElementById(layer_id);
  if (isIncreaseOpacity){
    // Add
    svgGroup.classList.remove("opacity-80");
    svgGroup.classList.add("opacity-100");
  } else {
    // Remove
    svgGroup.classList.remove("opacity-100");
    svgGroup.classList.add("opacity-80");
  }
}

// function closeModal () {
//   show_modal.value = false;
// }

// onMounted(() => {
//   onClickOutside(pictureCardRef, closeModal);
// });

function moveTonxtScene (){
  if (props.layerData[layer.value].layer_data.video_tag){
    Store.currentSceneVideo=props.layerData[layer.value].layer_data.video_tag;
    Store.currentSceneVideoThumb=Store.SceneData[route.params.sceneId].sceneData.background.low_resolution;
    window.asd = function (){
      router.push({name: 'projectScene', params: {sceneId: sceneId.value}, query: {...route.query}});
    };
  } else {
    router.push({name: 'projectScene', params: {sceneId: sceneId.value}, query: {...route.query}});
  }
}
const bedroomRange = () => {
  const unitTypes = Store.communityData[communityId.value].uniqueUnitTypes;
  const bedroomNumbers = unitTypes
    .map((type) => parseInt(type, 10))
    .filter(Boolean)
    .sort((a, b) => a - b);

  if (bedroomNumbers.length === 0) {
    return '';
  }
  if (bedroomNumbers.length === 1) {
    return `${bedroomNumbers[0]} Bedrooms`;
  }

  const rangeString = bedroomNumbers.slice(0, -1).join(', ');
  const lastNumber = bedroomNumbers.at(-1);

  return `${rangeString} & ${lastNumber} Bedrooms`;
};

if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);

        obj.svgElement.children[0].style.cursor= 'pointer';
        obj.svgElement.children[0].classList.add('opacity-70', 'stroke-[0.2rem]');

        communitySVGElem.value[item.layer_data.layer_id] = {
          'g': obj.svgElement.children[0],
          'minZoomLevel': item.layer_data.minZoomLevel,
          'maxZoomLevel': item.layer_data.maxZoomLevel,
        };

        new OpenSeadragon.MouseTracker({
          element: obj.svgElement.children[0],
          clickHandler: function () {
            if (item.layer_data.layer_id !== clickedLayerId.value){
              clickedLayerId.value = item.layer_data.layer_id;
              // placeModal(item.layer_data.layer_id, item.layer_data.building_id, item.layer_data.community_id, item.layer_data.scene_id);
              const viewport = window.viewer.viewport;
              const viewerRect = window.viewer.element.getBoundingClientRect();
              const overlayRect = obj.svgElement.children[0].getBoundingClientRect(); // Get the element position and sizings
              // Calculate the center of the overlay in screen coordinates
              const overlayCenterX = overlayRect.left + (overlayRect.width / 2); // X
              const overlayCenterY = overlayRect.top + (overlayRect.height / 2); // Y
              // Convert the screen coordinates to viewport coordinates
              const viewportPoint = viewport.pointFromPixel(
                new OpenSeadragon.Point(
                  overlayCenterX - viewerRect.left,
                  overlayCenterY - viewerRect.top,
                ),
              );
              if (Store.currentZoomlevel < 2.5){
                // Define the zoom level you want to zoom into
                const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
                // Zoom in to the calculated viewport point
                viewport.zoomTo(zoomLevel, viewportPoint); // Zoom in to calculated point
                viewport.applyConstraints();
              } else {
                placeModal(item.layer_data.layer_id);
              }

              window.viewer.addOnceHandler('animation-finish', function () {
                placeModal(item.layer_data.layer_id);
              });
            }
          },

        });
        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.children[0].classList.add('!visible');
            obj.svgElement.children[0].classList.remove('!hidden');
          } else {
            obj.svgElement.children[0].classList.remove('!visible');
            obj.svgElement.children[0].classList.add('!hidden');
          }
        }

        obj.svgElement.children[0].addEventListener("mouseover", () => {
          obj.svgElement.children[0].classList.remove('opacity-70');
          obj.svgElement.children[0].classList.add('opacity-100');
        });

        obj.svgElement.children[0].addEventListener("mouseleave", () => {
          obj.svgElement.children[0].classList.remove('opacity-100');
          obj.svgElement.children[0].classList.add('opacity-70');

        });
      });
    }
  });
}

if (props.sceneType === "deep_zoom"){
  window.viewer.addHandler('zoom', function () {
    show_modal.value = false;
    clickedLayerId.value  = false;
    layer.value = null;
    communityId.value = null;
    sceneId.value = null;
  });
}
onClickOutside(pictureCardRef, (event) => {
  if (props.sceneType === 'deep_zoom'){
    if (show_modal.value && event.target.tagName === 'DIV'){
      show_modal.value = false;
      clickedLayerId.value  = false;
      layer.value = null;
      communityId.value = null;
      sceneId.value = null;
    }
  }
});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="layer, layerId, LayerIndex in layerData"
    v-show="sceneType !=='deep_zoom'"
    :id="layerId"
    :key="layerId"
    class="opacity-80 hover:opacity-100 cursor-pointer group"
    @click="(event) => { placeModal(layer.layer_data.layer_id); toggleOpacity(layer.layer_data.layer_id,true)} "
    @mouseleave="(event) => toggleOpacity(layer.layer_data.layer_id,false)"
  >
    <g v-if="layer.layer_data.showLabel">
      <g
        :ref="(el) => setCommunityRef(el, LayerIndex)"
        :label="Store.communityData[layer.layer_data.community_id]?.name"
        class="opacity-5 hover:opacity-30 hover:fill-grey cursor-pointer"
        :class="sceneType !=='deep_zoom' ? layer.layer.getAttribute('class') + ' ' + layer.layer_data.type:''"
        v-html="layer.layer.innerHTML"
      />

      <foreignObject
        v-if="communityRefs[LayerIndex] && Store.communityData[layer.layer_data.community_id]?.name"
        :key="`foreign-${LayerIndex}`"
        :x="communityRefs[LayerIndex].getBBox().x + (communityRefs[LayerIndex].getBBox().width - 140) / 2"
        :y="communityRefs[LayerIndex].getBBox().y + (communityRefs[LayerIndex].getBBox().height - 120) / 2"
        :width="180"
        :height="240"
        class="lg:block community pointer-events-none bg-transparent relative rounded group:hover:hidden"
      >
        <div xmlns="http://www.w3.org/1999/xhtml">
          <span class="z-10 w-full h-full flex flex-col gap-4 items-center">
            <div class="w-[100px] h-[70px] grid place-items-center">
              <div class="w-[35px] h-[35px] rounded-[50%] grid place-items-center animation">
                <div class="w-[35px] h-[35px] bg-secondary rounded-[50%] grid place-items-center">
                  <div class="w-[16px] h-[16px] bg-primary rounded-[50%]" />
                </div>
              </div>
            </div>

            <div
              class="uppercase h-fit font-medium px-3 py-2 rounded bg-secondary text-secondaryText inline-block max-w-[165px] whitespace-normal break-words overflow-hidden text-center"
            >
              {{ communityRefs[LayerIndex]?.getAttribute('label') }}
            </div>
          </span>
        </div>
      </foreignObject>
    </g>
    <g
      v-else
      :id="layer.layer_data.layer_id"
      :key="`layer-${LayerIndex}`"
      class="opacity-80 hover:opacity-100 hover:fill-grey cursor-pointer"
      :class="sceneType !=='deep_zoom' ?layer.layer.getAttribute('class') + ' ' + layer.layer_data.type:''"
      v-html="layer.layer.innerHTML"
    />
  </g>
  <!--eslint-enable-->
  <portal to="community">
    <div
      v-if="show_modal"
      ref="pictureCardRef"
      :class="Store.isMobile ? '!w-full !absolute !bottom-0 z-[10]' : Store.isLandscape ? '!bottom-2 !w-fit !absolute !z-[10] !right-20' : 'w-fit absolute sm:top-20 sm:right-7 sm:bottom-auto bottom-20 flex justify-center sm:align-bottom sm:justify-end z-[10]'"
    >
      <PictureCard
        :imageUrl="cdn(Store.communityData[communityId].thumbnail)"
        :title="Store.communityData[communityId].name"
        :location="Store.projectCardData[projectId].name ?? ''"
        :bedrooms="Store.communityData[communityId].uniqueUnitTypes ? bedroomRange() : ''"
        :units="Store.communityData[communityId].totalUnits + ' Units'"
        :type="Store.communityData[communityId].category"
        :towerLayer="false"
        :communityLayer="true"
        :showExplore="true"
        :status="Store.communityData[communityId].unitStatus.Available > 0 ? '':'soldOut'"
        :availableUnitsPresent="Store.communityData[communityId].unitStatus.Available > 0 ? true:false"
        @explore="moveTonxtScene"
      />
    </div>
  </portal>
</template>
<style scoped>

.animation {
  animation: ripple 1.1s linear infinite;
}

@keyframes ripple {
  0% {
    height: 35px;
    width: 35px;
    background-color: var(--secondaryText);
  }
  50% {
    height: 55px;
    width: 55px;
    background-color: var(--secondaryText);
  }
  100% {
    height: 70px;
    width: 70px;
    background-color:  rgba(255, 255, 255, 0);
  }
}
</style>
