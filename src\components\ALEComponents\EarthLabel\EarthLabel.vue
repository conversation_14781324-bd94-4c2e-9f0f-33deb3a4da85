<script setup>
import { ref } from 'vue';
const porps = defineProps({
  name: {type: String, default: ""},
  active: Boolean,
});

const isClicked = ref(false);

const emit = defineEmits(['countryView']);

function handleCountryView () {
  if (porps.active === false){
    isClicked.value = !isClicked.value;
  } else {
    emit('countryView');
  }
}

</script>

<template>
  <div class="flex items-center gap-x-2.5 w-fit absolute">
    <button
      class="peer earthLabelContainer border-4 border-solid h-fit w-fit flex justify-center items-center gap-2 relative px-2.5 py-1.5 rounded-[50px] hover:cursor-pointer"
      :class="active ? 'bg-white text-neutral-800 border-white opacity-100 boxShapeColorWhite hover:bg-[#D8DBDF] hover:border-[#D8DBDF]' : 'text-[#D8DBDF] bg-[#000000] border-black hover:opacity-80 boxshapecolor'"
      @click="handleCountryView"
    >
      <svg
        v-if="active"
        class="h-6 w-6"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.61664 6.27046C5.53174 6.34667 5.46373 6.44098 5.41729 6.54693C5.37084 6.65288 5.34705 6.76797 5.34754 6.88432V18.1923C5.34754 18.4065 5.42972 18.612 5.576 18.7634C5.72228 18.9149 5.92067 19 6.12754 19C6.33441 19 6.53281 18.9149 6.67908 18.7634C6.82536 18.612 6.90754 18.4065 6.90754 18.1923V15.3464C8.39214 14.1941 9.64404 14.7326 11.5017 15.683C12.5567 16.2215 13.7598 16.8407 15.0637 16.8407C16.0205 16.8407 17.0319 16.5082 18.0784 15.5699C18.1627 15.4943 18.2303 15.4009 18.2767 15.2959C18.3231 15.1909 18.3473 15.0769 18.3475 14.9614V6.88432C18.3476 6.72945 18.3046 6.57784 18.2238 6.44754C18.1429 6.31725 18.0276 6.21377 17.8916 6.14944C17.7555 6.0851 17.6045 6.06262 17.4565 6.08467C17.3084 6.10673 17.1696 6.17238 17.0566 6.27382C15.4505 7.71492 14.1622 7.16971 12.1933 6.16074C10.3649 5.21976 8.08859 4.05329 5.61664 6.27046ZM16.7875 14.5751C15.3029 15.7281 14.051 15.1883 12.1933 14.2385C10.6879 13.4645 8.87899 12.5383 6.90754 13.4719V7.26731C8.39214 6.11497 9.64404 6.65345 11.5017 7.60386C12.5567 8.14233 13.7598 8.76158 15.0637 8.76158C15.659 8.7625 16.2472 8.62905 16.7875 8.37051V14.5751Z"
          fill="#262626"
        />
      </svg>
      <svg
        v-else
        class="h-6 w-6"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.76911 6.27046C5.6842 6.34667 5.6162 6.44098 5.56975 6.54693C5.52331 6.65288 5.49952 6.76797 5.50001 6.88432V18.1923C5.50001 18.4065 5.58219 18.612 5.72846 18.7634C5.87474 18.9149 6.07314 19 6.28001 19C6.48688 19 6.68527 18.9149 6.83155 18.7634C6.97783 18.612 7.06001 18.4065 7.06001 18.1923V15.3464C8.54461 14.1941 9.7965 14.7326 11.6542 15.683C12.7092 16.2215 13.9123 16.8407 15.2162 16.8407C16.173 16.8407 17.1844 16.5082 18.2309 15.5699C18.3151 15.4943 18.3828 15.4009 18.4292 15.2959C18.4756 15.1909 18.4997 15.0769 18.5 14.9614V6.88432C18.5 6.72945 18.4571 6.57784 18.3762 6.44754C18.2954 6.31725 18.1801 6.21377 18.044 6.14944C17.908 6.0851 17.757 6.06262 17.6089 6.08467C17.4609 6.10673 17.3221 6.17238 17.2091 6.27382C15.603 7.71492 14.3147 7.16971 12.3458 6.16074C10.5174 5.21976 8.24106 4.05329 5.76911 6.27046ZM16.94 14.5751C15.4554 15.7281 14.2035 15.1883 12.3458 14.2385C10.8404 13.4645 9.03146 12.5383 7.06001 13.4719V7.26731C8.54461 6.11497 9.7965 6.65345 11.6542 7.60386C12.7092 8.14233 13.9123 8.76158 15.2162 8.76158C15.8114 8.7625 16.3997 8.62905 16.94 8.37051V14.5751Z"
          fill="#D8DBDF"
        />
      </svg>
      <p
        class="text-inherit text-base w-fit"
        :class="active ? 'font-medium' : 'font-bold'"
      >
        {{ name }}
      </p>
    </button>
    <div
      v-show="!active"
      :id="`${name}_CS`"
      class="peer-focus:block hidden absolute right-[-110px] w-fit h-fit px-3 py-2 bg-comingSoonBgcolor bg-opacity-75 rounded-[30px] justify-center items-center gap-2.5 "
    >
      <p class="text-center text-comingSoonTextColor text-xs font-medium z-10 relative">
        Coming Soon.
      </p>
    </div>
  </div>
</template>

<style scoped>

/* Container *//*
.earthLabelContainer{
  background: rgba( 0,  0, 0, 1);
  opacity: 0.4;
  backdrop-filter: blur(19px);
  -webkit-backdrop-filter: blur(19px);
}

.earthLabelContainer:hover{
  background: white;
  color: black;
  opacity: 1;
  backdrop-filter: blur(0px);
}

/* After Shape */
/* .earthLabelContainer::after {
     content:'\2726';
  position:absolute;
  bottom:-46px;
  left:11px;
  font-size:70px;
  color: rgba(0, 0, 0, 1);
  opacity:1;
  transform:rotate(0deg);
  z-index:0;
}

.earthLabelContainer:hover::after{
  color: white;
} */

.earthLabelContainer{
    position: relative;
    /* border: 4px solid rgba( 0,  0, 0, 1); */
    /* background: rgba( 0,  0, 0, 1); */
    /* opacity: 0.8; */
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
}
.earthLabelContainer::after{
    content: '';
    position: absolute;
    display: block;
    bottom: -9px;
    width: 28px;
    height: 20px;
    left: 4px;
    z-index: -1;
    transform: rotate(45deg);
    /* background-color: rgba( 0, 0, 0, 1); */
    opacity: 1;
    backdrop-filter: blur(19px);
    -webkit-backdrop-filter: blur(19px);
    border-bottom-right-radius: 4px;
    z-index: -1;
}
.earthLabelContainer::before{
    content: "";
    position: absolute;
    background-color: transparent;
    bottom: -35px;
    left: 19px;
    height: 34px;
    width: 25px;
    border-top-left-radius: 25px;
    transform: rotate(12deg);
    z-index: -1;
}
.boxshapecolor::before{
  box-shadow: 0 -19px 0 0 rgba( 0, 0, 0, 1);
}
.boxshapecolor::after{
  background-color: rgba( 0, 0, 0, 1);
}
.boxShapeColorWhite::before{
  box-shadow: 0 -19px 0 0 rgb(255, 255, 255);
}
.boxShapeColorWhite::after{
  background-color: white;
}

.boxShapeColorWhite:hover::before{
  box-shadow:  0 -19px 0 0 rgba(216, 219, 223, 1);
}
.boxShapeColorWhite:hover::after{
  background-color: #D8DBDF;
}
/* .earthLabelContainer:hover{
  background: white;

  opacity: 1;
  backdrop-filter: blur(0px);
  border-color: white;
}
.earthLabelContainer:hover::after{
    background-color:white;
}
.earthLabelContainer:hover::before{
    box-shadow: 0 -19px 0 0 white;
} */
</style>
