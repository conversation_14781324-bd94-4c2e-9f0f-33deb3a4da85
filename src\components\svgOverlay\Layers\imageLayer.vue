<script setup>
import { defineProps, ref, onMounted, defineEmits, watch, nextTick } from 'vue';
import { loadImageData, cdn } from '../../../helpers/helper';
const props = defineProps({highres: {type: String, default: ""}, lowres: {type: String, default: ""}, video: {type: String, default: ""}});
const emit = defineEmits(['lowresLoaded', 'highresLoaded', 'progressData']);
const lowResFlag = ref(true), lowRes=ref(), highRes=ref(false), videoUrl = ref();
const oldImageFlag = ref(false);
const prevImage = ref(null);
const fadeIn = ref(false);
// HighRes.value =cdn(props.highres);
videoUrl.value=cdn(props.video);
function lowResLoaded (){
  setTimeout(() => {
    lowResFlag.value = false;
  }, 1000);
}

watch(() => props.lowres, async () => {
  prevImage.value = highRes.value || lowRes.value || null;
  fadeIn.value = false;
  oldImageFlag.value = true;
  lowRes.value = false;
  lowResFlag.value = true;

  loadImageData(cdn(props.lowres), (progress) => emit('progressData', (progress/2)))
    .then(async (data) => {
      oldImageFlag.value = false;
      lowResFlag.value = false;
      lowRes.value = data;
      lowResFlag.value = false;
      highRes.value = false;
      await nextTick();
      fadeIn.value = true;
      emit('lowresLoaded');
      if (props.highres) {
        loadImageData(cdn(props.highres), (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false))
          .then((data) => {
            highRes.value = data;
            emit('highresLoaded');
          });
      }
      setTimeout(() => {
        prevImage.value = null;
      }, 600);
    });
});

onMounted(() => {
  oldImageFlag.value = true;
  loadImageData(cdn(props.lowres), (progress) => emit('progressData', (progress/2)))
    .then(async (data) => {
      oldImageFlag.value = false;
      lowResFlag.value = false;
      lowRes.value = data;
      lowResFlag.value = false;
      highRes.value = false;
      await nextTick();
      fadeIn.value = true;
      emit('lowresLoaded');
      if (props.highres) {
        loadImageData(cdn(props.highres), (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false))
          .then((data) => {
            highRes.value = data;
            emit('highresLoaded');
          });
      }
      setTimeout(() => {
        prevImage.value = null;
      }, 600);
    });
});
watch(() => props.highres, () => {

  lowResFlag.value=true;
  oldImageFlag.value = true;
  lowRes.value = false;
  if (props.highres) {
    if (!highRes.value) {
      loadImageData(cdn(props.highres), (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false))
        .then((data) => {
          highRes.value = data;
          emit('highresLoaded');
        });
    }
  }
});
</script>
<template>
  <portal to="image_layer">
    <div id="oldimage-div" />
    <div style="position:relative;width:100%;height:100%;">
      <img
        v-if="prevImage"
        :src="prevImage"
        class="image-transition-prev"
        style="position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;z-index:0;"
        alt="Previous Scene"
      >
      <img
        v-if="lowRes"
        :src="lowRes"
        class="image-transition-new"
        :class="{ 'fade-in': fadeIn }"
        style="position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;z-index:1;transition:opacity 0.6s;opacity:0;"
        alt="Current Scene"
        @load="lowResLoaded"
      >
    </div>
    <svg
      v-if="(lowResFlag || highRes==false) || (oldImageFlag && highRes)"
      id="ImageLayer"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      viewBox="0 0 1920 1080"
      style="enable-background:new 0 0 1920 1080;"
      xml:space="preserve"
      preserveAspectRatio="xMidYMid slice"
      class="fixed top-0 z-1"
    >
      <image
        v-if="lowResFlag || highRes==false"
        id="svg_lowres_image"
        style="transform-origin: center center;transition: transform 2s opacity 0.1s;object-fit:contain;height:100%;width:100%"
        :xlink:href="lowRes"
        @load="lowResLoaded"
      />
      <image
        v-if="oldImageFlag && highRes"
        id="oldImage"
        class="fixed top-1"
        style="transform-origin: center center;transition: transform 2s, opacity 0.1s;object-fit:cover;"
        :xlink:href="highRes"
      />
    </svg>
  </portal>

  <image
    v-if="!props.video && highRes && !oldImageFlag"
    id="svg_highres_image"
    style="transform-origin: center center;transition: transform 2s, opacity 0.1s;object-fit:cover;"
    :xlink:href="highRes || lowRes"
    @load="highResLoaded"
  />
  <foreignObject
    v-if="cdn(props.video)"
    style="position: absolute; top:0; left:0; width:100%; height:100%; object-fit: contain;"
  >
    <video
      :poster="cdn(lowres)"
      style="position: absolute; top:0; left:0; width:100%; height:100%; object-fit: contain;"
      muted
      autoplay
      loop
    >
      <source :src="videoUrl">
    </video>
  </foreignObject>
</template>
<style scoped>
.fade-in {
  opacity: 1 !important;
  transition: opacity 0.6s;
}
</style>
