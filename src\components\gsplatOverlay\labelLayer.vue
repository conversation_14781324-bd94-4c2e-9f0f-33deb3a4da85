<script setup>
import { ref, defineProps, watch, onMounted, defineEmits} from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
import LandmarkLabel from '../ALEComponents/LandmarkLabel/LandmarkLabel.vue';
import { cdn } from '../../helpers/helper';
import { layer_icons } from '../../config/masterdata';
const emit = defineEmits(['appendObject']);
const props = defineProps({layer: {
  type: Object,
  default () {
    return {};
  },
}});
const show_modal = ref(false);
const position=ref(false);
const fillValue = ref(null), title = ref(), labelLayer = ref(null);
const currentCategory = ref(route.query.category);
watch(() => {
  return route.query;
}, (newPath) => {
  currentCategory.value = newPath.category;
});
const namedColors = {
  "red": [255, 0, 0],
  "green": [0, 128, 0],
  "yellow": [255, 255, 0],
  "indigo": [75, 0, 130],
  "purple": [255, 0, 0],
  "pink": [255, 192, 203],
  "dark": [0, 0, 0],
};
const getDistance = (color1, color2) => {
  return Math.sqrt(
    Math.pow(color1[0] - color2[0], 2) +
    Math.pow(color1[1] - color2[1], 2) +
    Math.pow(color1[2] - color2[2], 2),
  );
};
const parseRgbString = (rgbString) => {
  const result = rgbString.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
  if (result) {
    const r = parseInt(result[1]);
    const g = parseInt(result[2]);
    const b = parseInt(result[3]);
    return [r, g, b];
  }
  return null;
};
const closestNamedColor = (rgbString) => {
  const rgb = parseRgbString(rgbString);
  if (!rgb) {
    return null;
  }

  let closestColor = "";
  let minDistance = Infinity;

  for (const [name, colorRgb] of Object.entries(namedColors)) {
    const distance = getDistance(rgb, colorRgb);
    if (distance < minDistance) {
      minDistance = distance;
      closestColor = name;
    }
  }

  return closestColor;
};
function placeModal (layer_id, event, name){
  title.value=name;
  const svgGroup = document.getElementById(layer_id);
  const rect = svgGroup.getBoundingClientRect();
  position.value={x: rect.x + rect.width + 10, y: rect.y-3};
  show_modal.value=true;

  const clickedElement = event.target;

  if (clickedElement.tagName === 'path') {
    const paths = Array.from(clickedElement.parentNode.getElementsByTagName('path'));
    const largestPath = paths.reduce((largest, current) => {
      const largestArea = largest.getBoundingClientRect().width * largest.getBoundingClientRect().height;
      const currentArea = current.getBoundingClientRect().width * current.getBoundingClientRect().height;
      return largestArea > currentArea ? largest : current;
    });
    const computedStyle = window.getComputedStyle(largestPath);
    // FillValue.value = computedStyle.getPropertyValue('fill');
    const closestColor = closestNamedColor(computedStyle.getPropertyValue('fill'));
    fillValue.value = closestColor;
  }
}
function closeModal (e){
  if (e.target.getAttribute("clickaway")==="true"){
    show_modal.value=false;
  }
}
onMounted(async () => {
  const url =  cdn(props.layer.svg_url);
  var svgString;
  if (props.layer.svg_url !== undefined && props.layer.svg_url !== ""){
    const requestOptions = {
      method: "GET",
      redirect: "follow",
    };
    const response = await fetch(url, requestOptions);
    svgString = await response.text();
  } else {
    svgString = layer_icons[props.layer.type][props.layer.name].icon;
  }
  const div = document.createElement('div');
  div.id=props.layer.layer_id;
  div.innerHTML=svgString;
  div.setAttribute('class', 'amenityIcons');
  div.setAttribute('clickaway', "true");
  labelLayer.value=div;
  document.body.appendChild(div);
  document.getElementById(props.layer.layer_id).addEventListener( 'mouseover', (event) => placeModal(props.layer.layer_id, event, props.layer.title) );
  document.getElementById(props.layer.layer_id).addEventListener( 'mouseleave', (event) => closeModal(event) );
  emit('appendObject', labelLayer.value);
});

</script>

<template>
  <div
    v-if="show_modal"
    class="rect_overlay fixed top-0 left-0 w-fit h-fit "
    :style="{ 'top': position.y + 'px', 'left': position.x + 'px' }"
  >
    <div class="absolute text-white w-max">
      <LandmarkLabel
        class=" -top-2"
        :name="title"
        :color="fillValue"
      />
    </div>
  </div>
</template>
