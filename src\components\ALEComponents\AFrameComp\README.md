# AFrameComp Component

A Vue 3 component for displaying 360-degree images with interactive hotspots using A-Frame.

## Changes Made

The component has been updated to accept a complete scene object instead of separate thumbnail and highRes props, and now supports:

1. **Object-based props**: Instead of separate `thumbnail` and `highRes` props, the component now accepts a single `sceneData` object
2. **Interactive hotspots**: Displays clickable hotspots for navigation links
3. **Camera rotation**: Sets initial camera angle based on scene rotation data

## Props

### sceneData (Object, required)
A complete scene object containing:

```javascript
{
  "_id": "string",
  "project_id": "string", 
  "name": "string",
  "category": "string",
  "thumbnail": "string", // URL for low-res preview image
  "media_type": "360_image",
  "file": "string", // URL for high-res 360 image
  "media": [],
  "__v": 0,
  "modified": "string",
  "order": 1,
  "links": { // Optional - hotspot navigation links
    "linkId": {
      "_id": "string",
      "position": {
        "x": "string", // X coordinate for hotspot
        "y": "string", // Y coordinate for hotspot  
        "z": "string"  // Z coordinate for hotspot
      },
      "text": "string", // Display text for hotspot
      "destination_img_id": "string" // Target scene ID
    }
  },
  "rotation": "string" // Optional - camera rotation "x y z"
}
```

## Events

### @progress-data
Emitted during image loading with progress percentage (0-100) or `false` when complete.

### @hotspot-click
Emitted when a hotspot is clicked, passes the complete link object.

## Usage

### Basic Usage
```vue
<template>
  <AFrameComp
    :sceneData="sceneObject"
    @progress-data="handleProgress"
    @hotspot-click="handleHotspotClick"
  />
</template>

<script setup>
import AFrameComp from './AframeComp.vue';

const sceneObject = {
  // ... scene data object
};

const handleProgress = (progress) => {
  console.log('Loading progress:', progress);
};

const handleHotspotClick = (linkData) => {
  console.log('Navigate to:', linkData.destination_img_id);
  // Handle navigation logic here
};
</script>
```

### Migration from Old Usage

**Before:**
```vue
<AFrameComp
  :thumbnail="item.thumbnail"
  :highRes="item.file"
  @progress-data="handleProgress"
/>
```

**After:**
```vue
<AFrameComp
  :sceneData="item"
  @progress-data="handleProgress"
  @hotspot-click="handleHotspotClick"
/>
```

## Features

- **Progressive loading**: Shows low-res thumbnail first, then loads high-res image
- **Interactive hotspots**: Red spheres with text labels for navigation
- **Camera rotation**: Initial camera angle based on scene rotation data
- **Responsive design**: Works on desktop and mobile devices
- **A-Frame integration**: Full A-Frame scene with orbit controls

## Hotspot Styling

Hotspots appear as:
- Red animated spheres (`#ff6b6b` color)
- Pulsing scale animation
- White text labels with black background
- Clickable interaction

## Dependencies

- Vue 3 (Composition API)
- A-Frame (loaded globally)
- Helper functions: `cdn()`, `loadImageData()`
