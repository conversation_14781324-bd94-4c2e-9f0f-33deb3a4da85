import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
// export default defineConfig({
//   build: {
//     lib: {
//       entry: path.resolve(__dirname, 'src/components/index.js'),
//       name: 'propvr-ui-library',
//       fileName: (name,format) => `${name}.${format}.js`,
//     },
//     rollupOptions: {
//       external: ['vue'],
//       output: {
//         // Provide global variables to use in the UMD build
//         // Add external deps here
//         globals: {
//           vue: 'Vue',
//         },
//       },
//     },
//   },
//   plugins: [vue()],
// })
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})