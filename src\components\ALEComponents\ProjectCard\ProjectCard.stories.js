import ProjectCard from './projectCardComponent.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/ProjectCard',
  component: ProjectCard,
  tags: ['autodocs'],

};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary = {
  args: {
    name: 'Prestige Park Grove',
    units: 12823,
    amenities: 152321,
    floor: 2624,
    location: "Bangalore",
    id: "####",
    thumbnail: "https://images.adsttc.com/media/images/649c/670f/5921/183e/3b97/89cc/medium_jpg/london-architecture-city-guide-20-modern-and-contemporary-attractions-to-explore-in-uks-cultural-and-financial-powerhouse_1.jpg?1687971619",
    status: true,
    isDisabled: false,
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1127-9833&mode=dev",
    },
  },
};
