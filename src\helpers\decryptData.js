import CryptoJS from "crypto-js";
import pako from "pako";

const keyValue = import.meta.env.VITE_APP_ENCRYPTION_KEY;
const ivKey = import.meta.env.VITE_APP_ENCRYPTION_IV;

function getKeyIV () {
  if (!keyValue || !ivKey) {
    throw new Error(
      "Encryption key or IV is not defined in environment variables.",
    );
  }
  const key = CryptoJS.enc.Base64.parse(keyValue);
  const iv = CryptoJS.enc.Base64.parse(ivKey);
  return { key, iv };
}

export function decrypt (encryptedData) {
  if (encryptedData) {
    const { key, iv } = getKeyIV();

    const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
    });

    const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
    const uint8Array = Uint8Array.from(atob(decryptedString), (c) =>
      c.charCodeAt(0),
    );

    const decompressed = pako.inflate(uint8Array, { to: "string" });

    return JSON.parse(decompressed);
  }
  throw new Error("No data provided for decryption.");
}
