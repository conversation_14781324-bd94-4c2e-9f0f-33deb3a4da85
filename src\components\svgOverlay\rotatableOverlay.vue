<script setup>
import { ref, defineProps, defineEmits, watch, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import ImageLayer from '../svgOverlay/Layers/oldimageLayer.vue';
import IndexLayer from '../svgOverlay/IndexLayer.vue';
// Import SwiperButton from '../ALEComponents/SwiperButton/SwiperButton.vue';
import RangeSlider from '../ALEComponents/RangeSlider/RangeSlider.vue';
import { creationToolStore } from '../../store';
// Import  svgPanZoom from "svg-pan-zoom";
import { cdn } from '../../helpers/helper';
import { fetchImageURL } from '../../helpers/API';
import { availUnitOrgs } from '../../config/masterdata';

const Store = creationToolStore();
const layers = ref({});
const transitionHelpRef = ref(null);
const route = useRoute();
const svgLoaded=ref(false);
var isDragging = false;
var isTouching = false;
let previousMouseX = 0;
let accumulatedDeltaX = 0;
var initialTouchX = 0;
var imageIndex = 0;
Store.show360=true;
const sliderValue= ref(null);
const totalFrames = ref(0);
const portaltargets = ref([]);
const emit = defineEmits([
  'clickEvent',
  'mouseEnter',
  'mouseLeave',
  'imageHigResLoaded',
  'imageLowResLoaded',
  'svgLoaded',
  'transitionEnd',
  'onClick',
  'removeLoader',
]);
const containerRef = ref(null), SVGLayer = ref(false), lowResLoaded = ref(false);
let autoRotateInterval;

const props = defineProps({
  data: {
    type: Object,
    default () {
      return {};
    },
  },
  bucketURL: {type: String, default: ""},
  replaceURL: {type: String, default: ""},
  landmarkData: {
    type: Object,
    default () {
      return {};
    },
  },
});
let totalTiles =Object.keys(props.data).length;
let accumulatedTouchDeltaX = 0;
const baseThreshold = 200;
let dragThreshold = Math.round(baseThreshold/totalTiles);
let touchThreshold = Math.round(baseThreshold/totalTiles);
const fetchAllImageURLs = () => {
  const promises = Object.keys(props.data).map((key) => {
    return fetchImageURL(
      cdn(Store.rotatableFrames[route.params.projectId][key].sceneData.background.low_resolution),
    );
  });
  // Wait for all fetch operations to complete
  return Promise.all(promises)
    .then((results) => {
      return results;
    })
    .catch((error) => {
      throw error;
    });
};

async function readSVGLink (data, frameId) {
  return new Promise((resolve) => {
    var requestOptions = {
      method: "GET",
      redirect: "follow",
    };
    if (data?.svg_url) {
      const url =  cdn(data.svg_url);
      fetch(url, requestOptions)
        .then((response) => response.text())
        .then((result) => {
          var tempElement = document.createElement('div');
          tempElement.innerHTML = result;
          layers.value[frameId] = {};
          // Portaltargets.value[frameId] = []
          for (let i = 0; i<tempElement.children.length;i++){
            for (let j = 0; j<tempElement.children[i].children.length;j++){
              let layerType;
              if (data.layers[tempElement.children[i].children[j].id]){
                layerType =data.layers[tempElement.children[i].children[j].id].type;
                if (!portaltargets.value.includes(layerType)) {
                  portaltargets.value.push(layerType);
                }
              } else {
                layerType='style';
              }
              layers.value[frameId][layerType] = layers.value[frameId][layerType]?layers.value[frameId][layerType]:layerType==='style'?[]:{};
              if (layerType==='style'){
                layers.value[frameId][layerType].push(
                  {
                    layer_data: data.layers[tempElement.children[i].children[j].id],
                    layer: tempElement.children[i].children[j],
                  },
                );
              } else {
                layers.value[frameId][layerType][tempElement.children[i].children[j].id]=
                {
                  layer_data: data.layers[tempElement.children[i].children[j].id],
                  layer: tempElement.children[i].children[j],

                };
              }
            }
          }
          resolve();
        });
    }

  });
}
function scrollToCenter () {
  const container = containerRef.value;
  const scrollTarget = SVGLayer.value;
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;

  if (screenWidth < 991 && container) {
    const targetWidth = scrollTarget.clientWidth;
    const scrollLeft = (targetWidth - screenWidth) / 2;
    container.scrollLeft = scrollLeft;
  }

  if (screenHeight < 700 && container) {
    const targetHeight = scrollTarget.clientHeight;
    const scrollTop = (targetHeight - screenHeight) / 2;
    container.scrollTop = scrollTop;
  }
}

function handleSliderValueChange (newValue) {
  sliderValue.value = newValue;
  Store.currentImageId = Object.keys(props.data)[sliderValue.value];
}
function stopAutoRotate () {
  clearInterval(autoRotateInterval);
}

function rotate (direction) {
  // Update the image index with wrapping
  imageIndex = (imageIndex + direction + totalTiles) % totalTiles;

  // Update the current image
  Store.currentImageId = Object.keys(props.data)[imageIndex];

  // Optionally trigger any additional updates (like panzoom setup)
  // setupPanzoom();
}
watch(sliderValue, (newValue) => {
  handleSliderValueChange(newValue);
  stopAutoRotate();
});

// Function findCenterOfDiv () {
//   Const panstart_panmove = new CustomEvent('hammer_panstart_panmove', {
//     Detail: {
//       Key: 'event',
//     },
//   });
//   Document.dispatchEvent(panstart_panmove);

// }
function initSVG (){
  layers.value = {};
  svgLoaded.value=false;
  Object.values(props.data).forEach((frame) => {
    const promises = Object.values(frame.svgData).map((svg) => readSVGLink(svg, frame.sceneData._id));
    Promise.all(promises).then(() => {
      svgLoaded.value = true;

      setTimeout(() => {
        scrollToCenter();
        // Initialize panzoom for each SVG
      }, 100);

    });

  });
}
initSVG();
Store.currentImageId=Object.keys(props.data)[imageIndex];
watch(props, () => {
  lowResLoaded.value=false;
  Store.currentImageId=Object.keys(props.data)[imageIndex];
  fetchAllImageURLs()
    .then(() => {
      emit('removeLoader');
    });
  totalTiles = Object.keys(props.data).length;
  dragThreshold=Math.round(baseThreshold/totalTiles);
  touchThreshold=Math.round(baseThreshold/totalTiles);

  initSVG();
  // SetTimeout(()=>{
  //     SetupPanzoom()
  //   }, 200)
});

Store.currentImageId=Object.keys(props.data)[imageIndex];
function lowresLoaded (index){
  if (Store.rotatableFrames[route.params.projectId] && Object.values(Store.rotatableFrames[route.params.projectId]).length === index+1){
    emit('removeLoader');
    lowResLoaded.value=true;
    if (transitionHelpRef.value) {

      transitionHelpRef.value.startAnimation();
    }
  }
}

function mouseDown (event) {
  event.preventDefault();
  isDragging = true;
  previousMouseX = event.clientX;
  stopAutoRotate();
  containerRef.value.style.cursor = "grabbing";
}

function mouseMove (event) {
  event.preventDefault();
  if (!isDragging) {
    return;
  }

  // Calculate delta
  const deltaX = event.clientX - previousMouseX;
  accumulatedDeltaX += deltaX;

  // Check if movement exceeds the threshold for an index change
  if (Math.abs(accumulatedDeltaX) >= dragThreshold) {
    const direction = accumulatedDeltaX > 0 ? -1 : 1; // Left drag = next image, right drag = previous image
    rotate(direction);

    // Reset accumulated delta but keep remainder for smoother experience
    accumulatedDeltaX %= dragThreshold;
  }

  previousMouseX = event.clientX;
}

function mouseUp (event) {
  event.preventDefault();
  isDragging = false;
  stopAutoRotate();
  containerRef.value.style.cursor = "grab";
}

function touchStart (event) {
  isTouching = true;
  initialTouchX = event.touches[0].clientX;
}
function touchMove (event) {
  if (!isTouching) {
    return;
  }

  const currentTouchX = event.touches[0].clientX;
  const deltaX = currentTouchX - initialTouchX;
  accumulatedTouchDeltaX += deltaX;

  // Check if accumulated delta exceeds the threshold for an image change
  if (Math.abs(accumulatedTouchDeltaX) >= touchThreshold) {
    const direction = accumulatedTouchDeltaX > 0 ? -1 : 1; // Left swipe = next image, right swipe = previous image
    rotate(direction);

    // Reset accumulated delta but retain remainder
    accumulatedTouchDeltaX %= touchThreshold;
  }

  // Update the initial touch point for the next move
  initialTouchX = currentTouchX;
}
function touchEnd () {
  isTouching = false;
  accumulatedTouchDeltaX = 0; // Reset accumulated delta on touch end
}
//  Function initpanZoom (options) {
//   Var instance = options.instance,
//     InitialScale = 1,
//     PannedX = 0,
//     PannedY = 0;
//   This.hammer = window.Hammer(options.svgElement, {
//     InputClass: window.Hammer.SUPPORT_POINTER_EVENTS ? window.Hammer.PointerEventInput : window.Hammer.TouchInput,
//   });

//   This.hammer.get('pinch').set({enable: true});

//   This.hammer.on('doubletap', function (){
//     Instance.zoomIn();
//   });

//   This.hammer.on('panstart panmove', function (ev){

//     If (ev.type === 'panstart') {
//       PannedX = 0;
//       PannedY = 0;
//     }

//     Instance.panBy({x: ev.deltaX - pannedX, y: ev.deltaY - pannedY});
//     PannedX = ev.deltaX;
//     PannedY = ev.deltaY;
//   });

//   This.hammer.on('pinchstart pinchmove', function (ev){

//     If (ev.type === 'pinchstart') {
//       InitialScale = instance.getZoom();
//       Instance.zoomAtPoint(initialScale * ev.scale, {x: ev.center.x, y: ev.center.y});
//     }
//     Instance.zoomAtPoint(initialScale * ev.scale, {x: ev.center.x, y: ev.center.y});
//   });

//   Options.svgElement.addEventListener('touchmove', function (e){
//     E.preventDefault();
//   });
//   Options.svgElement.addEventListener('mousemove', function (e){
//     E.preventDefault();
//   });
// }
// Function setupPanzoom (){
//   Var beforePan;
//   Var eventsHandler;
//   BeforePan = function (oldPan, newPan){
//     Var gutterWidth = this.getSizes().width,
//       GutterHeight = this.getSizes().height,
//       // Computed variables
//       Sizes = this.getSizes(),
//       LeftLimit = -((sizes.viewBox.x + sizes.viewBox.width) * sizes.realZoom) + gutterWidth,
//       RightLimit = sizes.width - gutterWidth - (sizes.viewBox.x * sizes.realZoom),
//       TopLimit = -((sizes.viewBox.y + sizes.viewBox.height) * sizes.realZoom) + gutterHeight,
//       BottomLimit = sizes.height - gutterHeight - (sizes.viewBox.y * sizes.realZoom);

//     Var customPan = {};
//     CustomPan.x = Math.max(leftLimit, Math.min(rightLimit, newPan.x));
//     CustomPan.y = Math.max(topLimit, Math.min(bottomLimit, newPan.y));
//     Return customPan;
//   };
//   EventsHandler = {
//     HaltEventListeners: ['touchstart', 'touchend', 'touchmove', 'touchleave', 'touchcancel'],
//     Init: initpanZoom,
//   };
//   Const panzoomer =  svgPanZoom(`#SVGLayer`, {
//     ViewportSelector: '.svg-pan-zoom_viewport',
//     ZoomEnabled: true,
//     ControlIconsEnabled: false,
//     Fit: false,
//     Contain: true,
//     Center: true,
//     BeforePan: beforePan,
//     OnZoom: findCenterOfDiv,
//     OnPan: findCenterOfDiv,
//     PreventMouseEventsDefault: true,
//     DblClickZoomEnabled: false,
//     MinZoom: 1,
//     CustomEventsHandler: eventsHandler,
//   });
//   Window.panzoomer = panzoomer;
// }
onMounted(() => {
  containerRef.value.addEventListener("mousedown", mouseDown);
  containerRef.value.addEventListener("mouseup", mouseUp);
  containerRef.value.addEventListener("mousemove", mouseMove);
  containerRef.value.addEventListener("mouseleave", mouseUp);
  containerRef.value.addEventListener("touchstart", touchStart);
  containerRef.value.addEventListener("touchmove", touchMove);
  containerRef.value.addEventListener("touchend", touchEnd);
  containerRef.value.addEventListener("touchcancel", touchEnd);
  containerRef.value.addEventListener("click", stopAutoRotate);

});

onMounted(() => {
  if (Store && Store.rotatableFrames[route.params.projectId]) {
    const frameCount = Object.keys(Store.rotatableFrames[route.params.projectId]).length;
    totalFrames.value = frameCount;
  }
});
onUnmounted(() => {
  Store.show360 = false;

});
</script>

<template>
  <div
    id="maincompview"
    class="svgContainer "
  >
    <div
      ref="containerRef"
      class="svgWrapper"
    >
      <div
        id="svgContent"
        class="svgContent"
      >
        {{ Store.currentImageId }}
        <div
          v-for="(frame, frameId, index) in Store.rotatableFrames[route.params.projectId]"
          :key="frameId"
        >
          <div
            v-show="svgLoaded && frame.sceneData._id === Store.currentImageId"
            class="h-full w-full absolute top-0"
          >
            <svg
              id="SVGLayer"
              ref="SVGLayer"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              viewBox="0 0 1920 1080"
              style="enable-background:new 0 0 1920 1080;"
              xml:space="preserve"
              preserveAspectRatio="xMidYMid slice"
              class="svgLayer z-20"
            >
              <ImageLayer
                :lowres="frame.sceneData.background.low_resolution"
                :highres="frame.sceneData._id === Store.currentImageId?frame.sceneData.background.high_resolution:false"
                :video="frame.sceneData.video"
                style="transition: transform 1s opacity 0.1s ease-in;"
                @lowres-loaded="lowresLoaded(index)"
                @highres-loaded="highresLoaded"
              />
              <IndexLayer
                v-if="Store.currentImageId === frame.sceneData._id"
                :layer-data="layers[frameId]"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- <SwiperButton
      class="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[0] flex gap-2"
      :class="Store.isMobile ? 'bottom-[4.5rem]' : 'bottom-4'"
      @handleleftarrow="rotate(1)"
      @handlerightarrow="rotate(-1)"
    /> -->
    <RangeSlider
      v-if="totalTiles && !(availUnitOrgs.includes(route.params.organizationId))"
      v-model="sliderValue"
      class="absolute  transform -translate-x-1/2 -translate-y-1/2 flex gap-2"
      :class="Store.isMobile ? (route.fullPath.includes('exterior') ? 'left-[9.5rem] -bottom-8 p-3' : 'left-1/2 bottom-[4.5rem]') : 'left-1/2 bottom-4'"
      :totalCountOfFrame="totalTiles"
      @update:slider-value="handleSliderValueChange"
    />
    <portal-target
      v-for="name in portaltargets"
      :key="name"
      :name="name"
    >
      <slot />
    </portal-target>
  </div>
</template>

<style scoped>
.svgContainer {
  height: 100%;
  width: 100%;
  position: relative;
  user-select: none;
  overflow: hidden;
}
.svgWrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.fade-in-overlay {
  opacity: 0;
  transition: opacity 2s ease;
}

.fade-in-animation {
  opacity: 1;
}

.svgContainer.active {
  transform: scale(1.2);
  opacity: 0;
}

.svgContent {
  width: 100%;
  height: 100%;
  overflow-x: auto;
  white-space: nowrap;
  scroll-behavior: smooth;
}

.svgContent::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.svg-pan-zoom_viewport
{
  height: 100%;
  width: 100%;
}

.svgLayer {
  transition: transform 2s, opacity 0.5s ease-in-out;
}
</style>
