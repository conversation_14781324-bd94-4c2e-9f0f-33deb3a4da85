import BreadCrumb from './BreadCrumb.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/BreadCrumb',
  component: BreadCrumb,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    breadCrumbData: [
      {
        "_id": "656d6bc71555c0e410e73f8e",
        "name": "India",
        "type": "master",
      },
      {
        "_id": "656dcc063fc4f8392b4e66d8",
        "name": "Karnataka",
        "type": "master",
      },
      {
        "_id": "656dc4833fc4f8392b4e66a6",
        "name": "Bengaluru",
        "type": "master",
      },
      {
        "_id": "656dc4833fc4f8392b4e66a6",
        "name": "Whitefield",
        "type": "master",
      },
      {
        "_id": "656dc4833fc4f8392b4e66a6",
        "name": "Prestige Park Grove",
        "type": "master",
      },
      {
        "_id": "656dc4833fc4f8392b4e66a6",
        "name": "Amenity",
        "type": "master",
      },
      {
        "_id": "656dc4833fc4f8392b4e66a6",
        "name": "BasketBall Court",
        "type": "master",
      },
      {
        "_id": "656dc4833fc4f8392b4e66a6",
        "name": " Court",
        "type": "master",
      },
      {
        "_id": "656dc4833fc4f8392b4e66a6",
        "name": "Kitchen",
        "type": "master",
      },
    ],
    // PropertyData : {
    //   "unit-104":
    //       {
    //         "name":"unit -104",
    //         "status" : "Available",
    //         "units": 4
    //       } ,
    //       "unit-562":{
    //         "name":"unit -562",
    //       "status" : "Sold",
    //       "units": 5
    //       },
    //      " unit-529":{
    //         "name":"unit -529",
    //         "status" : "Available",
    //       "units": 8
    //       },
    //       "unit-362":{
    //         "name":"unit -362",
    //       "status" : "",
    //       "units": 1
    //       },
    //       "unit-114":{
    //         "name":"unit -114",
    //         "status" : "Sold",
    //         "units": 9
    //       },
    //       "unit -262":{
    //         "name":"unit -262",
    //       "status" : "",
    //       "units": 2
    //       },
    //       "unit -100":{
    //         "name":"unit -100",
    //         "status" : "Available",
    //         "units": 9
    //       },
    //       "unit -562":{
    //         "name":"unit -562",
    //       "status" : "",
    //       "units": 4
    //       },

    // },
    // ShowRadius:true,
    logo: "https://storagecdn.prestigemetaworld.com/assets/logo/favicon.png",
    moveToScene: () => {

    },
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=142-31410&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
