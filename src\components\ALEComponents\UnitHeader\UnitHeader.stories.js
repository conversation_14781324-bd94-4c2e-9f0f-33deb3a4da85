import UnitHeader from './UnitHeader.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/UnitHeader',
  component: UnitHeader,
  tags: ['autodocs'],
  argTypes: {
  },
};

export const Primary = {
  args: {
    modalWidth: 23,
    floor: 4,
    units: 32,
    area: 3200,
    minBedrooms: 1,
    maxBedrooms: 3,
    unitname: "Unit -456",
    status: "Available",
    currency: "INR",
    price: 2000,
    bedroom: 3,
  },
  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=771-40188&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
