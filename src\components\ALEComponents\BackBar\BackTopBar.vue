<script setup>
import { defineEmits, defineProps} from 'vue';

const props = defineProps({
  sceneName: {
    type: String,
    default () {
      return '';
    },
  },
  isRoot: {
    type: Boolean,
    default () {
      return false;
    },
  },
  thumbnail: {
    type: String,
    default () {
      return '';
    },
  },
});
const emit = defineEmits(['goBack', 'onClick']);
// Const showUnitplanCard=ref(false);
// Function gotoUnit (){
//   Router.push({name: "unit.Child", params: {unitplanId: Store.allUnitCardData[showUnitplanCard.value].unitplan_id}, query: {...route.query, unit_id: showUnitplanCard.value}});
//   ShowUnitplanC ard.value=false;
// }

</script>

<template>
  <div
    v-if="!props.isRoot || props.thumbnail"
    class="h-[48px] w-full fixed left-0 flex justify-between bg-black sm:absolute top-0 sm:left-2 sm:top-[50%] sm:-translate-y-[50%]"
  >
    <div
      class="px-4 sm:min-h-fit sm:max-h-full w-fit bg-blend-luminosity backdrop-blur-[3.125rem] flex sm:flex-col justify-start items-center"
    >
      <svg
        v-if="!props.isRoot"
        class="h-5 w-5 mr-5"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        @click="emit('goBack')"
      >
        <path
          d="M3.1012 13.488L19.8207 13.488C20.5493 13.488 21.1406 12.8966 21.1406 12.168C21.1406 11.4394 20.5493 10.8481 19.8207 10.8481L3.1012 10.8481C2.37259 10.8481 1.78125 11.4394 1.78125 12.168C1.78125 12.8966 2.37259 13.488 3.1012 13.488Z"
          fill="white"
        />
        <path
          d="M9.70234 20.0877C10.0404 20.0877 10.3781 19.9591 10.6353 19.7007C11.1509 19.1847 11.1509 18.3488 10.6353 17.8332L4.96812 12.1664L10.6353 6.50117C11.1509 5.98524 11.1509 5.14932 10.6353 4.63371C10.1197 4.11811 9.28374 4.11811 8.76781 4.63371L2.16806 11.2335C1.65245 11.7491 1.65245 12.585 2.16806 13.1009L8.76781 19.7007C9.02658 19.9591 9.36462 20.0877 9.70234 20.0877Z"
          fill="white"
        />
      </svg>

      <p
        v-if="!props.isRoot"
        class="text-white font-normal text-lg"
      >
        {{ props.sceneName }}
      </p>
      <img
        v-else
        :src="props.thumbnail"
        class="h-[60%] object-contain"
      >
    </div>
  </div>
</template>

<style scoped>
</style>
