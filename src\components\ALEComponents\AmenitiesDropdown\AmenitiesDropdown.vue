<template>
  <div
    ref="dropdownRef"
    class="dropdownamen bg-secondary cursor-pointer w-[172px] h-[65px]  flex justify-center items-center rounded-[0.5em]"
    aria-label="Project Drop Down Filter"
    @click="toggleDropdown"
  >
    <div class="flex flex-col justify-center w-[156px] h-[53px] items-center content-center gap-[1px_12px] shrink-0 flex-wrap p-2 rounded-xl">
      <!-- add svg here -->
      <div class="w-10 h-10 shrink-0 border border-[#B6BAC3] flex items-center justify-center shadow-[0_0_6px_2px_rgb(0_0_0_/_12%)] rounded-lg border-solid">
        <slot>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3.54886 7.39694C4.40434 5.36656 6.18578 3.8185 8.36725 3.27637C8.40131 3.62373 8.51905 3.94667 8.70014 4.22495C6.92133 4.6379 5.44378 5.82611 4.64421 7.41028C4.4618 7.36 4.26962 7.33313 4.07114 7.33313C3.89099 7.33313 3.71603 7.35526 3.54886 7.39694ZM15.5108 7.36081C14.8108 6.00504 13.6118 4.94528 12.1538 4.42307C12.3671 4.16557 12.521 3.8574 12.5944 3.51942C14.4487 4.2165 15.9325 5.66526 16.6708 7.49241C16.4198 7.38975 16.145 7.33313 15.8569 7.33313C15.739 7.33313 15.6234 7.3426 15.5108 7.36081ZM12.0693 15.8572C12.3004 16.1046 12.4733 16.4067 12.566 16.7419C14.9344 15.8631 16.7038 13.7601 17.0979 11.2058C16.7841 11.4281 16.4076 11.5684 15.9999 11.5951C15.5001 13.5899 14.0062 15.1956 12.0693 15.8572ZM8.38275 16.9782C8.43705 16.6317 8.57511 16.3128 8.77567 16.0428C6.5208 15.5495 4.737 13.8137 4.1808 11.5971C4.14448 11.5989 4.10792 11.5998 4.07114 11.5998C3.72569 11.5998 3.39933 11.5184 3.11035 11.3739C3.60255 14.1194 5.68918 16.3152 8.38275 16.9782Z"
              fill="white"
            />
            <ellipse
              cx="10.5001"
              cy="3.06667"
              rx="1.07143"
              ry="1.06667"
              fill="white"
            />
            <ellipse
              cx="4.07143"
              cy="9.46657"
              rx="1.07143"
              ry="1.06667"
              fill="white"
            />
            <ellipse
              cx="10.5001"
              cy="16.9334"
              rx="1.07143"
              ry="1.06667"
              fill="white"
            />
            <ellipse
              cx="16.9289"
              cy="9.46657"
              rx="1.07143"
              ry="1.06667"
              fill="white"
            />
          </svg>
        </slot>
      </div>
      <div class="flex flex-col">
        <p class="text-md text-text-secondary">
          {{ title }}
        </p>
        <div>
          <div class="flex justify-around gap-3">
            <div class="flex items-center cursor-pointer">
              <div class="overflow-hidden whitespace-nowrap w-20 text-sm text-ellipsis text-text-secondary">
                {{
                  selectedOption }}
              </div>
              <div class="themesvg-secondary-fill">
                <svg
                  v-if="!isOpen"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="red"
                    d="M18 15a1 1 0 0 1-.64-.23L12 10.29l-5.37 4.32a1 1 0 0 1-1.41-.15a1 1 0 0 1 .15-1.41l6-4.83a1 1 0 0 1 1.27 0l6 5a1 1 0 0 1 .13 1.41A1 1 0 0 1 18 15"
                  />
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="green"
                    d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15a1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div class="dropdown-list  md:right-auto !-right-[13px] md:w-[350px] items-center w-screen flex ">
            <ul
              :class="{ 'w-[95%] slide-top absolute flex flex-col bg-secondary backdrop-blur(3.3rem) bg-opacity-40 rounded-lg align-middle': isOpen,'hidden w-[95%] slide-bottom absolute flex-col bg-secondary bg-opacity-60 rounded-lg align-middle': !isOpen }"
            >
              <div
                class="flex justify-between pt-3 px-3 align-middle"
                @click="toggleDropdown"
              >
                <p class="text-text-secondary text-xl">
                  Amenities
                </p>
                <div class="themesvg-secondary-fill">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="#fff"
                    class="w-6 h-6 cursor-pointer"
                    @click="toggleDropdown"
                  >
                    <path
                      fill="#fff"
                      d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15a1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16"
                    />
                  </svg>
                </div>
              </div>
              <div
                id="scroll-style"
                class="child-dropdown-li  scroll"
              >
                <li
                  v-for="item in lists"
                  :key="item"
                  style="border-radius: 2em;height: 40px;"
                  :class="{ 'selected': item._id === selectedId }"
                  class="flex flex-row gap-2 text-sm items-center justify-start"
                  @click="selectOption(item)"
                >
                  <!-- <div class="h-6 w-6 mr-[10px]" v-html="props.svg"></div> -->
                  <p class="text-text-secondary ">
                    {{ item.name }}
                  </p>
                </li>
              </div>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

import { ref, defineProps, onMounted, onUnmounted } from 'vue';

const isOpen = ref(false);
const selectedOption = ref('Any');
defineProps({
  lists: {
    type: Array,
    required: true,
  },
  title: {
    type: String,
    default: "",
  },
});

const dropdownRef = ref(null);

const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
};
const selectedId = ref(null);
const emit = defineEmits(['goToProject']);
const selectOption = (list) => {
  selectedOption.value = list.value;
  selectedId.value = list.id;
  isOpen.value = true;
  // ToggleDropdown()
  emit('goToProject', list);
};

const closeDropdownOnOutsideClick = (event) => {
  if (isOpen.value && !dropdownRef.value.contains(event.target  ) ) {
    isOpen.value = false;
  }
};
onMounted(() => {

  document.addEventListener('click', closeDropdownOnOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
});

</script>

<style scoped>

.dropdownamen {
    /* cursor: pointer; */
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid #fff;
}

.fullscreen-image {
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    position: fixed;
    top: 0;
    left: 0;
}

p {
    @apply flex items-center;
}

svg {
    @apply w-5 h-5;
}

.selected {
    @apply text-[white] rounded-[3em];
    border-radius: 8px;
    border: 1px solid #6B7280 !important;
    background: rgba(0, 0, 0, 0.65) !important;
    backdrop-filter: blur(10px) !important;
}

.dropdown-list li {
    @apply cursor-pointer  px-6 py-4 hover:rounded-[2em] hover:border-[#FFF];
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.40);
    backdrop-filter: blur(10px);
    color: rgba(255, 255, 255, 1);
    border: 1px solid rgba(0, 0, 0, 0.40);
    gap: 0.25rem;
}

.scroll {
    @apply overflow-y-auto flex flex-col gap-3;
}

#scroll-style::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    background-color: #F5F5F5;
}

#scroll-style::-webkit-scrollbar {
    width: 8px;
    background-color: #F5F5F5;
    border-radius: 8px;
}

#scroll-style::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #555;
}

/*  styling the filterDropDown.vue   */

.dropdown-list {
    /* background: rgba(0, 0, 0, 0.50); */
    /* color: rgba(0, 0, 0, 0.5); */
    backdrop-filter: blur(5px);
    @apply absolute flex flex-col max-h-[365px] min-w-[200px] right-[0em] bottom-20 p-0 shadow-2xl rounded-xl;
}

.scroll {
    @apply overflow-y-auto p-[0.5em];
}

.active {
    outline: 3px solid #262626 !important;
}

.slide-top {
	-webkit-animation: slide-top 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
	animation: slide-top 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

@keyframes slide-top {
  0% {
    bottom:-1rem;
  }
  100% {
    bottom:1rem;
  }
}

.slide-bottom {
	animation: slide-bottom 0.5s cubic-bezier(0.6, 0.09, 0.44, 0.6) both;
}

@keyframes slide-bottom {
  0% {
    bottom:1rem;
    filter: blur(0);
    opacity: 1;
    display: flex;
  }
  100% {
    bottom:-3rem;
    filter: blur(1rem);
    opacity: 0;
    display:none;
  }
}

</style>
