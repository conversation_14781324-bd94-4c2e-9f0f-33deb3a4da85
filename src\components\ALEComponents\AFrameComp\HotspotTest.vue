<template>
  <div class="test-container">
    <h3>Hotspot Test</h3>
    <p>Links found: {{ Object.keys(testData.links || {}).length }}</p>
    <div v-if="Object.keys(testData.links || {}).length > 0">
      <h4>Link Details:</h4>
      <div v-for="(link, linkId) in testData.links" :key="linkId">
        <p>{{ link.text }} at position: {{ link.position.x }}, {{ link.position.y }}, {{ link.position.z }}</p>
      </div>
    </div>
    
    <div class="scene-wrapper">
      <AFrameComp
        :sceneData="testData"
        @progress-data="handleProgress"
        @hotspot-click="handleHotspotClick"
      />
    </div>
    
    <div v-if="lastClicked" class="click-info">
      Last clicked: {{ lastClicked.text }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AFrameComp from './AframeComp.vue';

const lastClicked = ref(null);

// Test data with your exact format
const testData = ref({
  "_id": "67455fd5e83433098cba1d1d",
  "project_id": "673db6162fa83b3e755e5d14",
  "name": "Saloooon",
  "category": "asada",
  "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F7T1pfE%2Fprojects%2F673db6162fa83b3e755e5d14%2Famenities%2F67455fd5e83433098cba1d1d%2FSaloon_thumb_1732599765630.webp?alt=media",
  "media_type": "360_image",
  "file": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F7T1pfE%2Fprojects%2F673db6162fa83b3e755e5d14%2Famenities%2F67455fd5e83433098cba1d1d%2FSaloon_1732599765629.jpg?alt=media",
  "media": [],
  "__v": 0,
  "modified": "2025-07-29T10:36:25.284Z",
  "order": 1,
  "links": {
    "6888a4481f308d97e67fa6af": {
      "_id": "6888a4481f308d97e67fa6af",
      "position": {
        "x": "4.7700446372678424",
        "y": "0.7536656392312024",
        "z": "1.2956419935516132"
      },
      "text": "Pool",
      "destination_img_id": "67455faee83433098cba1d02"
    }
  },
  "rotation": "-4.47 -93.85 0"
});

const handleProgress = (progress) => {
  console.log('Progress:', progress);
};

const handleHotspotClick = (linkData) => {
  console.log('Hotspot clicked in test:', linkData);
  lastClicked.value = linkData;
  alert(`Clicked hotspot: ${linkData.text}`);
};

// You can also test with different positions
const addTestHotspot = () => {
  testData.value.links.testHotspot = {
    "_id": "test123",
    "position": {
      "x": "0",
      "y": "0",
      "z": "-3"
    },
    "text": "Test Hotspot",
    "destination_img_id": "test_destination"
  };
};
</script>

<style scoped>
.test-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.scene-wrapper {
  width: 100%;
  height: 70vh;
  position: relative;
}

.click-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  z-index: 100;
}
</style>
