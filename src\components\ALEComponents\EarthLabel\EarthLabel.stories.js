import EarthLabel from './EarthLabel.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/EarthLabel',
  component: EarthLabel,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    name: 'The USA',
    active: true,
  },
  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-1014&mode=dev ",
    },
  },
};
