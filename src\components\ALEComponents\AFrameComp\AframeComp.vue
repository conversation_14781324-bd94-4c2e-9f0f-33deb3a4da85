<script setup>
import { ref, onMounted, computed } from 'vue';
import { cdn, loadImageData } from '../../../helpers/helper';

const emit = defineEmits(['progressData', 'hotspotClick']);

const props = defineProps({
  sceneData: {
    type: Object,
    required: true,
  },
});

const highResDataLoaded = ref(false);

// Computed properties for easier access
const thumbnail = computed(() => props.sceneData.thumbnail);
const highResFile = computed(() => props.sceneData.file);
const rotation = computed(() => props.sceneData.rotation || "0 0 0");
const links = computed(() => props.sceneData.links || {});

const loadHighResImage = (imgURL) => {
  loadImageData(imgURL, (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false)).then((highRes) => {
    highResDataLoaded.value = highRes;
  });
};

const handleHotspotClick = (linkData) => {
  emit('hotspotClick', linkData);
};

onMounted(() => {
  emit('progressData', 0);
  loadHighResImage(cdn(highResFile.value));
});

</script>

<template>
  <a-scene
    embedded
    class="h-[100vh] sample"
    style="position: absolute; top: 0;"
    loading-screen="enabled: false"
    vr-mode-ui="enabled: false"
    device-orientation-permission-ui="enabled: false"
    assetloaded=""
    rotation-reader=""
    renderer="colorManagement: true; sortObjects: true; maxCanvasWidth: 1920; maxCanvasHeight: 1920;"
    gltf-model="dracoDecoderPath: https://propvr.tech/draco/"
    keyboard-rot=""
    inspector=""
    keyboard-shortcuts=""
    screenshot=""
  >
    <a-assets>
      <img
        v-if="!highResDataLoaded"
        id="sky"
        :src="cdn(thumbnail)"
        crossorigin="anonymous"
        @load="()=>$emit('progressData',50)"
      >
      <img
        v-else
        id="skyHigh"
        :src="highResDataLoaded"
        crossorigin="anonymous"
      >
    </a-assets>
    <a-sky
      v-if="!highResDataLoaded"
      :src="'#sky'"
    />
    <a-sky
      v-else
      :src="'#skyHigh'"
    />

    <!-- Hotspots for links -->
    <template v-for="(link, linkId) in links" :key="linkId">
      <a-entity
        :position="`${link.position.x} ${link.position.y} ${link.position.z}`"
        @click="handleHotspotClick(link)"
        cursor="rayOrigin: mouse"
        class="hotspot"
      >
        <!-- Hotspot visual indicator -->
        <a-sphere
          radius="0.1"
          color="#ff6b6b"
          opacity="0.8"
          animation="property: scale; to: 1.2 1.2 1.2; dir: alternate; dur: 1000; loop: true"
        />
        <!-- Hotspot text label -->
        <a-text
          :value="link.text"
          position="0 0.2 0"
          align="center"
          color="#ffffff"
          background-color="#000000"
          background-opacity="0.7"
          width="8"
          wrap-count="20"
        />
      </a-entity>
    </template>

    <a-entity
      camera
      look-controls="enabled: false"
      orbit-controls="minDistance: 2; maxDistance: 180; initialPosition: 0 5 90; rotateSpeed: 0.5"
      :rotation="rotation"
    />
  </a-scene>
</template>

<style scoped>
.hotspot {
  cursor: pointer;
}
</style>
