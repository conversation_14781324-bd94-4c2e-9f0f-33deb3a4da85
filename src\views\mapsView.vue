<script setup>
import { watch, ref } from 'vue';
import { useRoute } from 'vue-router';
import { mapStyles } from '../helpers/mapStyles';
import { creationToolStore } from '../store/index';
var google;
const route = useRoute();
const mode = ref('driving');
const Store = creationToolStore();
if (Object.keys(Store.projectCardData).length === 0){
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}
if (!Store.landmarkData) {
  Store.getListofLandmark(route.params.projectId, route.params.organizationId);
}
const loadMap = () => {
  if (Store.landmarkData && Store.projectCardData[route.params.projectId]){
    google = window.google;
    const map = new google.maps.Map(document.getElementById('map'), {
      center: { lat: Store.projectCardData[route.params.projectId].projectSettings.general.lat,
        lng: Store.projectCardData[route.params.projectId].projectSettings.general.long,
      },
      zoom: 17,
      styles: mapStyles,
    });
    const directionsRenderer = new google.maps.DirectionsRenderer({suppressMarkers: true});
    directionsRenderer.setMap(map);
    new google.maps.Marker({
      position: { lat: Store.projectCardData[route.params.projectId].projectSettings.general.lat,
        lng: Store.projectCardData[route.params.projectId].projectSettings.general.long,
      },
      map: map,
      title: Store.projectCardData[route.params.projectId].name,
    });
    if (Store.landmarkData){
      Object.values(Store.landmarkData).forEach((landmark) => {
        const marker = new google.maps.Marker({
          position: { lat: landmark.lat, lng: landmark.long },
          map: map,
          title: landmark.name,
          icon: {
            url: 'https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FTest%20Org1%2Fprojects%2FJumeirah%20Lakes%20Towers.png?alt=media',
            scaledSize: new google.maps.Size(50, 50),
          },
        });
        marker.addListener('click', async () => {
          try {
            const url = landmark[mode.value];
            const response = await fetch(url);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            const routeData = await response.json();
            directionsRenderer.setDirections(routeData);

          } catch (error) {
            console.error('Failed to fetch route:', error);
          }
        });
      });
    }
  }
};
const initMap = () => {
  const script = document.createElement('script');
  script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_MAPS_API_KEY}&callback=initMap`;
  script.defer = true;
  script.async = true;
  window.initMap = loadMap;
  document.head.appendChild(script);
};
watch(() => Store.landmarkData, () => {
  if (Store.landmarkData && Store.projectCardData[route.params.projectId]) {
    initMap();
  }
});
</script>
<template>
  <div id="floating-panel">
    <b>Mode of Travel: </b>
    <select v-model="mode">
      <option value="driving">
        Driving
      </option>
      <option value="walking">
        Walking
      </option>
      <option value="transit">
        Transit
      </option>
    </select>
  </div>
  <div
    id="map"
    class="map"
  />
</template>
<style scoped>
.map {
  height: 100%;
  width: 100%;
  z-index: 4;
}
#floating-panel {
  position: absolute;
  top: 10px;
  left: 25%;
  z-index: 5;
  background-color: #fff;
  padding: 5px;
  border: 1px solid #999;
  text-align: center;
  line-height: 30px;
  padding-left: 10px;
}
</style>
