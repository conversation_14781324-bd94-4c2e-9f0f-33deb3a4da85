<script setup>
import {defineProps} from 'vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
const props = defineProps({
  infoText: {type: String, default: ""},
  infoIcon: {type: String, default: ""},
  // TimeInterval: {type: Number, default: 0},
});
// Const emit = defineEmits(['unMount']);
// Const infoContainer = ref(null);
// Function startAnimation (){
//   InfoContainer.value.classList.add('fade-out');
// }
// Function checkAnimation (event){
//   If (event.target.classList.contains('fade-in')){
//     Event.target.classList.remove('fade-in');
//   } else if (event.target.classList.contains('fade-out')){
//     Emit('unMount');
//   }
// }

</script>
<template>
  <!-- <div
    ref="infoContainer"
    class="fade-in hidden absolute top-0 left-0 h-full w-full opacity-100 z-[8] bg-[#0000008c]"
    @click="startAnimation"
    @animationend="checkAnimation"
  > -->
  <div class="z-[9] pointer-events-none fixed top-0 left-0 w-full h-full flex items-center justify-center">
    <div class="bg-secondary rounded-md flex-col justify-center items-center inline-flex py-3 px-4">
      <div class="flex-col justify-start items-center gap-2 flex ">
        <div class="w-14 h-14 justify-center items-center inline-flex">
          <img
            :src="props.infoIcon"
          >
        </div>
        <div class="text-center text-secondaryText text-sm font-medium leading-relaxed pb-2 px-2">
          <TranslationComp
            :text="props.infoText"
          />
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>
<style scoped>
.fade-out {
  animation: fadeOut 0.5s ease-in-out;
}
.fade-in{
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
