<script setup>
import { defineProps, ref, watch, onMounted } from 'vue';
import { creationToolStore } from '../../../store/index';
import { useRoute } from 'vue-router';
import AmenityCatergories from '../../ALEComponents/AmenityCatergories/AmenityCatergories.vue';
import router from '../../../router';
import { onClickOutside } from '@vueuse/core';
import { cdn, addSVGDeepZoom, setActiveElem, Googleanalytics } from '../../../helpers/helper';
import OpenSeadragon from 'openseadragon';

const route = useRoute();
const isOpen=ref(false);
const amenityRef = ref();
const Store = creationToolStore();
const filteredData = ref({});
const  selectedCategory = ref(route.query.amenity_category?route.query.amenity_category:"0"), amenitCatSVGElem=ref({});

// If (Store.amenityCategories && !route.query.amenity_category){
//   Router.push({name: 'projectScene', query: {amenity_category: Store.amenityCategories[0].category}});
//   SelectedCategory.value=Store.amenityCategories[0].category;
// }

// Watch(() => Store.amenityCategories, () => {
//   If (Store.amenityCategories && !route.query.amenity_category){
//     Router.push({name: 'projectScene', query: {amenity_category: Store.amenityCategories[0].category}});
//     SelectedCategory.value=Store.amenityCategories[0].category;
//   }
// });

if (Store.amenityCardData) {
  for (const [key, value] of Object.entries(Store.amenityCardData)) {
    if (value.category === selectedCategory.value) {
      filteredData.value[key] = value;
    }
  }
}

watch(selectedCategory, (newCategory) => {
  filteredData.value = {};
  if (Store.amenityCardData) {
    for (const [key, value] of Object.entries(Store.amenityCardData)) {
      if (value.category === newCategory) {
        filteredData.value[key] = value;
      }
    }
  }
});

const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
}});

function handleImageClick (amenity){
  Googleanalytics("amenity_clicked", {
    amenity_name: Store.amenityCardData[amenity._id].name,
    amenity_id: amenity._id,
    amenity_category: Store.amenityCardData[amenity._id].category,
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  router.push({name: 'amenityview', params: {amenityId: amenity._id}, query: {...route.query}});
}

function placeModal (amenity_category){
  router.push({name: 'projectScene', query: {...route.query, amenity_category: amenity_category}});
  selectedCategory.value=amenity_category;
  isOpen.value=true;
}
if (route.query.amenity_category){
  placeModal(route.query.amenity_category);
}
if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        obj.svgElement.children[0].style.cursor= 'pointer';
        obj.svgElement.children[0].style.opacity= '0.8';
        amenitCatSVGElem.value[item.layer_data.layer_id] = {'g': obj.svgElement.children[0], 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel};
        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.children[0].classList.add('!visible');
            obj.svgElement.children[0].classList.remove('!hidden');
          } else {
            obj.svgElement.children[0].classList.remove('!visible');
            obj.svgElement.children[0].classList.add('!hidden');
          }
        }
        new OpenSeadragon.MouseTracker({
          element: obj.svgElement.children[0],
          clickHandler: function () {
            placeModal(item.layer_data.amenity_category);
          },

        });
        obj.svgElement.children[0].addEventListener("mouseenter", () => {
          obj.svgElement.children[0].style.opacity= '1';
        });
        obj.svgElement.children[0].addEventListener("mouseleave", () => {
          obj.svgElement.children[0].style.opacity= '0.8';
        });
      });
    }
  });
}
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(amenitCatSVGElem.value, Store.currentZoomlevel);
});

onClickOutside(amenityRef, () => isOpen.value = false);

</script>
<template>
  <rect
    v-if="show_modal"
    class="rect_overlay"
    width="100%"
    height="100%"
    fill="rgba(0, 0, 0, 0.5)"
  />

  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="layer in layerData"
    v-show="props.sceneType!=='deep_zoom'"
    :id="layer.layer_data.layer_id"
    :key="layer"
    :class="props.sceneType!=='deep_zoom'?[layer.layer.getAttribute('class') + ' ' + layer.layer_data.type + ' opacity-80 hover:opacity-100 cursor-pointer']:''"
    @click="(event) =>{placeModal(layer.layer_data.amenity_category)}"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->

  <portal to="amenitycategory">
    <div v-if="isOpen && filteredData">
      <AmenityCatergories
        ref="amenityRef"
        class="amenity-categories"
        :categories="Store.amenityCategories"
        :data="filteredData"
        :defaultValue="selectedCategory"
        @goto-tour="handleImageClick"
        @clear-category="()=>{selectedCategory=''}"
      />
    </div>
  </portal>
</template>

<style scoped>

.slide-animation
{
  animation:slide 0.3s 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@keyframes slide
{
  0%
  {
    bottom :-12em;
  }

  100%
  {
   @apply md:bottom-[0.4em];

  }
}

</style>
