/** @type {import('tailwindcss').Config} */
import flowbitePlugin from 'flowbite/plugin';
export default {
    content: [
      "./index.html",
      "./src/**/*.{js,ts,jsx,tsx,vue}",
      'node_modules/flowbite-vue/**/*.{js,jsx,ts,tsx,vue}',
    'node_modules/flowbite/**/*.{js,jsx,ts,tsx}'
    ],
    theme: {
      extend: {
        colors:{
          'primary':'var(--primary)',
          'primaryText':'var(--primaryText)',
          'secondaryText':'var(--secondaryText)',
          'secondary':'var(--secondary)',
          'tertiary':'var(--tertiary)',
          'colormix':'var(--colormix)',
          'tertiary50opacity': 'var(--tertiary50opacity)',
          'button-active':'#597BEE',
          'inactive':'#CFC7C7',
          'pill-fill':'#C6C6C6',
          "unitName":"#000000",
          "numberOfUnitsType":"#262626",
          "unitsCardExploreBtn":"#262626",
           "sliderOptionDarkColor":"#262626",
           "sliderOptionLightColor":"#FFFFFF",
          "tower-available":"#9CFE7A",
          "unit-b3.5-fill":"#31C48D",
          "unit-b3-fill":"#31C48D",
          "unit-b3-stroke":"#0D50F9",
          "unit-b2.5-fill":"#E3A008",
          "unit-b2-fill":"#E3A008",
          "unit-b1-fill":"#FF8A4C",
          "unit-s1-fill":"#1A56DB",
          "unit-b1-stroke":"#EB5CF6",
          "unit-b4-fill": "#AC94FA",
          "unit-b5-stoke":"#9B1C1C",
          "unit-b5-fill":"#9B1C1C",
          "unit-b6-fill":"#AC94FA",
          "unit-b7-fill":"#F17EB8",
          "floorPlanCardBorder":"#8E95A2",
          "floorPlanCardGrey":"#D8DBDF",
          "floorCardBorder":"#8E95A2",
          "landmarklabelText":"#FFFFFF",
          "projectCardCompWhite":"#FFFFFF",
          "projectCardCompActiveButton":"#E8E8E8",
          "projectCardCompButtonBackground":"#FFF",
          "projectCardCompButtonHover":"#D8DBDF",
          "projectCardCompBlack":"#0F0F0F",
          "projectCardWhite":"#FFF",
          "projectCardBorder":"#FFFFFF",
          "projectCardDisabledTextColor":"#B6BAC3",
          "projectCardButtonText":"#262626",
          "projectCardActiveButton":"#E8E8E8",
          "projectCardButtonBackground":"#FFF",
          "projectCardButtonHover":"#D8DBDF",
          "sidebarHeaderText":"#FFFFFF",
          "floorSwitchBorderColor":"#757575",
          "floorSwitchArrowsBorderColor":"#8E95A2",
          "favoriteViewBg":"#262626",
          "favoriteViewBorderColor":"#4A4E5A",
          "favoriteViewWhite":"#FFFFFF",
          "favoriteViewGray":"#8E95A2",
          "statusPillAvailableBg": "#0E9F6E80 ",
          "statusPillAvailableBorderColor": "#14D56F" ,
          "statusPillSoldBg": "#e9b65238",
          "statusPillSoldBorderColor": "#E9B652" ,
          "statusPilReservedBg": "#ff4d0040",
          "statusPillReservedBorderColor": "#FF4D00",
          "comingSoonBgcolor":"#262626",
          "comingSoonTextColor":"white",
          "damacHeaderBg":"#0F0F0F",
          "damacHeaderDropDownBg":"#262626",
          "damacHeaderDropDownWhite":"#FFFFFF",
          "InventoryFloorPlateCardBg":"rgba(38, 38, 38, 0.40)",
          "InventoryFloorPlateCardBorder":"var(--Colors-Grey-Grey-400, #8E95A2)",
          "InventoryFloorPlateCardIconBg":"#808080",
          "SearchComponentBg": " rgba(0, 0, 0, 0.40)",
          "SearchComponentOverlay":"rgba(18, 18, 18, 0.85)",
          "ProjectLogoBg":"rgba(0, 0, 0, 0.50)",
        },
        fontSize:{
          'h1':'min(max(26px, calc(1.625rem + ((1vw - 3px) * 0.177))), 30px)',
          'h2':'min(max(22px, calc(1.375rem + ((1vw - 3px) * 0.1327))), 25px)',
          'h3':'min(max(18px, calc(1.125rem + ((1vw - 3px) * 0.1327))), 21px)',
          'h4':'min(max(14px, calc(0.875rem + ((1vw - 3px) * 0.1327))), 17px)',
          'h5':'min(max(8px, calc(0.5rem + ((1vw - 3px) * 0.2212))), 13px)',
        },
        backgroundImage:{
          'app-gradient': "var(--theme-gradient);"
        }
      },
    },
    plugins: [
      flowbitePlugin
    ],
};
