export const LandmarkCategories = {
  category: {
    All: {
      icon: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1.16675 3.99996C1.16675 2.90001 1.16675 2.35004 1.50846 2.00834C1.85016 1.66663 2.40014 1.66663 3.50008 1.66663C4.60003 1.66663 5.15 1.66663 5.4917 2.00834C5.83342 2.35004 5.83342 2.90001 5.83342 3.99996V5.16663C5.83342 6.26657 5.83342 6.81653 5.4917 7.15824C5.15 7.49996 4.60003 7.49996 3.50008 7.49996C2.40014 7.49996 1.85016 7.49996 1.50846 7.15824C1.16675 6.81653 1.16675 6.26657 1.16675 5.16663V3.99996Z"/>
              <path d="M1.16675 11.5834C1.16675 11.0398 1.16675 10.768 1.25555 10.5536C1.37397 10.2677 1.60109 10.0406 1.88695 9.92216C2.10135 9.83337 2.37315 9.83337 2.91675 9.83337H4.08341C4.62701 9.83337 4.89881 9.83337 5.11321 9.92216C5.39908 10.0406 5.6262 10.2677 5.74461 10.5536C5.83342 10.768 5.83342 11.0398 5.83342 11.5834C5.83342 12.127 5.83342 12.3988 5.74461 12.6132C5.6262 12.899 5.39908 13.1262 5.11321 13.2446C4.89881 13.3334 4.62701 13.3334 4.08341 13.3334H2.91675C2.37315 13.3334 2.10135 13.3334 1.88695 13.2446C1.60109 13.1262 1.37397 12.899 1.25555 12.6132C1.16675 12.3988 1.16675 12.127 1.16675 11.5834Z"/>
              <path d="M8.16675 9.83333C8.16675 8.7334 8.16675 8.18343 8.50846 7.84172C8.85018 7.5 9.40015 7.5 10.5001 7.5C11.6 7.5 12.15 7.5 12.4917 7.84172C12.8334 8.18343 12.8334 8.7334 12.8334 9.83333V11C12.8334 12.0999 12.8334 12.6499 12.4917 12.9916C12.15 13.3333 11.6 13.3333 10.5001 13.3333C9.40015 13.3333 8.85018 13.3333 8.50846 12.9916C8.16675 12.6499 8.16675 12.0999 8.16675 11V9.83333Z"/>
              <path d="M8.16675 3.41663C8.16675 2.87303 8.16675 2.60123 8.25553 2.38683C8.37395 2.10096 8.6011 1.87384 8.88693 1.75543C9.10136 1.66663 9.37314 1.66663 9.91675 1.66663H11.0834C11.627 1.66663 11.8988 1.66663 12.1132 1.75543C12.3991 1.87384 12.6262 2.10096 12.7446 2.38683C12.8334 2.60123 12.8334 2.87303 12.8334 3.41663C12.8334 3.96022 12.8334 4.23202 12.7446 4.44642C12.6262 4.73229 12.3991 4.95941 12.1132 5.07782C11.8988 5.16663 11.627 5.16663 11.0834 5.16663H9.91675C9.37314 5.16663 9.10136 5.16663 8.88693 5.07782C8.6011 4.95941 8.37395 4.73229 8.25553 4.44642C8.16675 4.23202 8.16675 3.96022 8.16675 3.41663Z"/>
            </svg>
            `,
      value: "Show All",
    },
    landmark: {
      icon: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8.45841 5.74996C8.45841 6.55537 7.80549 7.20829 7.00008 7.20829C6.19467 7.20829 5.54175 6.55537 5.54175 5.74996C5.54175 4.94455 6.19467 4.29163 7.00008 4.29163C7.80549 4.29163 8.45841 4.94455 8.45841 5.74996Z"/>
              <path d="M10.6295 10.4166C11.443 11.5766 11.8321 12.1943 11.6004 12.6916C11.5771 12.7414 11.5499 12.7896 11.5189 12.8355C11.1838 13.3333 10.3176 13.3333 8.5853 13.3333H5.41455C3.68222 13.3333 2.81605 13.3333 2.4809 12.8355C2.44995 12.7896 2.42273 12.7414 2.39949 12.6916C2.16769 12.1943 2.55682 11.5766 3.37029 10.4166" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.73356 10.7046C7.53681 10.894 7.27384 11 7.0002 11C6.7265 11 6.46353 10.894 6.26677 10.7046C4.46509 8.95876 2.05061 7.0085 3.22808 4.17714C3.86472 2.64623 5.39296 1.66663 7.0002 1.66663C8.6074 1.66663 10.1356 2.64624 10.7723 4.17714C11.9483 7.00494 9.53968 8.96477 7.73356 10.7046Z"/>
            </svg>
            `,
      value: "landmark",
    },
    projects: {
      icon: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8.45841 5.74996C8.45841 6.55537 7.80549 7.20829 7.00008 7.20829C6.19467 7.20829 5.54175 6.55537 5.54175 5.74996C5.54175 4.94455 6.19467 4.29163 7.00008 4.29163C7.80549 4.29163 8.45841 4.94455 8.45841 5.74996Z"/>
              <path d="M10.6295 10.4166C11.443 11.5766 11.8321 12.1943 11.6004 12.6916C11.5771 12.7414 11.5499 12.7896 11.5189 12.8355C11.1838 13.3333 10.3176 13.3333 8.5853 13.3333H5.41455C3.68222 13.3333 2.81605 13.3333 2.4809 12.8355C2.44995 12.7896 2.42273 12.7414 2.39949 12.6916C2.16769 12.1943 2.55682 11.5766 3.37029 10.4166" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.73356 10.7046C7.53681 10.894 7.27384 11 7.0002 11C6.7265 11 6.46353 10.894 6.26677 10.7046C4.46509 8.95876 2.05061 7.0085 3.22808 4.17714C3.86472 2.64623 5.39296 1.66663 7.0002 1.66663C8.6074 1.66663 10.1356 2.64624 10.7723 4.17714C11.9483 7.00494 9.53968 8.96477 7.73356 10.7046Z"/>
            </svg>
            `,
      value: "projects",
    },
    recreational: {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25">
            <path d="M17.0071 7.08681C16.5938 6.09985 15.8979 5.25706 15.0071 4.66433C14.1162 4.0716 13.07 3.75537 12 3.75537C10.93 3.75537 9.8838 4.0716 8.99293 4.66433C8.10206 5.25706 7.40624 6.09985 6.99287 7.08681C6.10338 7.49892 5.34958 8.15571 4.8196 8.98041C4.28961 9.80511 4.00534 10.7636 4.00002 11.7439C3.99216 14.5168 6.24002 16.8296 9.01001 16.9011C9.84795 16.9229 10.6785 16.7387 11.4286 16.3646V13.8254L8.31572 12.2697C8.18008 12.2018 8.07694 12.0829 8.02899 11.939C7.98103 11.7952 7.99219 11.6382 8.06001 11.5025C8.12783 11.3669 8.24675 11.2637 8.39062 11.2158C8.53449 11.1678 8.69151 11.179 8.82715 11.2468L11.4286 12.5482V8.9011C11.4286 8.74954 11.4888 8.6042 11.5959 8.49704C11.7031 8.38987 11.8484 8.32967 12 8.32967C12.1516 8.32967 12.2969 8.38987 12.4041 8.49704C12.5112 8.6042 12.5714 8.74954 12.5714 8.9011V10.8339L15.1728 9.53252C15.24 9.49894 15.3131 9.47892 15.388 9.4736C15.4629 9.46827 15.5381 9.47775 15.6094 9.5015C15.6806 9.52524 15.7465 9.56279 15.8032 9.61199C15.8599 9.66119 15.9064 9.72108 15.94 9.78824C15.9736 9.8554 15.9936 9.92852 15.9989 10.0034C16.0042 10.0783 15.9948 10.1535 15.971 10.2248C15.9473 10.296 15.9097 10.3619 15.8605 10.4186C15.8113 10.4753 15.7514 10.5218 15.6843 10.5554L12.5714 12.1111V16.3646C13.2813 16.7187 14.0639 16.9023 14.8571 16.9011H14.99C17.76 16.8296 20.0078 14.5182 20 11.7454C19.9952 10.7648 19.7112 9.80583 19.1811 8.9808C18.6511 8.15577 17.897 7.49881 17.0071 7.08681Z"/>
            <path d="M12.0049 16.9028C12.0049 16.9028 12.0049 16.9028 12.0006 16.9028C11.8175 17.0248 11.6266 17.1348 11.4292 17.2321V20.0543C11.4292 20.2058 11.4894 20.3511 11.5966 20.4583C11.7037 20.5655 11.8491 20.6257 12.0006 20.6257C12.1522 20.6257 12.2975 20.5655 12.4047 20.4583C12.5118 20.3511 12.5721 20.2058 12.5721 20.0543V17.2321C12.3747 17.1348 12.1838 17.0248 12.0006 16.9028H12.0049Z"/>
            </svg>`,
      value: "recreational",
    },
    health: {
      icon: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.13636 4.56489H7.63636H11.4544H11.4545C11.9393 4.56489 12.366 4.7448 12.708 5.08908L7.13636 4.56489ZM7.13636 4.56489V5.06489M7.13636 4.56489V5.06489M3.81772 6.48855C3.81787 6.48855 3.81803 6.48855 3.81818 6.48855C3.8709 6.48855 3.89261 6.47619 3.91675 6.45194C3.94089 6.42751 3.95447 6.40386 3.95455 6.34779C3.95455 6.34764 3.95454 6.34748 3.95454 6.34733C3.95455 6.29064 3.94082 6.26697 3.91638 6.24234C3.89219 6.21798 3.87111 6.20598 3.81937 6.20611L3.81864 6.20611L3.81772 6.48855ZM3.81772 6.48855C3.76514 6.48848 3.74352 6.47603 3.71935 6.45167C3.69497 6.4271 3.68169 6.40402 3.68182 6.3485L3.68182 6.34733C3.68182 6.40402 3.69554 6.42768 3.71999 6.45231C3.74417 6.47668 3.76525 6.48867 3.81699 6.48855L3.81772 6.48855ZM7.13636 5.06489V8.91221V9.41221H7.63636H12.7273H13.2273M7.13636 5.06489L13.2273 9.41221M13.2273 9.41221V8.91221M13.2273 9.41221V8.91221M13.2273 8.91221V6.34793M13.2273 8.91221V6.34733V6.34793M13.2273 6.34793C13.2279 5.86353 13.0512 5.43572 12.7103 5.09142L12.7273 6.34733L13.2273 6.34793ZM4.95454 6.3485C4.95471 6.65456 4.84623 6.93499 4.6268 7.15609L4.95454 6.3485ZM4.95454 6.3485C4.95526 6.04257 4.84753 5.76216 4.62906 5.54085M4.95454 6.3485L4.45455 6.34733L4.62906 5.54085M4.62906 5.54085C4.62831 5.54009 4.62756 5.53933 4.6268 5.53857L4.62906 5.54085ZM3.81818 5.20611C4.12573 5.20566 4.40627 5.31635 4.62617 5.53793L3.81818 5.20611ZM3.81818 5.20611C3.51063 5.20566 3.23009 5.31635 3.0102 5.53793L3.81759 5.20611C3.81779 5.20611 3.81798 5.20611 3.81818 5.20611ZM3.81818 5.20611C3.81858 5.20611 3.81897 5.20611 3.81937 5.20611L3.81818 5.70611L3.81818 5.20611ZM0.772727 10.1947V11.6183H0.5V3H0.772727V8.91221V9.41221H1.27273H6.36364H6.86364V8.91221V4.28244H11.4545C12.0196 4.28244 12.4916 4.47922 12.8974 4.88808C13.3033 5.29706 13.5 5.77442 13.5 6.34733V11.6183H13.2273V10.1947V9.69466H12.7273H1.27273H0.772727V10.1947ZM11.4545 5.56489C11.4546 5.56489 11.4547 5.56489 11.4548 5.56489L11.4552 5.56488C11.6693 5.56462 11.8414 5.63548 11.9982 5.79349C12.1551 5.95163 12.2273 6.12722 12.2273 6.34733V8.41221H8.13636V5.56489L11.4545 5.56489ZM3.81818 7.77099C3.4202 7.77099 3.09748 7.6365 2.8208 7.35772C2.54399 7.07879 2.40909 6.7518 2.40909 6.34733C2.40909 5.94286 2.54399 5.61587 2.8208 5.33694C3.09748 5.05816 3.4202 4.92366 3.81818 4.92366C4.21616 4.92366 4.53889 5.05816 4.81556 5.33694C5.09238 5.61587 5.22727 5.94286 5.22727 6.34733C5.22727 6.7518 5.09238 7.07879 4.81556 7.35772C4.53889 7.6365 4.21616 7.77099 3.81818 7.77099ZM3.0102 7.15673C3.22981 7.37802 3.50991 7.48871 3.81699 7.48855C3.81759 7.48855 3.81818 7.48855 3.81878 7.48855C3.81897 7.48855 3.81917 7.48855 3.81937 7.48855L3.81818 6.98855L3.0102 7.15673ZM3.00956 5.53857C2.78972 5.76009 2.68124 6.04116 2.68182 6.34791C2.68182 6.34772 2.68182 6.34752 2.68182 6.34733C2.68182 6.34694 2.68182 6.34655 2.68182 6.34616C2.6811 6.65314 2.78958 6.93442 3.00956 7.15608L3.00956 5.53857Z"/>
             </svg>
            `,
      value: "health",
    },
    education: {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25">
            <path d="M15.0001 17.1537C15.345 17.0094 15.6792 16.8381 16.0001 16.641V19.3265C16.0001 19.4673 15.9474 19.6022 15.8536 19.7017C15.7599 19.8012 15.6327 19.8571 15.5001 19.8571C15.3675 19.8571 15.2403 19.8012 15.1465 19.7017C15.0527 19.6022 15.0001 19.4673 15.0001 19.3265V17.1537ZM15.7351 11.2884L12.235 9.30718C12.1182 9.24389 11.9827 9.2318 11.8576 9.27354C11.7325 9.31527 11.6279 9.40748 11.5662 9.5303C11.5046 9.65312 11.4909 9.79672 11.5281 9.93016C11.5652 10.0636 11.6503 10.1762 11.765 10.2437L14.6876 11.8979L15.7501 11.297L15.7351 11.2884ZM19.7352 9.30718L12.235 5.06226C12.1627 5.02138 12.082 5 12 5C11.918 5 11.8373 5.02138 11.765 5.06226L4.26485 9.30718C4.18485 9.35243 4.11795 9.4199 4.0713 9.50238C4.02464 9.58486 4 9.67925 4 9.77545C4 9.87165 4.02464 9.96604 4.0713 10.0485C4.11795 10.131 4.18485 10.1985 4.26485 10.2437L5.99988 11.226V14.4376C5.99936 14.6982 6.08975 14.9498 6.25364 15.144C7.0724 16.1117 8.90681 17.7347 12 17.7347C13.0256 17.7437 14.0436 17.5465 15.0001 17.1537V12.075L14.6876 11.8979L12 13.4188L6.73927 10.4387L5.56237 9.77545L12 6.13211L18.4376 9.77545L17.2639 10.4387H17.2601L15.7501 11.297C15.8261 11.3436 15.8892 11.4106 15.9331 11.4913C15.977 11.5719 16.0001 11.6635 16.0001 11.7566V16.641C16.6521 16.2415 17.2413 15.7364 17.7464 15.144C17.9102 14.9498 18.0006 14.6982 18.0001 14.4376V11.226L19.7352 10.2437C19.8151 10.1985 19.8821 10.131 19.9287 10.0485C19.9754 9.96604 20 9.87165 20 9.77545C20 9.67925 19.9754 9.58486 19.9287 9.50238C19.8821 9.4199 19.8151 9.35243 19.7352 9.30718Z"/>
            </svg>`,
      value: "education",
    },
    religious: {
      icon: `<svg width="24" height="25" viewBox="0 0 24 25" xmlns="http://www.w3.org/2000/svg">
            <path d="M19.12 14.6585L16.4444 13.0533V11.6111C16.4454 11.5062 16.4184 11.4029 16.3664 11.3118C16.3143 11.2207 16.239 11.1451 16.1481 11.0926L12.2963 8.89704V7.46296H13.4815C13.6386 7.46296 13.7894 7.40053 13.9005 7.2894C14.0116 7.17826 14.0741 7.02754 14.0741 6.87037C14.0741 6.71321 14.0116 6.56248 13.9005 6.45134C13.7894 6.34021 13.6386 6.27778 13.4815 6.27778H12.2963V5.09259C12.2963 4.93543 12.2339 4.7847 12.1227 4.67357C12.0116 4.56243 11.8609 4.5 11.7037 4.5C11.5465 4.5 11.3958 4.56243 11.2847 4.67357C11.1735 4.7847 11.1111 4.93543 11.1111 5.09259V6.27778H9.92593C9.76876 6.27778 9.61803 6.34021 9.5069 6.45134C9.39577 6.56248 9.33333 6.71321 9.33333 6.87037C9.33333 7.02754 9.39577 7.17826 9.5069 7.2894C9.61803 7.40053 9.76876 7.46296 9.92593 7.46296H11.1111V8.89704L7.25926 11.0963C7.16839 11.1488 7.09308 11.2244 7.04102 11.3155C6.98896 11.4066 6.96202 11.5099 6.96296 11.6148V13.057L4.28741 14.6585C4.19971 14.7112 4.12715 14.7857 4.07679 14.8747C4.02642 14.9638 3.99997 15.0644 4 15.1667V19.9074C4 20.0646 4.06243 20.2153 4.17357 20.3264C4.2847 20.4376 4.43543 20.5 4.59259 20.5H9.92593C10.0831 20.5 10.2338 20.4376 10.345 20.3264C10.4561 20.2153 10.5185 20.0646 10.5185 19.9074V16.3519C10.5185 16.0375 10.6434 15.7361 10.8657 15.5138C11.0879 15.2915 11.3894 15.1667 11.7037 15.1667C12.018 15.1667 12.3195 15.2915 12.5418 15.5138C12.764 15.7361 12.8889 16.0375 12.8889 16.3519V19.9074C12.8889 20.0646 12.9513 20.2153 13.0625 20.3264C13.1736 20.4376 13.3243 20.5 13.4815 20.5H18.8148C18.972 20.5 19.1227 20.4376 19.2338 20.3264C19.345 20.2153 19.4074 20.0646 19.4074 19.9074V15.1667C19.4074 15.0644 19.381 14.9638 19.3306 14.8747C19.2803 14.7857 19.2077 14.7112 19.12 14.6585ZM6.96296 19.3148H7.68875L7.5 14.4356H6.96296V19.3148ZM16 19.5L16.4444 19.3148V14.4356L15.7285 16.3519L16 19.5Z"/>
            </svg>
            `,
      value: "religious",
    },
    shopping: {
      icon: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g>
                <path id="Vector" d="M0 1.5C0.944167 1.5 1.88833 1.5 2.86111 1.5C3.02971 2.09307 3.19832 2.68613 3.37203 3.29717C8.17716 3.34399 8.17716 3.34399 13.0794 3.39175C12.9644 4.34955 12.9644 4.34955 12.8546 4.75346C12.8304 4.84369 12.8062 4.93391 12.7813 5.02687C12.7553 5.12119 12.7293 5.21552 12.7026 5.3127C12.6757 5.41182 12.6489 5.51094 12.6213 5.61307C12.5649 5.821 12.5082 6.02886 12.4512 6.23666C12.3638 6.55572 12.2772 6.87499 12.1909 7.19429C12.1358 7.39672 12.0808 7.59914 12.0256 7.80154C11.9996 7.8972 11.9737 7.99287 11.9469 8.09143C11.9227 8.17957 11.8985 8.26771 11.8735 8.35851C11.8523 8.43618 11.831 8.51385 11.8091 8.59387C11.751 8.78326 11.751 8.78326 11.6488 8.97243C9.42328 8.97243 7.19774 8.97243 4.90476 8.97243C4.9722 9.31579 5.03965 9.65914 5.10913 10.0129C5.2767 10.0114 5.44427 10.0099 5.61692 10.0084C6.23868 10.0033 6.86043 10 7.48221 9.99731C7.7513 9.99588 8.0204 9.99395 8.28949 9.99149C8.6763 9.98805 9.06307 9.98644 9.44989 9.98519C9.57019 9.98372 9.69048 9.98226 9.81442 9.98075C9.98314 9.98072 9.98314 9.98072 10.1553 9.98068C10.3033 9.97974 10.3033 9.97974 10.4544 9.97877C10.9074 10.035 11.1693 10.2238 11.47 10.5331C11.7679 10.9271 11.7936 11.2425 11.751 11.7155C11.6129 12.0545 11.416 12.3154 11.1251 12.5609C10.5923 12.7431 10.0422 12.7503 9.51575 12.549C9.12246 12.2385 8.8821 11.995 8.80686 11.5145C8.7877 11.148 8.7877 11.148 8.88988 10.8642C7.912 10.8642 6.93411 10.8642 5.92659 10.8642C5.96031 10.9266 5.99403 10.989 6.02877 11.0534C6.08989 11.6304 5.97346 11.9178 5.62004 12.3776C5.22847 12.7181 4.77988 12.7201 4.26413 12.7072C3.80909 12.6324 3.55336 12.4213 3.26984 12.0938C3.01751 11.7337 3.04283 11.3928 3.10539 10.9798C3.21047 10.625 3.38814 10.4522 3.67857 10.2021C3.9001 10.14 3.9001 10.14 4.0873 10.1075C3.85104 9.22026 3.61475 8.33304 3.37845 7.44583C3.26872 7.03381 3.15898 6.62178 3.04926 6.20976C2.94327 5.81173 2.83726 5.41369 2.73124 5.01567C2.69091 4.86423 2.65058 4.71279 2.61025 4.56135C2.55353 4.34834 2.4968 4.13534 2.44006 3.92233C2.40784 3.80135 2.37562 3.68036 2.34242 3.55571C2.24348 3.18562 2.14355 2.81575 2.04365 2.44588C1.36925 2.41466 0.694842 2.38345 0 2.35129C0 2.07036 0 1.78944 0 1.5ZM3.57639 4.24304C3.71127 4.74247 3.84615 5.24189 3.98512 5.75645C4.35604 5.75645 4.72697 5.75645 5.10913 5.75645C5.0948 5.52191 5.07981 5.28743 5.06442 5.05295C5.06041 4.98607 5.05639 4.9192 5.05225 4.85029C5.06047 4.51859 5.06047 4.51859 4.90476 4.24304C4.68135 4.23618 4.45772 4.23512 4.23419 4.23713C3.90858 4.24006 3.90858 4.24006 3.57639 4.24304ZM5.92659 4.24304C5.94134 4.44008 5.9561 4.63712 5.97129 4.84013C5.97959 4.95096 5.98789 5.0618 5.99644 5.17599C6.0172 5.47514 6.0172 5.47514 6.13096 5.75645C6.50188 5.75645 6.8728 5.75645 7.25496 5.75645C7.25496 5.25703 7.25496 4.7576 7.25496 4.24304C6.8166 4.24304 6.37824 4.24304 5.92659 4.24304ZM8.27679 4.24304C8.27679 4.74247 8.27679 5.24189 8.27679 5.75645C8.64771 5.75645 9.01864 5.75645 9.4008 5.75645C9.46824 5.25703 9.53568 4.7576 9.60516 4.24304C9.1668 4.24304 8.72844 4.24304 8.27679 4.24304ZM10.5248 4.24304C10.4911 4.74247 10.4574 5.24189 10.4226 5.75645C10.7935 5.75645 11.1645 5.75645 11.5466 5.75645C11.5983 5.5753 11.6493 5.39397 11.6999 5.21257C11.7284 5.11161 11.7568 5.01065 11.7861 4.90664C11.8677 4.60843 11.8677 4.60843 11.8532 4.24304C11.4148 4.24304 10.9765 4.24304 10.5248 4.24304ZM4.18949 6.60774C4.32437 7.10716 4.45925 7.60659 4.59822 8.12114C4.86798 8.12114 5.13774 8.12114 5.41568 8.12114C5.38196 7.62172 5.34824 7.1223 5.31349 6.60774C4.94257 6.60774 4.57165 6.60774 4.18949 6.60774ZM6.23314 6.60774C6.30058 7.10716 6.36802 7.60659 6.4375 8.12114C6.70727 8.12114 6.97703 8.12114 7.25496 8.12114C7.25496 7.62172 7.25496 7.1223 7.25496 6.60774C6.91776 6.60774 6.58056 6.60774 6.23314 6.60774ZM8.27679 6.60774C8.27679 7.10716 8.27679 7.60659 8.27679 8.12114C8.54655 8.12114 8.81631 8.12114 9.09425 8.12114C9.16169 7.62172 9.22913 7.1223 9.29862 6.60774C8.96141 6.60774 8.62421 6.60774 8.27679 6.60774ZM10.2183 6.60774C10.1508 7.10716 10.0834 7.60659 10.0139 8.12114C10.2837 8.12114 10.5534 8.12114 10.8314 8.12114C11.028 7.6162 11.1787 7.14218 11.2401 6.60774C10.9029 6.60774 10.5657 6.60774 10.2183 6.60774ZM4.22142 10.9765C4.04141 11.2066 4.04112 11.343 4.0873 11.6209C4.37034 11.8399 4.37034 11.8399 4.66208 11.7805C4.89812 11.7368 4.89812 11.7368 5.00695 11.6209C5.04102 11.3411 5.04102 11.3411 5.00695 11.0534C4.61959 10.8134 4.61959 10.8134 4.22142 10.9765ZM9.94364 10.9765C9.76363 11.2066 9.76335 11.343 9.80953 11.6209C10.0926 11.8399 10.0926 11.8399 10.3843 11.7805C10.6203 11.7368 10.6203 11.7368 10.7292 11.6209C10.7632 11.3411 10.7632 11.3411 10.7292 11.0534C10.3418 10.8134 10.3418 10.8134 9.94364 10.9765Z"/>
              </g>
            </svg>
           `,
      value: "shopping",
    },
    gym: {
      icon: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.375 4.04932C4.78257 4.20826 4.78257 4.20826 4.92188 4.48696C4.93106 4.66141 4.93361 4.83624 4.93256 5.01093C4.93228 5.10368 4.93201 5.19642 4.93173 5.29198C4.93073 5.40994 4.92974 5.5279 4.92871 5.64943C4.92646 6.02402 4.9242 6.39861 4.92188 6.78455C6.22125 6.78455 7.52063 6.78455 8.85938 6.78455C8.85073 6.04318 8.85073 6.04318 8.83967 5.30184C8.83912 5.20936 8.83858 5.11689 8.83801 5.02161C8.83687 4.92707 8.83572 4.83254 8.83454 4.73513C8.85938 4.48696 8.85938 4.48696 9.07812 4.15873C9.61392 3.98008 10.1596 3.99257 10.7188 4.04932C10.9422 4.1879 11.1614 4.33366 11.375 4.48696C11.6592 4.52289 11.9342 4.53484 12.2205 4.5344C12.4688 4.59637 12.4688 4.59637 12.6644 4.82032C12.8284 5.22039 12.8332 5.57393 12.8174 5.99817C12.8152 6.11188 12.8152 6.11188 12.8129 6.22789C12.8091 6.41348 12.8032 6.59903 12.7969 6.78455C13.1939 6.78455 13.5909 6.78455 14 6.78455C14 7.43444 14 8.08433 14 8.75391C13.603 8.75391 13.2059 8.75391 12.7969 8.75391C12.8026 8.9221 12.8026 8.9221 12.8084 9.09368C12.8115 9.24255 12.8145 9.39142 12.8174 9.54029C12.8201 9.61398 12.8229 9.68767 12.8257 9.76359C12.8328 10.2479 12.7921 10.5721 12.4688 10.9421C12.2205 11.0041 12.2205 11.0041 11.9424 11.0036C11.5307 11.0226 11.4066 11.0288 11.0605 11.2772C10.541 11.5993 10.0034 11.5607 9.40625 11.4891C8.99868 11.3302 8.99868 11.3302 8.85938 11.0515C8.85019 10.8771 8.84764 10.7022 8.84869 10.5275C8.84897 10.4348 8.84924 10.342 8.84952 10.2465C8.85052 10.1285 8.85151 10.0106 8.85254 9.88903C8.85479 9.51444 8.85705 9.13985 8.85938 8.75391C7.56 8.75391 6.26062 8.75391 4.92188 8.75391C4.94346 9.49045 4.94346 9.49045 4.97114 10.2268C4.98225 10.9768 4.98225 10.9768 4.68774 11.3413C4.33321 11.5089 4.10261 11.541 3.71191 11.5302C3.60011 11.5288 3.4883 11.5273 3.37311 11.5259C2.94005 11.4746 2.71261 11.358 2.40625 11.0515C2.12906 11.0182 1.85508 11.0101 1.57611 11.0011C1.3125 10.9421 1.3125 10.9421 1.11511 10.7754C0.928468 10.3886 0.949425 10.0212 0.963867 9.60183C0.965348 9.52031 0.966828 9.43879 0.968353 9.35481C0.972205 9.15447 0.978089 8.95418 0.984375 8.75391C0.659531 8.75391 0.334688 8.75391 0 8.75391C0 8.10402 0 7.45413 0 6.78455C0.324844 6.78455 0.649688 6.78455 0.984375 6.78455C0.980568 6.66326 0.976761 6.54197 0.972839 6.417C0.969761 6.25688 0.966775 6.09676 0.963867 5.93663C0.961118 5.8568 0.958369 5.77698 0.955536 5.69473C0.95077 5.34511 0.962442 5.07902 1.11682 4.76219C1.36834 4.54905 1.51224 4.54568 1.83887 4.54167C2.24842 4.51868 2.37672 4.50815 2.7207 4.2613C3.2402 3.93912 3.77785 3.97778 4.375 4.04932ZM3.17188 4.70578C3.17188 6.72766 3.17188 8.74953 3.17188 10.8327C3.49672 10.8327 3.82156 10.8327 4.15625 10.8327C4.29273 10.7205 4.29273 10.7205 4.27878 10.4817C4.27853 10.375 4.27829 10.2684 4.27804 10.1585C4.27803 10.038 4.27803 9.91753 4.27802 9.79342C4.27745 9.66235 4.27689 9.53129 4.27631 9.39626C4.27615 9.26285 4.276 9.12944 4.27584 8.99199C4.27522 8.56389 4.27385 8.13579 4.27246 7.70769C4.27191 7.41828 4.27141 7.12887 4.27097 6.83947C4.26976 6.12824 4.26792 5.41701 4.26562 4.70578C3.90469 4.70578 3.54375 4.70578 3.17188 4.70578ZM9.51562 4.70578C9.51562 6.72766 9.51562 8.74953 9.51562 10.8327C9.84047 10.8327 10.1653 10.8327 10.5 10.8327C10.6838 10.6489 10.6233 10.4223 10.6235 10.1702C10.6239 10.0523 10.6242 9.9344 10.6246 9.8129C10.6244 9.68458 10.6241 9.55625 10.6239 9.42404C10.624 9.29332 10.6241 9.1626 10.6242 9.02791C10.6243 8.75088 10.6241 8.47385 10.6236 8.19682C10.623 7.77108 10.6236 7.34535 10.6243 6.9196C10.6243 6.65121 10.6241 6.38281 10.6239 6.11442C10.6242 5.92193 10.6242 5.92193 10.6246 5.72556C10.6242 5.60764 10.6239 5.48971 10.6235 5.36822C10.6234 5.26383 10.6234 5.15945 10.6233 5.0519C10.6358 4.81739 10.6358 4.81739 10.5 4.70578C10.1752 4.70578 9.85031 4.70578 9.51562 4.70578ZM1.64062 5.25282C1.62989 5.41009 1.62663 5.56787 1.62647 5.7255C1.62613 5.8259 1.62578 5.9263 1.62543 6.02974C1.62565 6.13892 1.62587 6.2481 1.6261 6.36059C1.62599 6.47188 1.62589 6.58318 1.62578 6.69784C1.62571 6.93367 1.62591 7.16949 1.62637 7.40532C1.62695 7.76767 1.62637 8.13 1.62567 8.49236C1.62574 8.72086 1.62588 8.94937 1.6261 9.17787C1.62588 9.28705 1.62566 9.39623 1.62543 9.50872C1.62577 9.60912 1.62612 9.70951 1.62647 9.81296C1.62656 9.90179 1.62665 9.99062 1.62674 10.0822C1.61835 10.2873 1.61835 10.2873 1.75 10.395C2.00588 10.3636 2.26121 10.3273 2.51562 10.2856C2.51839 9.57089 2.52031 8.85615 2.52163 8.1414C2.52218 7.89804 2.52293 7.65468 2.52388 7.41133C2.5252 7.06236 2.52582 6.7134 2.52631 6.36444C2.52687 6.25491 2.52744 6.14539 2.52802 6.03255C2.52803 5.9318 2.52803 5.83105 2.52804 5.72724C2.52841 5.59349 2.52841 5.59349 2.52878 5.45705C2.53808 5.25121 2.53808 5.25121 2.40625 5.14341C1.99036 5.09119 1.99036 5.09119 1.64062 5.25282ZM11.2656 5.25282C11.2549 5.41009 11.2516 5.56787 11.2515 5.7255C11.2511 5.8259 11.2508 5.9263 11.2504 6.02974C11.2506 6.13892 11.2509 6.2481 11.2511 6.36059C11.251 6.47188 11.2509 6.58318 11.2508 6.69784C11.2507 6.93367 11.2509 7.16949 11.2514 7.40532C11.252 7.76767 11.2514 8.13 11.2507 8.49236C11.2507 8.72086 11.2509 8.94937 11.2511 9.17787C11.2509 9.28705 11.2507 9.39623 11.2504 9.50872C11.2508 9.60912 11.2511 9.70951 11.2515 9.81296C11.2516 9.90179 11.2516 9.99062 11.2517 10.0822C11.2434 10.2873 11.2434 10.2873 11.375 10.395C11.7031 10.4133 11.7031 10.4133 12.0312 10.395C12.2033 10.2229 12.1545 10.0518 12.1548 9.81296C12.1551 9.71256 12.1555 9.61216 12.1558 9.50872C12.1556 9.39954 12.1554 9.29036 12.1552 9.17787C12.1553 9.06658 12.1554 8.95528 12.1555 8.84062C12.1555 8.60479 12.1553 8.36897 12.1549 8.13314C12.1543 7.77079 12.1549 7.40846 12.1556 7.04611C12.1555 6.8176 12.1554 6.58909 12.1552 6.36059C12.1554 6.25141 12.1556 6.14223 12.1558 6.02974C12.1553 5.87915 12.1553 5.87915 12.1548 5.7255C12.1547 5.63667 12.1546 5.54784 12.1545 5.45631C12.1629 5.25113 12.1629 5.25113 12.0312 5.14341C11.6154 5.09119 11.6154 5.09119 11.2656 5.25282ZM0.546875 7.441C0.546875 7.65763 0.546875 7.87426 0.546875 8.09746C0.69125 8.09746 0.835625 8.09746 0.984375 8.09746C0.984375 7.88083 0.984375 7.6642 0.984375 7.441C0.84 7.441 0.695625 7.441 0.546875 7.441ZM4.92188 7.441C4.92188 7.65763 4.92188 7.87426 4.92188 8.09746C6.22125 8.09746 7.52063 8.09746 8.85938 8.09746C8.85938 7.88083 8.85938 7.6642 8.85938 7.441C7.56 7.441 6.26062 7.441 4.92188 7.441ZM12.7969 7.441C12.7969 7.65763 12.7969 7.87426 12.7969 8.09746C13.0134 8.09746 13.23 8.09746 13.4531 8.09746C13.4531 7.88083 13.4531 7.6642 13.4531 7.441C13.2366 7.441 13.02 7.441 12.7969 7.441Z"/>
            </svg>
           `,
      value: "gym",
    },
    salon: {
      icon: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_94_8857)">
                <path d="M12.8131 0.507048C13.016 0.710436 13.016 0.710436 13.0794 1.01552C13.061 1.61033 12.5748 2.01663 12.2045 2.43923C12.0538 2.62302 11.9037 2.80735 11.7543 2.99219C11.6777 3.08579 11.6011 3.17939 11.5221 3.27582C11.3721 3.45924 11.2226 3.64301 11.0736 3.82719C10.741 4.23767 10.4064 4.64572 10.0611 5.04554C10.0066 5.10875 9.95204 5.17196 9.89584 5.23708C9.78906 5.36042 9.68194 5.48347 9.57443 5.60617C9.52517 5.66302 9.47592 5.71987 9.42517 5.77845C9.38157 5.82831 9.33798 5.87817 9.29307 5.92955C9.12696 6.14463 8.99311 6.3737 8.85695 6.60868C8.90374 6.65537 8.95054 6.70206 8.99875 6.75016C9.48445 7.23483 9.9698 7.71986 10.4549 8.2051C10.6362 8.3863 10.8176 8.56741 10.999 8.74842C11.2593 9.00818 11.5193 9.26826 11.7793 9.52841C11.8609 9.60966 11.9424 9.69092 12.0265 9.77463C12.1016 9.84995 12.1767 9.92527 12.2542 10.0029C12.3206 10.0693 12.3871 10.1356 12.4555 10.2041C12.7438 10.5158 12.7818 10.8112 12.7768 11.2258C12.6355 11.7987 12.1381 12.1732 11.7226 12.5641C11.6293 12.657 11.5359 12.7499 11.4397 12.8456C10.7413 13.5239 10.7413 13.5239 10.2771 13.5239C10.3194 13.0227 10.5358 12.8143 10.8921 12.4751C11.0319 12.3405 11.0319 12.3405 11.1746 12.203C11.2467 12.1355 11.3187 12.068 11.3929 11.9985C11.1613 11.9748 11.1613 11.9748 10.9626 12.2209C10.7848 12.4113 10.6073 12.602 10.4301 12.7929C10.1757 13.0154 10.1757 13.0154 9.7699 13.0154C9.81224 12.5143 10.0286 12.3058 10.3849 11.9667C10.5247 11.832 10.5247 11.832 10.6674 11.6946C10.7395 11.6271 10.8115 11.5595 10.8857 11.49C10.5636 11.455 10.5636 11.455 10.3441 11.716C10.1684 11.9086 9.99298 12.1014 9.81785 12.2944C9.56702 12.5069 9.56702 12.5069 9.33205 12.4875C9.27569 12.4603 9.21933 12.4332 9.16127 12.4052C9.20936 11.9662 9.46903 11.7609 9.77624 11.4646C9.91609 11.3285 9.91609 11.3285 10.0588 11.1897C10.1308 11.121 10.2029 11.0523 10.2771 10.9815C10.2436 10.9144 10.2102 10.8473 10.1757 10.7781C10.0998 10.8589 10.024 10.9396 9.94584 11.0228C9.84498 11.1278 9.74407 11.2326 9.6431 11.3374C9.59328 11.3908 9.54346 11.4442 9.49213 11.4991C9.35568 11.6396 9.20808 11.7689 9.05983 11.8968C8.92593 11.8968 8.79203 11.8968 8.65407 11.8968C8.6964 11.3956 8.91272 11.1872 9.26905 10.848C9.4089 10.7134 9.4089 10.7134 9.55157 10.5759C9.62362 10.5084 9.69567 10.4409 9.7699 10.3714C9.53827 10.3477 9.53827 10.3477 9.33958 10.5938C9.16178 10.7842 8.98426 10.9749 8.80702 11.1658C8.55263 11.3883 8.55263 11.3883 8.14687 11.3883C8.1892 10.8872 8.40553 10.6787 8.76185 10.3396C8.9017 10.2049 8.9017 10.2049 9.04437 10.0675C9.11642 9.99995 9.18847 9.93244 9.26271 9.86289C8.9406 9.82787 8.9406 9.82787 8.72103 10.0889C8.54535 10.2815 8.36995 10.4743 8.19482 10.6673C7.94399 10.8798 7.94399 10.8798 7.70902 10.8604C7.65266 10.8332 7.5963 10.8061 7.53823 10.7781C7.58632 10.3391 7.846 10.1337 8.15321 9.83746C8.29306 9.70139 8.29306 9.70139 8.43573 9.56257C8.50778 9.49388 8.57983 9.42519 8.65407 9.35442C8.62059 9.2873 8.58712 9.22018 8.55263 9.15103C8.47679 9.23178 8.40094 9.31253 8.3228 9.39573C8.22195 9.50065 8.12103 9.60552 8.02007 9.71035C7.97025 9.7637 7.92043 9.81705 7.8691 9.87202C7.73264 10.0125 7.58504 10.1418 7.43679 10.2697C7.30289 10.2697 7.16899 10.2697 7.03103 10.2697C7.07337 9.76853 7.28969 9.56007 7.64601 9.22094C7.78586 9.08625 7.78586 9.08625 7.92854 8.94883C8.00059 8.88132 8.07264 8.81381 8.14687 8.74425C7.91523 8.72058 7.91523 8.72058 7.71654 8.96671C7.53874 9.15712 7.36122 9.3478 7.18399 9.53874C6.92959 9.76119 6.92959 9.76119 6.52384 9.76119C6.56617 9.26006 6.78249 9.0516 7.13881 8.71247C7.27866 8.57778 7.27866 8.57778 7.42134 8.44036C7.49339 8.37285 7.56544 8.30534 7.63967 8.23578C7.32239 8.20109 7.32239 8.20109 7.11583 8.44116C7.03345 8.53489 6.95107 8.62862 6.86619 8.72519C6.80031 8.8 6.80031 8.8 6.7331 8.87634C6.59457 9.03474 6.45764 9.19441 6.32096 9.35442C6.27597 9.40651 6.23099 9.4586 6.18465 9.51227C5.62896 10.074 5.62896 10.074 5.408 10.7781C5.45704 10.87 5.45704 10.87 5.50706 10.9636C5.73771 11.4552 5.69671 12.1152 5.54471 12.6289C5.35739 13.0004 5.08912 13.2708 4.71734 13.4567C4.10978 13.6402 3.49236 13.6276 2.92273 13.3523C2.17219 12.6563 2.17219 12.6563 2.15203 12.1629C2.1532 12.0709 2.15438 11.9789 2.15559 11.8841C2.15651 11.7915 2.15742 11.699 2.15837 11.6036C2.15954 11.5325 2.16072 11.4615 2.16193 11.3883C2.07367 11.3936 2.07367 11.3936 1.98362 11.399C1.45412 11.4174 1.09198 11.3919 0.640338 11.0832C0.202349 10.637 0.0148746 10.3168 0 9.69128C0.00702062 9.10359 0.0668437 8.79476 0.437458 8.33748C0.961422 7.91069 1.41101 7.86839 2.07912 7.89018C2.57885 7.96106 2.82401 8.18767 3.17633 8.54086C3.13829 8.75696 3.13829 8.75696 3.07489 8.94764C2.82457 8.90283 2.68355 8.85791 2.48527 8.69341C2.17207 8.4781 1.93069 8.45222 1.55329 8.43917C1.13465 8.59252 0.850743 8.75137 0.640338 9.15103C0.533599 9.56377 0.512519 9.79051 0.659358 10.1934C0.893755 10.5564 0.893755 10.5564 1.24898 10.7781C1.71431 10.8502 2.06002 10.8282 2.48527 10.6192C2.87051 10.2845 3.25038 9.91779 3.48065 9.45611C3.50558 9.00613 3.48163 8.73259 3.27777 8.33748C3.36958 7.58556 4.25341 7.06193 4.79936 6.62775C4.8956 6.55368 4.99184 6.47962 5.091 6.40331C5.16214 6.33685 5.23327 6.27038 5.30656 6.20191C5.30656 6.10123 5.30656 6.00055 5.30656 5.89682C5.23072 5.97758 5.15488 6.05833 5.07674 6.14153C4.97588 6.24645 4.87497 6.35132 4.774 6.45614C4.72418 6.50949 4.67436 6.56285 4.62303 6.61782C4.48658 6.75826 4.33898 6.88759 4.19072 7.01546C4.05682 7.01546 3.92292 7.01546 3.78497 7.01546C3.8273 6.51433 4.04362 6.30587 4.39994 5.96674C4.49318 5.87694 4.58641 5.78715 4.68247 5.69463C4.75452 5.62712 4.82657 5.55961 4.9008 5.49005C4.66917 5.46638 4.66917 5.46638 4.47048 5.7125C4.29268 5.90292 4.11515 6.09359 3.93792 6.28453C3.68353 6.50699 3.68353 6.50699 3.27777 6.50699C3.3201 6.00586 3.53642 5.7974 3.89275 5.45827C3.98598 5.36847 4.07921 5.27868 4.17527 5.18616C4.24732 5.11865 4.31937 5.05114 4.3936 4.98158C4.0715 4.94656 4.0715 4.94656 3.85193 5.20761C3.67625 5.40015 3.50084 5.59295 3.32571 5.78599C3.07489 5.99852 3.07489 5.99852 2.83991 5.97905C2.78355 5.95192 2.7272 5.92478 2.66913 5.89682C2.71722 5.45778 2.97689 5.25244 3.28411 4.95616C3.42396 4.82009 3.42396 4.82009 3.56663 4.68126C3.63868 4.61257 3.71073 4.54388 3.78497 4.47311C3.75149 4.40599 3.71802 4.33887 3.68353 4.26972C3.60768 4.35047 3.53184 4.43122 3.4537 4.51442C3.35285 4.61935 3.25193 4.72422 3.15097 4.82904C3.10115 4.88239 3.05133 4.93575 3 4.99072C2.86354 5.13116 2.71594 5.26049 2.56769 5.38836C2.43379 5.38836 2.29989 5.38836 2.16193 5.38836C2.20426 4.88722 2.42059 4.67876 2.77691 4.33964C2.87014 4.24984 2.96338 4.16004 3.05943 4.06753C3.13148 4.00002 3.20353 3.9325 3.27777 3.86295C3.04613 3.83927 3.04613 3.83927 2.84744 4.0854C2.66964 4.27581 2.49212 4.46649 2.31488 4.65743C2.06049 4.87989 2.06049 4.87989 1.65473 4.87989C1.69707 4.37875 1.91339 4.17029 2.26971 3.83117C2.36294 3.74137 2.45618 3.65157 2.55224 3.55906C2.66031 3.45779 2.66031 3.45779 2.77057 3.35448C2.44846 3.31946 2.44846 3.31946 2.2289 3.58051C2.05322 3.77305 1.87781 3.96585 1.70268 4.15889C1.45185 4.37142 1.45185 4.37142 1.21688 4.35195C1.16052 4.32482 1.10416 4.29768 1.0461 4.26972C1.09419 3.83068 1.35386 3.62534 1.66107 3.32905C1.75431 3.23834 1.84754 3.14763 1.9436 3.05416C2.01565 2.98547 2.0877 2.91678 2.16193 2.84601C2.12846 2.77889 2.09498 2.71177 2.06049 2.64262C1.98465 2.72337 1.90881 2.80412 1.83067 2.88732C1.72981 2.99225 1.6289 3.09711 1.52793 3.20194C1.47811 3.25529 1.42829 3.30864 1.37696 3.36361C1.24051 3.50406 1.09291 3.63339 0.944656 3.76125C0.810756 3.76125 0.676856 3.76125 0.538898 3.76125C0.58123 3.26012 0.797555 3.05166 1.15388 2.71253C1.24711 2.62274 1.34034 2.53294 1.4364 2.44042C1.54448 2.33916 1.54448 2.33916 1.65473 2.23584C1.4231 2.21217 1.4231 2.21217 1.22441 2.4583C1.04661 2.64871 0.869086 2.83939 0.69185 3.03033C0.437458 3.25278 0.437458 3.25278 0.0316999 3.25278C0.109824 2.57117 0.583805 2.21373 1.0461 1.74644C1.17182 1.61165 1.17182 1.61165 1.30009 1.47413C2.00926 0.753727 2.00926 0.753727 2.52965 0.709244C2.89622 0.761978 3.08755 0.878009 3.359 1.12704C3.42938 1.1914 3.49976 1.25575 3.57228 1.32206C3.90426 1.63725 4.2301 1.95769 4.5521 2.28312C4.62075 2.35208 4.6894 2.42105 4.76013 2.4921C4.97633 2.70941 5.19217 2.92706 5.408 3.14473C5.55578 3.29339 5.70358 3.44203 5.8514 3.59064C6.21107 3.95232 6.57042 4.31432 6.92959 4.6765C7.05786 4.58092 7.18601 4.48517 7.3141 4.38937C7.38548 4.33607 7.45685 4.28277 7.53039 4.22786C7.83585 3.99371 8.12422 3.74207 8.41315 3.48795C8.94758 3.023 9.49383 2.57842 10.0532 2.14406C10.5425 1.76245 11.0163 1.36443 11.4857 0.958489C11.9578 0.551864 12.1816 0.473132 12.8131 0.507048ZM1.95905 1.72738C3.26458 3.03618 4.57011 4.34498 5.9152 5.69344C6.11605 5.52564 6.3169 5.35785 6.52384 5.18497C6.38499 4.87362 6.22688 4.65783 5.98776 4.41623C5.92004 4.34737 5.85231 4.2785 5.78253 4.20755C5.70941 4.13434 5.63629 4.06113 5.56095 3.98569C5.48565 3.90977 5.41034 3.83384 5.33276 3.75561C5.17356 3.59543 5.0141 3.43552 4.85439 3.27585C4.6099 3.03107 4.3668 2.78497 4.12376 2.53874C3.96891 2.38327 3.81398 2.22788 3.65896 2.07258C3.54989 1.96183 3.54989 1.96183 3.43862 1.84885C3.37026 1.78095 3.30189 1.71305 3.23146 1.64309C3.14182 1.55313 3.14182 1.55313 3.05037 1.46135C2.52363 1.04568 2.46856 1.21659 1.95905 1.72738ZM10.5814 2.33754C10.5211 2.39299 10.4609 2.44844 10.3988 2.50557C9.86281 2.9944 9.31565 3.46297 8.75273 3.92015C8.23264 4.34768 7.72471 4.78941 7.21524 5.22956C7.03073 5.38862 6.84572 5.54708 6.66054 5.70535C6.03871 6.23713 5.4185 6.76938 4.81997 7.32769C4.59648 7.52393 4.59648 7.52393 4.373 7.66932C4.15687 7.81348 4.02165 7.91094 3.88641 8.13409C3.87927 8.47474 3.9197 8.79441 3.96447 9.13196C3.99893 9.60987 3.87116 9.89718 3.58209 10.2697C3.4059 10.464 3.22571 10.6469 3.03289 10.8246C2.72167 11.1281 2.63577 11.3528 2.62475 11.776C2.63218 12.1407 2.66445 12.3702 2.89103 12.6595C3.28824 13.0134 3.4994 13.1204 4.03223 13.1107C4.55615 12.9725 4.86138 12.7402 5.14806 12.2654C5.22545 11.9034 5.19319 11.6455 5.10368 11.2866C4.96939 11.1163 4.83428 10.9467 4.69792 10.7781C4.71644 10.4625 4.82247 10.3366 5.00224 10.0663C4.95412 10.0285 4.906 9.99077 4.85642 9.95187C4.64497 9.69748 4.63237 9.47483 4.59648 9.15103C4.69691 9.15103 4.79733 9.15103 4.9008 9.15103C5.07198 9.40526 5.07198 9.40526 5.20512 9.6595C5.57002 9.50742 5.77632 9.30082 6.03566 9.00484C6.11581 8.91347 6.19597 8.8221 6.27856 8.72797C6.51241 8.45262 6.74086 8.17379 6.96763 7.89257C7.29676 7.48444 7.6384 7.09436 7.99471 6.71038C8.42329 6.24804 8.82776 5.77141 9.22268 5.27991C9.55861 4.86733 9.90503 4.46415 10.2519 4.06082C10.5683 3.69276 10.8807 3.32177 11.1901 2.9477C11.1398 2.84702 11.1398 2.84702 11.0886 2.74431C9.11359 4.72429 7.13856 6.70427 5.10368 8.74425C4.95304 8.69391 4.95304 8.69391 4.79936 8.64256C4.96254 8.23419 5.24939 7.96072 5.55605 7.6575C5.61247 7.6009 5.6689 7.54431 5.72703 7.48599C5.91317 7.29953 6.10002 7.1138 6.28688 6.92806C6.41627 6.79873 6.54561 6.66935 6.67491 6.53993C7.015 6.19976 7.35569 5.86022 7.69652 5.52079C8.04433 5.17418 8.39157 4.827 8.73886 4.47986C9.42011 3.79909 10.102 3.11896 10.7843 2.43923C10.6839 2.38889 10.6839 2.38889 10.5814 2.33754ZM8.34975 7.01546C8.18237 7.21681 8.015 7.41816 7.84255 7.62562C9.14808 8.93442 10.4536 10.2432 11.7987 11.5917C11.9661 11.4239 12.1334 11.2561 12.3059 11.0832C12.1765 10.71 12.0356 10.469 11.7573 10.1903C11.6869 10.1191 11.6165 10.0478 11.5439 9.97447C11.4679 9.89923 11.392 9.824 11.3137 9.74649C11.2357 9.66821 11.1576 9.58993 11.0772 9.50927C10.9125 9.34438 10.7474 9.17986 10.582 9.01569C10.3279 8.76337 10.0752 8.50971 9.82261 8.25584C9.66247 8.09587 9.50226 7.93598 9.34196 7.77618C9.26598 7.69991 9.19 7.62365 9.11172 7.54508C9.04131 7.47547 8.9709 7.40585 8.89836 7.33412C8.83635 7.27242 8.77434 7.21073 8.71045 7.14716C8.56086 6.99377 8.56086 6.99377 8.34975 7.01546Z"/>
              </g>
              <defs>
                <clipPath id="clip0_94_8857">
                  <rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
                </clipPath>
              </defs>
            </svg>
           `,
      value: 'salon',
    },
    hotels: {
      icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1338_128" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="21">
            <rect y="0.5" width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_1338_128)">
            <path d="M9.16634 18.8334C8.93023 18.8334 8.73231 18.7535 8.57259 18.5938C8.41287 18.434 8.33301 18.2361 8.33301 18C8.33301 17.7639 8.41287 17.566 8.57259 17.4063C8.73231 17.2465 8.93023 17.1667 9.16634 17.1667H18.333C18.5691 17.1667 18.767 17.2465 18.9268 17.4063C19.0865 17.566 19.1663 17.7639 19.1663 18C19.1663 18.2361 19.0865 18.434 18.9268 18.5938C18.767 18.7535 18.5691 18.8334 18.333 18.8334H9.16634ZM9.16634 16.3334C9.16634 15.2084 9.52051 14.2257 10.2288 13.3854C10.9372 12.5452 11.833 12.0278 12.9163 11.8334V11.3125C12.9163 11.0764 12.9962 10.8785 13.1559 10.7188C13.3156 10.559 13.5136 10.4792 13.7497 10.4792C13.9858 10.4792 14.1837 10.559 14.3434 10.7188C14.5031 10.8785 14.583 11.0764 14.583 11.3125V11.8334C15.6525 12.0278 16.5448 12.5452 17.2601 13.3854C17.9754 14.2257 18.333 15.2084 18.333 16.3334H9.16634ZM0.833008 9.66669C0.833008 10.125 0.996202 10.5174 1.32259 10.8438C1.64898 11.1702 2.04134 11.3334 2.49967 11.3334C2.95801 11.3334 3.35037 11.1702 3.67676 10.8438C4.00315 10.5174 4.16634 10.125 4.16634 9.66669V3.83335C4.16634 3.37502 4.00315 2.98266 3.67676 2.65627C3.35037 2.32988 2.95801 2.16669 2.49967 2.16669C2.04134 2.16669 1.64898 2.32988 1.32259 2.65627C0.996202 2.98266 0.833008 3.37502 0.833008 3.83335V9.66669ZM5.83301 10C5.83301 10.375 5.96148 10.691 6.21842 10.9479C6.47537 11.2049 6.79134 11.3334 7.16634 11.3334H7.18717C7.29829 11.3334 7.39898 11.3229 7.48926 11.3021C7.57954 11.2813 7.67329 11.257 7.77051 11.2292L13.3538 9.14585C13.59 9.04863 13.7844 8.89238 13.9372 8.6771C14.09 8.46183 14.1663 8.22224 14.1663 7.95835C14.1663 7.73613 14.09 7.54863 13.9372 7.39585C13.7844 7.24308 13.5969 7.16669 13.3747 7.16669H10.833L9.74967 7.58335C9.63856 7.62502 9.5344 7.62502 9.43717 7.58335C9.33995 7.54169 9.27051 7.4653 9.22884 7.35419C9.18717 7.24308 9.19065 7.13544 9.23926 7.03127C9.28787 6.9271 9.36773 6.85419 9.47884 6.81252L10.833 6.33335H16.6663C17.1108 6.33335 17.4997 6.17363 17.833 5.85419C18.1663 5.53474 18.333 5.13891 18.333 4.66669L12.1663 2.35419C11.9997 2.28474 11.8295 2.24655 11.6559 2.2396C11.4823 2.23266 11.3122 2.25696 11.1455 2.31252L7.06217 3.45835C6.70106 3.56946 6.40592 3.77085 6.17676 4.06252C5.94759 4.35419 5.83301 4.68752 5.83301 5.06252V10Z"/>
            </g>
            </svg>`,
      value: "hotels",
    },
    "metro stations": {
      icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1338_93" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="21">
            <rect y="0.5" width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_1338_93)">
            <path d="M9.16634 14.6667H10.833V17.1667H14.9997V18.8334H4.99967V17.1667H9.16634V14.6667ZM6.66634 2.16669H13.333C14.2497 2.16669 15.0344 2.49308 15.6872 3.14585C16.34 3.79863 16.6663 4.58335 16.6663 5.50002V13.4167C16.6663 14.2361 16.3816 14.9271 15.8122 15.4896C15.2427 16.0521 14.5552 16.3334 13.7497 16.3334H11.6663V13.8334H8.33301V16.3334H6.24967C5.43023 16.3334 4.73926 16.0521 4.17676 15.4896C3.61426 14.9271 3.33301 14.2361 3.33301 13.4167V5.50002C3.33301 4.58335 3.65592 3.79863 4.30176 3.14585C4.94759 2.49308 5.73579 2.16669 6.66634 2.16669ZM4.99967 6.33335V8.83335H14.9997V6.33335H4.99967Z"/>
            </g>
            </svg>`,
      value: "metro stations",
    },
    banks: {
      icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1338_61" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="21">
            <rect y="0.5" width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_1338_61)">
            <path d="M4.16699 13.8334V9.66669C4.16699 9.43058 4.24685 9.23266 4.40658 9.07294C4.5663 8.91322 4.76421 8.83335 5.00033 8.83335C5.23644 8.83335 5.43435 8.91322 5.59408 9.07294C5.7538 9.23266 5.83366 9.43058 5.83366 9.66669V13.8334C5.83366 14.0695 5.7538 14.2674 5.59408 14.4271C5.43435 14.5868 5.23644 14.6667 5.00033 14.6667C4.76421 14.6667 4.5663 14.5868 4.40658 14.4271C4.24685 14.2674 4.16699 14.0695 4.16699 13.8334ZM9.16699 13.8334V9.66669C9.16699 9.43058 9.24685 9.23266 9.40658 9.07294C9.5663 8.91322 9.76421 8.83335 10.0003 8.83335C10.2364 8.83335 10.4344 8.91322 10.5941 9.07294C10.7538 9.23266 10.8337 9.43058 10.8337 9.66669V13.8334C10.8337 14.0695 10.7538 14.2674 10.5941 14.4271C10.4344 14.5868 10.2364 14.6667 10.0003 14.6667C9.76421 14.6667 9.5663 14.5868 9.40658 14.4271C9.24685 14.2674 9.16699 14.0695 9.16699 13.8334ZM2.50033 18C2.26421 18 2.0663 17.9202 1.90658 17.7604C1.74685 17.6007 1.66699 17.4028 1.66699 17.1667C1.66699 16.9306 1.74685 16.7327 1.90658 16.5729C2.0663 16.4132 2.26421 16.3334 2.50033 16.3334H17.5003C17.7364 16.3334 17.9344 16.4132 18.0941 16.5729C18.2538 16.7327 18.3337 16.9306 18.3337 17.1667C18.3337 17.4028 18.2538 17.6007 18.0941 17.7604C17.9344 17.9202 17.7364 18 17.5003 18H2.50033ZM14.167 13.8334V9.66669C14.167 9.43058 14.2469 9.23266 14.4066 9.07294C14.5663 8.91322 14.7642 8.83335 15.0003 8.83335C15.2364 8.83335 15.4344 8.91322 15.5941 9.07294C15.7538 9.23266 15.8337 9.43058 15.8337 9.66669V13.8334C15.8337 14.0695 15.7538 14.2674 15.5941 14.4271C15.4344 14.5868 15.2364 14.6667 15.0003 14.6667C14.7642 14.6667 14.5663 14.5868 14.4066 14.4271C14.2469 14.2674 14.167 14.0695 14.167 13.8334ZM17.5003 7.16669H2.41699C2.20866 7.16669 2.03158 7.09377 1.88574 6.94794C1.73991 6.8021 1.66699 6.62502 1.66699 6.41669V5.95835C1.66699 5.80558 1.70519 5.67363 1.78158 5.56252C1.85796 5.45141 1.95866 5.36113 2.08366 5.29169L9.25033 1.70835C9.48644 1.59724 9.73644 1.54169 10.0003 1.54169C10.2642 1.54169 10.5142 1.59724 10.7503 1.70835L17.8753 5.27085C18.0281 5.3403 18.1427 5.44446 18.2191 5.58335C18.2955 5.72224 18.3337 5.86808 18.3337 6.02085V6.33335C18.3337 6.56947 18.2538 6.76738 18.0941 6.9271C17.9344 7.08683 17.7364 7.16669 17.5003 7.16669Z"/>
            </g>
            </svg>`,
      value: "banks",
    },
    workspaces: {
      icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1338_146" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="21">
            <rect y="0.5" width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_1338_146)">
            <path d="M4.16667 18C3.70833 18 3.31597 17.8368 2.98958 17.5104C2.66319 17.184 2.5 16.7917 2.5 16.3333V8C2.5 7.54167 2.66319 7.14931 2.98958 6.82292C3.31597 6.49653 3.70833 6.33333 4.16667 6.33333H5.83333V4.66667C5.83333 4.20833 5.99653 3.81597 6.32292 3.48958C6.64931 3.16319 7.04167 3 7.5 3H12.5C12.9583 3 13.3507 3.16319 13.6771 3.48958C14.0035 3.81597 14.1667 4.20833 14.1667 4.66667V9.66667H15.8333C16.2917 9.66667 16.684 9.82986 17.0104 10.1563C17.3368 10.4826 17.5 10.875 17.5 11.3333V16.3333C17.5 16.7917 17.3368 17.184 17.0104 17.5104C16.684 17.8368 16.2917 18 15.8333 18H10.8333V14.6667H9.16667V18H4.16667ZM4.16667 16.3333H5.83333V14.6667H4.16667V16.3333ZM4.16667 13H5.83333V11.3333H4.16667V13ZM4.16667 9.66667H5.83333V8H4.16667V9.66667ZM7.5 13H9.16667V11.3333H7.5V13ZM7.5 9.66667H9.16667V8H7.5V9.66667ZM7.5 6.33333H9.16667V4.66667H7.5V6.33333ZM10.8333 13H12.5V11.3333H10.8333V13ZM10.8333 9.66667H12.5V8H10.8333V9.66667ZM10.8333 6.33333H12.5V4.66667H10.8333V6.33333ZM14.1667 16.3333H15.8333V14.6667H14.1667V16.3333ZM14.1667 13H15.8333V11.3333H14.1667V13Z"/>
            </g>
            </svg>`,
      value: "workspaces",
    },
    restaurants: {
      icon: `<svg width="20" height="21" viewBox="0 0 20 21" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1338_164" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="21">
            <rect y="0.5" width="20" height="20"/>
            </mask>
            <g mask="url(#mask0_1338_164)">
            <path d="M5.83301 8.00002V3.00002C5.83301 2.76391 5.91287 2.56599 6.07259 2.40627C6.23231 2.24655 6.43023 2.16669 6.66634 2.16669C6.90245 2.16669 7.10037 2.24655 7.26009 2.40627C7.41981 2.56599 7.49967 2.76391 7.49967 3.00002V8.00002H8.33301V3.00002C8.33301 2.76391 8.41287 2.56599 8.57259 2.40627C8.73231 2.24655 8.93023 2.16669 9.16634 2.16669C9.40245 2.16669 9.60037 2.24655 9.76009 2.40627C9.91981 2.56599 9.99967 2.76391 9.99967 3.00002V8.00002C9.99967 8.7778 9.76009 9.45835 9.28092 10.0417C8.80176 10.625 8.20801 11.0139 7.49967 11.2084V18C7.49967 18.2361 7.41981 18.434 7.26009 18.5938C7.10037 18.7535 6.90245 18.8334 6.66634 18.8334C6.43023 18.8334 6.23231 18.7535 6.07259 18.5938C5.91287 18.434 5.83301 18.2361 5.83301 18V11.2084C5.12467 11.0139 4.53092 10.625 4.05176 10.0417C3.57259 9.45835 3.33301 8.7778 3.33301 8.00002V3.00002C3.33301 2.76391 3.41287 2.56599 3.57259 2.40627C3.73231 2.24655 3.93023 2.16669 4.16634 2.16669C4.40245 2.16669 4.60037 2.24655 4.76009 2.40627C4.91981 2.56599 4.99967 2.76391 4.99967 3.00002V8.00002H5.83301ZM14.1663 12.1667H12.4997C12.2636 12.1667 12.0656 12.0868 11.9059 11.9271C11.7462 11.7674 11.6663 11.5695 11.6663 11.3334V6.33335C11.6663 5.36113 12.024 4.42363 12.7393 3.52085C13.4545 2.61808 14.1941 2.16669 14.958 2.16669C15.208 2.16669 15.4163 2.26391 15.583 2.45835C15.7497 2.6528 15.833 2.88196 15.833 3.14585V18C15.833 18.2361 15.7531 18.434 15.5934 18.5938C15.4337 18.7535 15.2358 18.8334 14.9997 18.8334C14.7636 18.8334 14.5656 18.7535 14.4059 18.5938C14.2462 18.434 14.1663 18.2361 14.1663 18V12.1667Z"/>
            </g>
            </svg>`,
      value: "restaurants",
    },
  },
};
export const layer_icons = {
  amenity: {
    Gym: {
      icon: `<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_b_3671_6973)">
      <rect width="46" height="46" rx="12" fill="white" fill-opacity="0.74"/>
      <path d="M18.7344 24.7321C18.7344 24.7321 19.6398 23.51 20.2344 23.2316C22.0292 22.3913 22.3943 22.0261 23.2344 20.2307C23.5127 19.636 24.7344 18.7302 24.7344 18.7302M21.2344 27.2329C21.2344 27.2329 22.4561 26.3271 22.7344 25.7324C23.5745 23.9371 23.9396 23.5719 25.7344 22.7315C26.3289 22.4531 27.2344 21.231 27.2344 21.231" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M25.4313 13.8926C25.9382 13.384 26.7613 13.3826 27.2698 13.8897L32.0816 18.6878C32.5901 19.1948 32.5914 20.0182 32.0845 20.5268L30.5559 22.0608C30.049 22.5694 29.2259 22.5707 28.7174 22.0637L23.9056 17.2656C23.3972 16.7586 23.3959 15.9352 23.9027 15.4265L25.4313 13.8926Z" stroke="black" stroke-width="1.5"/>
      <path d="M15.4138 23.9026C15.9207 23.394 16.7438 23.3927 17.2522 23.8997L22.0641 28.6978C22.5725 29.2049 22.5738 30.0282 22.067 30.5369L20.5384 32.0708C20.0315 32.5795 19.2084 32.5808 18.6999 32.0737L13.8881 27.2756C13.3796 26.7686 13.3783 25.9452 13.8852 25.4366L15.4138 23.9026Z" stroke="black" stroke-width="1.5"/>
      <path d="M28.9377 14.4525C30.8201 11.9854 34.14 14.8401 31.5431 17.0287M14.3781 28.9773C11.9985 30.9687 14.9978 34.1586 17.0674 31.4656" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      <defs>
      <filter id="filter0_b_3671_6973" x="-4" y="-4" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3671_6973"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_3671_6973" result="shape"/>
      </filter>
      </defs>
      </svg>`,
    },
    Lounge: {
      icon: `<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_b_3848_1152)">
      <rect width="46" height="46" rx="12" fill="white" fill-opacity="0.74"/>
      <path d="M14.3956 18.5704C14.9223 18.919 15.0266 19.4255 15.1896 20.003C15.2158 20.0932 15.2421 20.1834 15.2685 20.2736C15.3214 20.4549 15.3738 20.6364 15.4256 20.818C15.4936 21.056 15.5631 21.2935 15.6332 21.5308C15.7063 21.7788 15.7793 22.0268 15.8511 22.2752C15.8718 22.3465 15.8718 22.3465 15.8929 22.4193C15.922 22.5313 15.922 22.5313 15.922 22.6251C15.9666 22.6247 16.0113 22.6242 16.0573 22.6238C16.48 22.62 16.9027 22.6172 17.3254 22.6154C17.5427 22.6144 17.76 22.6131 17.9772 22.6109C18.1872 22.6089 18.3972 22.6078 18.6073 22.6073C18.687 22.6069 18.7668 22.6063 18.8466 22.6052C19.417 22.5983 19.951 22.6167 20.3852 23.0402C20.6482 23.3562 20.7625 23.6817 20.7999 24.0883C20.8036 24.1248 20.8073 24.1613 20.8111 24.199C20.8226 24.3149 20.8334 24.4309 20.8439 24.5469C20.849 24.6029 20.849 24.6029 20.8542 24.6599C20.8745 24.885 20.8933 25.1102 20.911 25.3356C20.9201 25.4499 20.9296 25.5642 20.9391 25.6785C21.0469 27.01 21.0469 27.01 20.797 27.3126C20.5387 27.5439 20.289 27.5284 19.9532 27.5235C19.9032 27.5242 19.8532 27.525 19.8016 27.5257C19.5101 27.5243 19.3295 27.51 19.1095 27.3126C18.9429 27.1044 18.9052 26.9727 18.8282 26.7032C17.8692 26.7032 16.9101 26.7032 15.922 26.7032C15.8988 26.8888 15.8988 26.8888 15.8751 27.0782C15.7648 27.2871 15.6635 27.3955 15.4532 27.5001C15.2519 27.5285 15.0499 27.5218 14.8468 27.5206C14.7647 27.522 14.7647 27.522 14.681 27.5234C14.314 27.5232 14.1266 27.4799 13.8582 27.2208C13.7235 27.013 13.7059 26.8073 13.6729 26.5647C13.6653 26.5131 13.6577 26.4616 13.6499 26.4085C13.6337 26.2978 13.6178 26.1872 13.6022 26.0765C13.5787 25.9095 13.5542 25.7427 13.5295 25.5759C13.477 25.2217 13.4261 24.8672 13.3754 24.5127C13.3661 24.4483 13.3569 24.3838 13.3477 24.3194C13.3093 24.0515 13.271 23.7835 13.2328 23.5155C13.1788 23.1363 13.1245 22.7572 13.0697 22.3782C13.0308 22.1085 12.9922 21.8387 12.9539 21.5689C12.9311 21.409 12.9083 21.2491 12.885 21.0893C12.8592 20.9114 12.834 20.7335 12.809 20.5556C12.8014 20.5041 12.7938 20.4526 12.7859 20.3996C12.6418 19.3587 12.6418 19.3587 12.8563 19.0038C13.2335 18.5341 13.8116 18.3116 14.3956 18.5704ZM13.6075 19.3673C13.4066 19.6763 13.5715 20.1906 13.6217 20.5346C13.6314 20.6025 13.6411 20.6704 13.6508 20.7384C13.677 20.9219 13.7035 21.1054 13.7301 21.2889C13.7579 21.481 13.7854 21.6731 13.8129 21.8652C13.865 22.2287 13.9173 22.592 13.9698 22.9554C14.0295 23.3693 14.0889 23.7832 14.1483 24.1971C14.2705 25.0481 14.393 25.8991 14.5157 26.7501C14.7168 26.7501 14.9179 26.7501 15.1251 26.7501C15.1357 26.6776 15.1464 26.605 15.1573 26.5303C15.2025 26.304 15.2653 26.1901 15.4532 26.0469C15.6243 25.9368 15.7745 25.9408 15.9752 25.9396C16.0086 25.9393 16.0419 25.9389 16.0762 25.9386C16.1863 25.9377 16.2963 25.9375 16.4063 25.9374C16.483 25.9371 16.5597 25.9368 16.6364 25.9365C16.7973 25.9359 16.9581 25.9358 17.1189 25.9358C17.3244 25.9358 17.5299 25.9346 17.7354 25.9329C17.8939 25.9319 18.0524 25.9317 18.2109 25.9318C18.2866 25.9316 18.3624 25.9312 18.4381 25.9305C18.5444 25.9296 18.6505 25.9299 18.7568 25.9304C18.8473 25.9302 18.8473 25.9302 18.9396 25.93C19.1804 25.9629 19.3666 26.0494 19.5196 26.2462C19.5949 26.4116 19.6579 26.5788 19.7189 26.7501C19.8735 26.7501 20.0282 26.7501 20.1876 26.7501C20.1667 26.1499 20.1292 25.5508 20.0851 24.952C20.0792 24.8717 20.0736 24.7913 20.0682 24.7109C20.0603 24.5962 20.0517 24.4816 20.043 24.367C20.0408 24.3326 20.0386 24.2982 20.0363 24.2627C20.0161 24.0153 19.9597 23.8105 19.7875 23.6261C19.5457 23.4412 19.41 23.4147 19.1111 23.4131C19.0725 23.4127 19.0339 23.4123 18.9942 23.4119C18.9103 23.4111 18.8263 23.4104 18.7424 23.4097C18.6096 23.4088 18.4769 23.4075 18.3441 23.4061C18.0621 23.4033 17.7801 23.4009 17.4982 23.3985C17.1718 23.3957 16.8454 23.3928 16.519 23.3895C16.3882 23.3883 16.2573 23.3873 16.1264 23.3863C16.0462 23.3855 15.9659 23.3848 15.8857 23.3839C15.8492 23.3838 15.8128 23.3836 15.7753 23.3834C15.6149 23.3815 15.4659 23.3785 15.3126 23.3282C15.305 23.2999 15.2975 23.2717 15.2897 23.2425C15.1302 22.652 14.9571 22.0652 14.7851 21.4781C14.7279 21.2829 14.671 21.0876 14.6146 20.8922C14.5597 20.7018 14.5041 20.5116 14.4482 20.3214C14.427 20.2493 14.4061 20.1771 14.3854 20.1049C14.3565 20.0041 14.3269 19.9036 14.297 19.8031C14.2804 19.7459 14.2637 19.6888 14.2466 19.6299C14.1846 19.477 14.1291 19.3984 14.0001 19.2969C13.8313 19.2688 13.7527 19.2756 13.6075 19.3673Z" fill="black"/>
      <path d="M32.6767 18.5701C32.9537 18.7375 33.1634 18.991 33.2656 19.2967C33.3051 19.7537 33.243 20.2074 33.1775 20.659C33.168 20.727 33.1585 20.7949 33.149 20.8628C33.1236 21.0451 33.0976 21.2273 33.0714 21.4095C33.044 21.6011 33.017 21.7928 32.99 21.9845C32.9446 22.3058 32.8988 22.6271 32.8527 22.9484C32.7996 23.3192 32.7472 23.6901 32.6951 24.061C32.6501 24.3807 32.6049 24.7003 32.5593 25.0198C32.5321 25.2102 32.5051 25.4006 32.4784 25.591C32.4533 25.7698 32.4277 25.9485 32.4019 26.1271C32.3925 26.1925 32.3832 26.2579 32.3741 26.3233C32.2702 27.0715 32.2702 27.0715 32.0686 27.3002C31.7607 27.5286 31.5457 27.5114 31.1679 27.5144C31.1126 27.5158 31.0572 27.5172 31.0002 27.5186C30.4849 27.5223 30.4849 27.5223 30.2656 27.3123C30.1971 27.2218 30.1971 27.2218 30.1543 27.1306C30.1394 27.1008 30.1245 27.0709 30.1092 27.0402C30.0697 26.9095 30.0731 26.7918 30.0781 26.656C30.0469 26.6575 30.0158 26.6589 29.9836 26.6603C29.0302 26.7024 28.0791 26.7084 27.125 26.7029C27.1195 26.7315 27.114 26.7601 27.1084 26.7895C27.1007 26.8272 27.093 26.8649 27.085 26.9038C27.0776 26.9411 27.0702 26.9784 27.0626 27.0168C27.0054 27.2135 26.8861 27.3503 26.7113 27.4594C26.5035 27.5417 26.2732 27.5171 26.0527 27.5174C26.0022 27.5185 25.9518 27.5197 25.8998 27.5208C25.6056 27.5219 25.4249 27.5124 25.2031 27.3123C24.7998 26.8241 25.0418 25.8708 25.088 25.2828C25.1006 25.1206 25.1123 24.9584 25.1241 24.7962C25.1321 24.6918 25.1402 24.5874 25.1483 24.4829C25.1517 24.4351 25.1551 24.3873 25.1586 24.3381C25.2015 23.8136 25.3231 23.3383 25.7118 22.9628C26.1802 22.584 26.692 22.6056 27.2628 22.6111C27.3442 22.6113 27.4256 22.6115 27.507 22.6117C27.7199 22.6122 27.9328 22.6136 28.1457 22.6151C28.3635 22.6166 28.5812 22.6172 28.799 22.6179C29.2254 22.6194 29.6517 22.6218 30.0781 22.6248C30.0895 22.5781 30.1008 22.5315 30.1126 22.4834C30.2161 22.0622 30.3283 21.6446 30.4516 21.2286C30.4771 21.1424 30.4771 21.1424 30.5031 21.0545C30.5386 20.9345 30.5742 20.8146 30.6099 20.6947C30.6554 20.5419 30.7003 20.389 30.7451 20.2361C30.7885 20.0883 30.8325 19.9407 30.8764 19.7931C30.8923 19.7387 30.9082 19.6843 30.9245 19.6282C31.1277 18.9518 31.1277 18.9518 31.4111 18.7195C31.4371 18.6975 31.4632 18.6754 31.49 18.6527C31.8189 18.4318 32.3153 18.4099 32.6767 18.5701ZM31.8125 19.4842C31.7655 19.6033 31.7274 19.7155 31.693 19.8381C31.6773 19.8912 31.6773 19.8912 31.6612 19.9453C31.6269 20.062 31.5934 20.1788 31.56 20.2957C31.5362 20.377 31.5124 20.4582 31.4886 20.5395C31.426 20.7532 31.3642 20.9671 31.3025 21.181C31.2397 21.3986 31.1761 21.616 31.1125 21.8334C31.0199 22.151 30.9275 22.4686 30.8357 22.7865C30.826 22.8197 30.8164 22.8529 30.8065 22.8871C30.7643 23.0335 30.7228 23.1797 30.6875 23.3279C30.5008 23.3894 30.3115 23.3816 30.1174 23.3814C30.0597 23.3817 30.0597 23.3817 30.0009 23.3819C29.9176 23.3823 29.8342 23.3825 29.7508 23.3827C29.6188 23.383 29.4868 23.3838 29.3548 23.3846C28.9795 23.3871 28.6041 23.3893 28.2288 23.3901C27.9991 23.3905 27.7694 23.3919 27.5398 23.3939C27.4523 23.3945 27.3649 23.3947 27.2775 23.3946C27.1551 23.3945 27.0327 23.3955 26.9103 23.3968C26.8743 23.3965 26.8384 23.3962 26.8014 23.3959C26.4999 23.4011 26.3346 23.5087 26.1264 23.7173C26.0274 23.8744 26.0114 23.9916 25.993 24.1761C25.9896 24.2085 25.9862 24.2409 25.9827 24.2744C25.9719 24.3807 25.9623 24.4871 25.9531 24.5935C25.9481 24.6481 25.9481 24.6481 25.943 24.7038C25.9223 24.9326 25.9035 25.1615 25.8859 25.3906C25.8768 25.5088 25.8674 25.627 25.8579 25.7453C25.8519 25.8218 25.8459 25.8983 25.8399 25.9749C25.8371 26.0096 25.8344 26.0442 25.8315 26.08C25.8146 26.3039 25.8101 26.5252 25.8125 26.7498C25.9826 26.7498 26.1528 26.7498 26.3281 26.7498C26.3465 26.6773 26.3648 26.6048 26.3838 26.5301C26.4587 26.28 26.5616 26.138 26.7834 25.9948C26.9332 25.9363 27.0644 25.9402 27.2251 25.9393C27.2584 25.939 27.2918 25.9387 27.3261 25.9383C27.4361 25.9375 27.5461 25.9373 27.6562 25.9372C27.7329 25.9369 27.8096 25.9366 27.8863 25.9362C28.0471 25.9357 28.2079 25.9355 28.3687 25.9355C28.5743 25.9356 28.7798 25.9343 28.9853 25.9327C29.1438 25.9316 29.3023 25.9314 29.4608 25.9315C29.5365 25.9314 29.6122 25.931 29.688 25.9302C29.7942 25.9293 29.9004 25.9296 30.0066 25.9301C30.0971 25.9299 30.0971 25.9299 30.1895 25.9297C30.4022 25.9588 30.5106 26.0199 30.6875 26.1404C30.818 26.3363 30.8517 26.5174 30.875 26.7498C31.0761 26.7498 31.2772 26.7498 31.4843 26.7498C31.6221 25.9099 31.7468 25.0686 31.8645 24.2257C31.8732 24.1635 31.8819 24.1013 31.8906 24.0392C31.936 23.7151 31.9813 23.3909 32.0264 23.0668C32.0683 22.7665 32.1104 22.4663 32.1527 22.1661C32.1893 21.9059 32.2257 21.6456 32.262 21.3853C32.2835 21.2309 32.3051 21.0765 32.3269 20.9221C32.351 20.7508 32.3748 20.5795 32.3985 20.4081C32.4057 20.3578 32.4129 20.3075 32.4202 20.2557C32.5064 19.7952 32.5064 19.7952 32.3925 19.367C32.1492 19.2133 31.984 19.2718 31.8125 19.4842Z" fill="black"/>
      <path d="M20.2968 20.3624C20.3463 20.362 20.3958 20.3616 20.4468 20.3612C20.5006 20.3613 20.5544 20.3613 20.6098 20.3613C20.6668 20.361 20.7239 20.3607 20.7826 20.3604C20.9388 20.3596 21.0949 20.3593 21.251 20.3593C21.3487 20.3592 21.4464 20.359 21.544 20.3588C21.8852 20.3579 22.2263 20.3575 22.5675 20.3576C22.8848 20.3576 23.2022 20.3567 23.5195 20.3552C23.7925 20.3539 24.0655 20.3534 24.3385 20.3535C24.5013 20.3535 24.6641 20.3533 24.8269 20.3523C25.0087 20.3512 25.1904 20.3516 25.3722 20.3522C25.4257 20.3517 25.4792 20.3511 25.5343 20.3506C25.899 20.3535 26.1514 20.4075 26.4333 20.6546C26.4586 20.6851 26.4838 20.7156 26.5098 20.747C26.5358 20.7774 26.5618 20.8078 26.5886 20.8391C26.7985 21.1439 26.7817 21.4705 26.7501 21.8281C26.6713 22.1402 26.4471 22.3524 26.1876 22.5312C25.9614 22.6523 25.7391 22.6369 25.4894 22.6341C25.45 22.634 25.4106 22.6339 25.37 22.6338C25.2236 22.6333 25.0772 22.632 24.9307 22.6308C24.4391 22.6279 24.4391 22.6279 23.9376 22.625C23.9376 23.615 23.9376 24.605 23.9376 25.625C24.0613 25.625 24.1851 25.625 24.3126 25.625C24.4282 25.6975 24.4282 25.6975 24.5001 25.8125C24.5243 25.9685 24.5243 25.9685 24.525 26.1523C24.5252 26.2012 24.5252 26.2012 24.5255 26.251C24.5256 26.3198 24.5253 26.3885 24.5245 26.4572C24.5235 26.5621 24.5245 26.667 24.5257 26.7719C24.5256 26.8388 24.5253 26.9057 24.525 26.9726C24.5248 27.0333 24.5246 27.0939 24.5243 27.1565C24.4955 27.3421 24.4578 27.3854 24.3126 27.5C24.1916 27.5238 24.1916 27.5238 24.0533 27.5242C24.0014 27.5248 23.9494 27.5254 23.8959 27.526C23.8121 27.5254 23.8121 27.5254 23.7266 27.5249C23.6403 27.5251 23.6403 27.5251 23.5522 27.5254C23.4306 27.5255 23.3089 27.5252 23.1872 27.5244C23.0008 27.5234 22.8144 27.5244 22.628 27.5256C22.5098 27.5255 22.3917 27.5252 22.2735 27.5249C22.2176 27.5252 22.1618 27.5256 22.1042 27.526C22.0263 27.5251 22.0263 27.5251 21.9469 27.5242C21.8784 27.524 21.8784 27.524 21.8085 27.5238C21.6524 27.493 21.5965 27.4369 21.5001 27.3125C21.4758 27.1565 21.4758 27.1565 21.4752 26.9726C21.475 26.94 21.4748 26.9074 21.4746 26.8739C21.4745 26.8052 21.4748 26.7364 21.4756 26.6677C21.4766 26.5628 21.4756 26.458 21.4744 26.353C21.4746 26.2861 21.4748 26.2192 21.4752 26.1523C21.4754 26.0916 21.4756 26.031 21.4758 25.9685C21.5001 25.8125 21.5001 25.8125 21.5719 25.6975C21.6876 25.625 21.6876 25.625 22.0626 25.625C22.0626 24.635 22.0626 23.645 22.0626 22.625C21.4161 22.6324 21.4161 22.6324 20.7697 22.6418C20.6879 22.6424 20.6062 22.6429 20.5245 22.6433C20.4621 22.6447 20.4621 22.6447 20.3984 22.6462C20.0654 22.6464 19.8192 22.5665 19.5668 22.3454C19.5416 22.3148 19.5163 22.2843 19.4903 22.2529C19.4643 22.2225 19.4383 22.1921 19.4116 22.1608C19.2016 21.856 19.2185 21.5294 19.2501 21.1718C19.3289 20.8597 19.5531 20.6475 19.8126 20.4687C19.9803 20.3903 20.1122 20.3631 20.2968 20.3624ZM20.0938 21.2656C20.0272 21.3987 20.0322 21.4931 20.0469 21.6406C20.1516 21.7741 20.2134 21.821 20.3751 21.875C20.4464 21.8794 20.5179 21.8813 20.5894 21.8815C20.6553 21.8819 20.6553 21.8819 20.7226 21.8824C20.7708 21.8824 20.8189 21.8825 20.8685 21.8825C20.9193 21.8827 20.9701 21.883 21.0224 21.8832C21.1907 21.8839 21.359 21.8842 21.5274 21.8845C21.6442 21.8847 21.761 21.885 21.8779 21.8853C22.0916 21.8857 22.3054 21.886 22.5192 21.8862C22.8023 21.8863 23.0854 21.8869 23.3685 21.8881C23.6723 21.8894 23.9761 21.89 24.2799 21.8901C24.3957 21.8903 24.5115 21.8906 24.6274 21.8913C24.7894 21.8921 24.9514 21.892 25.1134 21.8917C25.1613 21.8921 25.2092 21.8925 25.2585 21.893C25.5088 21.8915 25.6916 21.8789 25.9063 21.7343C25.9729 21.6012 25.9679 21.5068 25.9532 21.3593C25.8486 21.2258 25.7868 21.1789 25.6251 21.125C25.5537 21.1205 25.4822 21.1186 25.4107 21.1184C25.3668 21.1181 25.3228 21.1178 25.2775 21.1176C25.2294 21.1175 25.1812 21.1175 25.1316 21.1174C25.0809 21.1172 25.0301 21.1169 24.9778 21.1167C24.8094 21.116 24.6411 21.1157 24.4728 21.1154C24.356 21.1152 24.2391 21.1149 24.1223 21.1146C23.9085 21.1142 23.6947 21.1139 23.4809 21.1138C23.1978 21.1136 22.9147 21.113 22.6316 21.1118C22.3278 21.1105 22.024 21.1099 21.7202 21.1098C21.6044 21.1096 21.4886 21.1093 21.3728 21.1087C21.2108 21.1079 21.0487 21.1079 20.8867 21.1082C20.8388 21.1078 20.791 21.1074 20.7417 21.1069C20.4913 21.1084 20.3086 21.121 20.0938 21.2656ZM22.8126 22.625C22.8126 23.615 22.8126 24.605 22.8126 25.625C22.9363 25.625 23.0601 25.625 23.1876 25.625C23.1876 24.635 23.1876 23.645 23.1876 22.625C23.0638 22.625 22.9401 22.625 22.8126 22.625ZM22.2501 26.375C22.2501 26.4987 22.2501 26.6225 22.2501 26.75C22.7451 26.75 23.2401 26.75 23.7501 26.75C23.7501 26.6262 23.7501 26.5025 23.7501 26.375C23.2551 26.375 22.7601 26.375 22.2501 26.375Z" fill="black"/>
      </g>
      <defs>
      <filter id="filter0_b_3848_1152" x="-4" y="-4" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3848_1152"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_3848_1152" result="shape"/>
      </filter>
      </defs>
      </svg>`,
    },
    Lobby: {
      icon: `<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_b_3848_1142)">
      <rect width="46" height="46" rx="12" fill="white" fill-opacity="0.74"/>
      <g clip-path="url(#clip0_3848_1142)">
      <path d="M20.994 14.6928C21.5008 15.1453 21.9057 15.7919 21.9529 16.4858C21.9926 17.4166 21.7904 18.1395 21.1964 18.8746C21.2301 18.8871 21.2637 18.8996 21.2984 18.9125C21.7287 19.094 22.0852 19.3938 22.4151 19.7184C22.4687 19.771 22.4687 19.771 22.5234 19.8247C23.4739 20.8181 23.6808 22.0444 23.6808 23.3746C24.7172 23.3746 25.7536 23.3746 26.8214 23.3746C26.8214 23.2199 26.8214 23.0652 26.8214 22.9059C26.9028 22.464 27.1661 22.1295 27.5146 21.8556C27.7363 21.7169 28.0562 21.5934 28.3214 21.5934C28.3059 21.5624 28.2905 21.5315 28.2745 21.4996C28.2609 21.3456 28.2615 21.1912 28.2599 21.0367C28.2585 20.994 28.2572 20.9513 28.2558 20.9073C28.2528 20.5941 28.2528 20.5941 28.3575 20.4402C28.4667 20.3717 28.522 20.3492 28.6495 20.3512C28.6959 20.3504 28.6959 20.3504 28.7433 20.3497C28.8907 20.3889 28.938 20.4349 29.0245 20.5621C29.0487 20.7209 29.0443 20.8765 29.0392 21.0367C29.0387 21.0798 29.0383 21.1229 29.0378 21.1673C29.0325 21.4837 29.0325 21.4837 28.9776 21.5934C29.006 21.5969 29.0344 21.6004 29.0637 21.604C29.5241 21.6762 29.8797 21.8798 30.1744 22.2427C30.4229 22.6062 30.4794 22.9448 30.4776 23.3746C30.5194 23.3735 30.5194 23.3735 30.5619 23.3724C31.2749 23.356 31.9422 23.3562 32.587 23.7027C32.6244 23.7224 32.6618 23.7421 32.7004 23.7624C33.4508 24.1836 33.9677 24.8507 34.2078 25.6748C34.278 25.9387 34.2945 26.1829 34.2927 26.4549C34.293 26.5022 34.2932 26.5494 34.2935 26.5981C34.2941 26.7526 34.2937 26.9071 34.2932 27.0616C34.2933 27.17 34.2934 27.2784 34.2936 27.3868C34.2938 27.6132 34.2935 27.8397 34.2929 28.0662C34.2921 28.3554 34.2925 28.6446 34.2934 28.9338C34.2939 29.1574 34.2937 29.381 34.2933 29.6046C34.2932 29.7112 34.2934 29.8177 34.2937 29.9243C34.294 30.0737 34.2935 30.2231 34.2927 30.3724C34.293 30.4158 34.2933 30.4592 34.2936 30.5038C34.286 31.295 33.9162 32.0253 33.381 32.5943C32.8269 33.1256 32.0651 33.4791 31.2894 33.4652C31.1808 33.4527 31.1808 33.4527 31.0577 33.3854C30.9645 33.2117 30.939 33.0348 30.9933 32.8434C31.1654 32.6628 31.3164 32.6266 31.5587 32.5914C32.148 32.49 32.6378 32.2161 33.0074 31.7401C33.4911 31.028 33.4852 30.2867 33.4855 29.4588C33.4857 29.376 33.4858 29.2931 33.486 29.2102C33.4863 29.037 33.4864 28.8638 33.4863 28.6906C33.4863 28.4704 33.487 28.2502 33.4878 28.03C33.4883 27.8589 33.4884 27.6878 33.4884 27.5167C33.4884 27.4357 33.4886 27.3546 33.489 27.2735C33.5027 26.342 33.5027 26.342 33.2169 25.4693C33.2005 25.438 33.184 25.4066 33.1671 25.3742C32.8679 24.8289 32.389 24.4763 31.7997 24.2907C31.5681 24.2336 31.3465 24.2125 31.1083 24.2122C31.047 24.212 30.9858 24.2118 30.9226 24.2117C30.8549 24.2117 30.7872 24.2117 30.7196 24.2118C30.6474 24.2117 30.5752 24.2115 30.5031 24.2114C30.3049 24.211 30.1068 24.2109 29.9086 24.2109C29.6948 24.2108 29.4811 24.2104 29.2673 24.2101C28.7999 24.2095 28.3325 24.2092 27.8651 24.209C27.5732 24.2088 27.2812 24.2086 26.9893 24.2084C26.1808 24.2079 25.3723 24.2074 24.5638 24.2072C24.4862 24.2072 24.4862 24.2072 24.407 24.2072C24.3293 24.2072 24.3293 24.2072 24.2499 24.2072C24.1448 24.2071 24.0397 24.2071 23.9347 24.2071C23.8825 24.2071 23.8304 24.2071 23.7767 24.2071C22.9323 24.2069 22.0879 24.2061 21.2435 24.205C20.3763 24.2039 19.5091 24.2033 18.6419 24.2032C18.1551 24.2032 17.6683 24.2029 17.1815 24.2021C16.7669 24.2013 16.3524 24.2011 15.9378 24.2015C15.7264 24.2017 15.515 24.2016 15.3037 24.201C15.1099 24.2004 14.9161 24.2004 14.7223 24.201C14.6524 24.2011 14.5826 24.2009 14.5128 24.2005C13.7237 24.1962 13.0735 24.3505 12.4897 24.9032C12.334 25.068 12.2128 25.2393 12.1026 25.4371C12.0844 25.4692 12.0661 25.5013 12.0473 25.5343C11.7639 26.0941 11.8073 26.7754 11.8089 27.3868C11.8089 27.4694 11.8088 27.5521 11.8087 27.6347C11.8085 27.8067 11.8087 27.9788 11.8092 28.1509C11.8097 28.3696 11.8094 28.5883 11.8088 28.8071C11.8085 28.9773 11.8086 29.1476 11.8089 29.3179C11.8089 29.3985 11.8088 29.479 11.8086 29.5596C11.8067 30.4616 11.8521 31.3204 12.5245 31.9996C12.93 32.3504 13.3977 32.6145 13.9436 32.6151C13.9939 32.6153 14.0443 32.6154 14.0962 32.6156C14.1514 32.6156 14.2067 32.6155 14.2637 32.6155C14.3225 32.6156 14.3812 32.6157 14.4418 32.6159C14.605 32.6162 14.7681 32.6163 14.9313 32.6163C15.1073 32.6164 15.2833 32.6168 15.4593 32.6171C15.7642 32.6176 16.0692 32.618 16.3741 32.6183C16.856 32.6188 17.3379 32.6198 17.8199 32.6208C17.9847 32.6212 18.1495 32.6215 18.3144 32.6219C18.3762 32.622 18.3762 32.622 18.4392 32.6221C18.9081 32.6231 19.3771 32.624 19.846 32.6248C19.9103 32.6249 19.9103 32.6249 19.9759 32.6251C20.6709 32.6263 21.366 32.6272 22.061 32.6279C22.7744 32.6288 23.4877 32.6301 24.201 32.6319C24.6412 32.633 25.0813 32.6337 25.5215 32.6338C25.823 32.6339 26.1245 32.6345 26.426 32.6355C26.6 32.636 26.7741 32.6364 26.9481 32.6361C27.1367 32.6358 27.3252 32.6366 27.5138 32.6375C27.569 32.6372 27.6241 32.637 27.681 32.6367C27.7315 32.6371 27.782 32.6375 27.834 32.638C27.8775 32.638 27.921 32.6381 27.9659 32.6381C28.1437 32.6641 28.2746 32.7369 28.3858 32.8785C28.4299 33.0372 28.4086 33.1538 28.3683 33.3121C28.2279 33.4499 28.1252 33.4703 27.9319 33.4706C27.8811 33.471 27.8304 33.4714 27.7781 33.4718C27.7223 33.4715 27.6666 33.4712 27.6092 33.4709C27.55 33.4712 27.4907 33.4714 27.4297 33.4717C27.2652 33.4724 27.1007 33.4722 26.9362 33.4718C26.7587 33.4716 26.5813 33.4722 26.4039 33.4727C26.0562 33.4735 25.7084 33.4735 25.3607 33.4732C25.0779 33.4731 24.795 33.4731 24.5122 33.4734C24.472 33.4734 24.4317 33.4734 24.3902 33.4735C24.3085 33.4736 24.2267 33.4736 24.145 33.4737C23.3776 33.4743 22.6103 33.4741 21.843 33.4736C21.1418 33.4731 20.4407 33.4737 19.7396 33.4748C19.0193 33.476 18.2991 33.4764 17.5788 33.4761C17.1745 33.4759 16.7703 33.476 16.3661 33.4769C16.0217 33.4775 15.6773 33.4776 15.3329 33.4768C15.1574 33.4764 14.9819 33.4763 14.8063 33.477C14.6155 33.4778 14.4246 33.4771 14.2337 33.4762C14.1791 33.4767 14.1244 33.4771 14.068 33.4776C13.1585 33.4695 12.4457 33.099 11.8214 32.4684C11.7895 32.4375 11.7576 32.4066 11.7247 32.3748C11.0229 31.6464 11.0025 30.7135 11.0042 29.7661C11.004 29.6581 11.0037 29.5501 11.0035 29.4422C11.003 29.2166 11.0031 28.991 11.0036 28.7654C11.0042 28.4778 11.0032 28.1903 11.0018 27.9027C11.001 27.6799 11.001 27.4571 11.0013 27.2343C11.0013 27.1283 11.001 27.0224 11.0004 26.9164C10.9977 26.3465 11.0029 25.8345 11.212 25.2965C11.2244 25.2644 11.2368 25.2323 11.2496 25.1992C11.574 24.4421 12.2423 23.8711 12.9933 23.5621C13.3893 23.4324 13.7669 23.3668 14.1827 23.3717C14.2172 23.3719 14.2517 23.3721 14.2873 23.3723C14.3716 23.3729 14.4559 23.3737 14.5401 23.3746C14.538 23.3401 14.5358 23.3056 14.5336 23.2701C14.5258 23.061 14.5206 22.9024 14.6075 22.7096C14.7607 22.6012 14.8651 22.5932 15.0497 22.6059C15.1495 22.6246 15.1495 22.6246 15.2696 22.6861C15.337 22.8121 15.337 22.8121 15.337 23.3277C17.8275 23.3277 20.318 23.3277 22.8839 23.3277C22.7773 22.0485 22.6155 20.9931 21.6302 20.1199C21.1869 19.7708 20.6916 19.5263 20.16 19.3397C20.0714 19.2965 20.0714 19.2965 19.9776 19.1559C19.9408 18.8293 19.9408 18.8293 20.0714 18.6578C20.146 18.5988 20.2212 18.5402 20.2995 18.4862C20.7371 18.1807 20.9989 17.758 21.1026 17.234C21.1657 16.5852 21.0538 16.0448 20.6462 15.5219C20.2367 15.0735 19.6983 14.8679 19.0995 14.8388C18.574 14.8545 18.0758 15.0534 17.6925 15.4117C17.244 15.8888 17.0951 16.3848 17.1067 17.0249C17.1402 17.4956 17.3425 17.895 17.6485 18.2506C17.7683 18.3569 17.7683 18.3569 17.8796 18.4342C18.1798 18.6515 18.1798 18.6515 18.2755 18.857C18.2914 19.0283 18.2887 19.1052 18.1964 19.2496C18.0177 19.372 17.8304 19.4439 17.628 19.5191C16.6526 19.8981 16.0297 20.5964 15.606 21.5369C15.5003 21.7644 15.5003 21.7644 15.3833 21.8493C15.2285 21.8913 15.1156 21.8689 14.962 21.8277C14.8448 21.7311 14.8448 21.7311 14.7745 21.5934C14.7888 21.3124 14.8562 21.1028 14.9855 20.8551C15.0021 20.8226 15.0188 20.7902 15.036 20.7567C15.4537 19.9677 16.0695 19.365 16.837 18.9215C16.8991 18.9041 16.9615 18.888 17.0245 18.8746C16.9968 18.8371 16.9691 18.7997 16.9405 18.7611C16.6965 18.4248 16.5028 18.0994 16.3683 17.7027C16.3568 17.67 16.3453 17.6372 16.3335 17.6035C16.1221 16.8594 16.2923 16.0474 16.6429 15.3808C17.0208 14.7395 17.6365 14.321 18.337 14.0934C19.2687 13.859 20.2607 14.0685 20.994 14.6928ZM27.8752 22.5962C27.646 22.8571 27.6183 22.9726 27.6183 23.3277C28.2989 23.3277 28.9795 23.3277 29.6808 23.3277C29.6497 22.9201 29.6497 22.9201 29.4464 22.6246C29.0393 22.3108 28.2816 22.2232 27.8752 22.5962Z" fill="black"/>
      <path d="M22.9855 23.0786C23.0997 23.1547 23.0997 23.1547 23.1466 23.2485C23.1736 23.5839 23.1736 23.5839 23.0754 23.7279C22.9158 23.842 22.773 23.8385 22.5841 23.8344C22.5507 23.8352 22.5174 23.8359 22.483 23.8366C22.3215 23.8354 22.2256 23.8229 22.0934 23.7275C21.9967 23.5874 21.9948 23.5032 22.0031 23.3377C22.0316 23.2002 22.0971 23.141 22.2091 23.061C22.4695 22.9742 22.7301 22.98 22.9855 23.0786Z" fill="black"/>
      </g>
      </g>
      <defs>
      <filter id="filter0_b_3848_1142" x="-4" y="-4" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3848_1142"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_3848_1142" result="shape"/>
      </filter>
      <clipPath id="clip0_3848_1142">
      <rect width="24" height="24" fill="white" transform="translate(11 11)"/>
      </clipPath>
      </defs>
      </svg>`,
    },
  },
};
export const landmark_icon = {
  landmark: {
    icon: `<svg width="13" height="16" viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.28571 0C4.61922 0.00189054 3.02152 0.664747 1.84313 1.84315C0.66474 3.02155 0.00189052 4.61927 0 6.28578C0 11.6644 5.71429 15.7266 5.95786 15.8966C6.05394 15.9639 6.1684 16 6.28571 16C6.40302 16 6.51749 15.9639 6.61357 15.8966C6.85714 15.7266 12.5714 11.6644 12.5714 6.28578C12.5695 4.61927 11.9067 3.02155 10.7283 1.84315C9.54991 0.664747 7.95221 0.00189054 6.28571 0ZM6.28571 4.00004C6.73779 4.00004 7.1797 4.1341 7.55559 4.38526C7.93147 4.63642 8.22444 4.9934 8.39744 5.41106C8.57044 5.82873 8.6157 6.28831 8.52751 6.7317C8.43931 7.17509 8.22162 7.58237 7.90196 7.90204C7.58229 8.2217 7.17502 8.4394 6.73163 8.5276C6.28825 8.61579 5.82867 8.57053 5.41101 8.39752C4.99335 8.22452 4.63637 7.93155 4.38521 7.55567C4.13405 7.17978 4 6.73785 4 6.28578C4 5.67956 4.24082 5.09818 4.66947 4.66952C5.09812 4.24086 5.6795 4.00004 6.28571 4.00004Z" fill="white"/>
      </svg>
      `,
  },
};
export const sidebar_icons = {
  "66026a782f7e24c644a3a3ee": {
    name: "Home",
    active: `<svg width="21" height="21" viewBox="0 0 21 21" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M1.28539 11.7911L1.28541 11.7913L1.56169 13.7538L1.5617 13.7539C2.02096 17.0164 2.2506 18.6477 3.40614 19.6338C4.56169 20.6199 6.26898 20.6414 9.68355 20.6846L11.961 20.7133C15.3756 20.7565 17.0829 20.778 18.2629 19.8215C19.443 18.8649 19.7139 17.2399 20.2553 13.99L20.5811 12.035C20.9632 9.74235 21.1541 8.5961 20.7327 7.57245C20.3114 6.54883 19.3567 5.84004 17.4475 4.42257L17.4473 4.42238L16.0206 3.36322L16.0203 3.363C13.6453 1.59967 12.4578 0.718005 11.0749 0.700536C9.69198 0.683068 8.48244 1.53453 6.06343 3.23744L4.61055 4.26023C2.666 5.62915 1.69371 6.31362 1.24659 7.32631C0.799459 8.33897 0.961432 9.48965 1.28539 11.7911ZM8.35379 15.0755C8.03017 14.8169 7.55824 14.8697 7.29969 15.1933C7.04114 15.5169 7.09389 15.9889 7.4175 16.2474C8.34186 16.9859 9.54935 17.4326 10.8633 17.4492C12.1773 17.4658 13.3957 17.0498 14.3383 16.3348C14.6684 16.0845 14.733 15.6141 14.4827 15.284C14.2324 14.954 13.762 14.8893 13.4319 15.1396C12.76 15.6492 11.8686 15.9618 10.8822 15.9493C9.89594 15.9369 9.01264 15.6019 8.35379 15.0755Z"/>
    </svg>
      `,
    inactive: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 25 25" fill="none">
    <path d="M3.1338 12.7913C2.80983 10.4897 2.64784 9.33899 3.09497 8.32631C3.5421 7.31362 4.51438 6.62915 6.45894 5.26023L7.91182 4.23744C10.3308 2.53453 11.5404 1.68307 12.9233 1.70054C14.3062 1.71801 15.4938 2.59974 17.869 4.36322L19.2957 5.42238C21.205 6.83998 22.1597 7.54878 22.5811 8.57245C23.0025 9.5961 22.8115 10.7424 22.4295 13.035L22.1037 14.99C21.5622 18.2399 21.2914 19.8649 20.1113 20.8215C18.9312 21.778 17.224 21.7565 13.8094 21.7133L11.5319 21.6846C8.11737 21.6414 6.41008 21.6199 5.25453 20.6338C4.09898 19.6477 3.86935 18.0164 3.41008 14.7538L3.1338 12.7913Z" stroke="white" stroke-opacity="0.7" stroke-linejoin="round"/>
    <path d="M9.79645 16.5833L9.48435 16.974L9.79645 16.5833ZM9.6559 16.599L9.26527 16.2869L9.6559 16.599ZM15.6731 16.6576L15.371 16.2592L15.371 16.2592L15.6731 16.6576ZM15.8132 16.6768L15.4148 16.9789L15.4148 16.9789L15.8132 16.6768ZM15.794 16.8169L16.0961 17.2153L16.0961 17.2153L15.794 16.8169ZM9.67161 16.7396L9.98371 16.3489L9.98371 16.3489L9.67161 16.7396ZM10.1085 16.1927C9.84966 15.9858 9.47211 16.028 9.26527 16.2869L10.0465 16.9111C9.90865 17.0837 9.65695 17.1119 9.48435 16.974L10.1085 16.1927ZM12.7287 17.0993C11.7097 17.0864 10.7939 16.7403 10.1085 16.1927L9.48435 16.974C10.3468 17.663 11.4786 18.0836 12.7161 18.0992L12.7287 17.0993ZM15.371 16.2592C14.672 16.7893 13.7479 17.1122 12.7287 17.0993L12.7161 18.0992C13.9537 18.1149 15.0957 17.723 15.9752 17.0559L15.371 16.2592ZM16.2116 16.3747C16.0114 16.1106 15.635 16.0589 15.371 16.2592L15.9752 17.0559C15.7992 17.1894 15.5483 17.155 15.4148 16.9789L16.2116 16.3747ZM16.0961 17.2153C16.3601 17.0151 16.4118 16.6387 16.2116 16.3747L15.4148 16.9789C15.2813 16.8029 15.3158 16.552 15.4918 16.4185L16.0961 17.2153ZM12.7136 18.2992C13.9948 18.3154 15.1805 17.9097 16.0961 17.2153L15.4918 16.4185C14.7567 16.976 13.789 17.3127 12.7262 17.2993L12.7136 18.2992ZM9.35952 17.1302C10.2573 17.8475 11.4324 18.283 12.7136 18.2992L12.7262 17.2993C11.6635 17.2859 10.7045 16.9248 9.98371 16.3489L9.35952 17.1302ZM9.26527 16.2869C9.05843 16.5458 9.10063 16.9234 9.35952 17.1302L9.98371 16.3489C10.1563 16.4868 10.1844 16.7385 10.0465 16.9111L9.26527 16.2869Z" fill="white" fill-opacity="0.7"/>
    </svg>
      `,
  },
  "660294c6d2dcb300f89325e8": {
    name: "Location",
    active: `<svg xmlns="http://www.w3.org/2000/svg"  width="24" height="24" viewBox="0 0 24 24">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0011 22C12.6044 22 13.1841 21.773 13.6177 21.367C13.9205 21.0834 14.231 20.7972 14.5451 20.5075C18.3609 16.9893 22.711 12.9784 20.316 7.37966C18.9126 4.09916 15.5439 2 12.0011 2C8.45834 2 5.08963 4.09916 3.68627 7.37966C1.30092 12.9556 5.60302 16.9368 9.39811 20.4489C9.73249 20.7583 10.0629 21.0641 10.3845 21.367C10.8182 21.773 11.3978 22 12.0011 22ZM12 14.5C13.933 14.5 15.5 12.933 15.5 11C15.5 9.067 13.933 7.5 12 7.5C10.067 7.5 8.5 9.067 8.5 11C8.5 12.933 10.067 14.5 12 14.5Z"/>
    </svg>
    `,
    inactive: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.6177 21.367C13.1841 21.773 12.6044 22 12.0011 22C11.3978 22 10.8182 21.773 10.3845 21.367C6.41302 17.626 1.09076 13.4469 3.68627 7.37966C5.08963 4.09916 8.45834 2 12.0011 2C15.5439 2 18.9126 4.09916 20.316 7.37966C22.9082 13.4393 17.599 17.6389 13.6177 21.367Z" stroke="white" stroke-width="1.5"/>
    <path d="M15.5 11C15.5 12.933 13.933 14.5 12 14.5C10.067 14.5 8.5 12.933 8.5 11C8.5 9.067 10.067 7.5 12 7.5C13.933 7.5 15.5 9.067 15.5 11Z" stroke="white" stroke-width="1.5"/>
    </svg>

    `,
  },
  "660294cd04f5d80bc07059ce": {
    name: "Amenities",
    active: `<svg width="25" height="25" viewBox="0 0 25 25"  xmlns="http://www.w3.org/2000/svg">
    <path d="M20.0654 4.21785C19.8159 3.24231 18.4365 3.22489 18.1625 4.19382L17.7736 5.56861C17.6781 5.90656 17.4098 6.1681 17.0696 6.25512L15.6854 6.60912C14.7099 6.85861 14.6924 8.23801 15.6614 8.51206L17.0362 8.90091C17.3741 8.9965 17.6356 9.26473 17.7227 9.60498L18.0767 10.9892C18.3261 11.9647 19.7056 11.9821 19.9796 11.0132L20.3685 9.6384C20.464 9.30045 20.7323 9.03892 21.0725 8.9519L22.4567 8.5979C23.4322 8.3484 23.4497 6.969 22.4807 6.69495L21.1059 6.3061C20.768 6.21052 20.5065 5.94229 20.4194 5.60203L20.0654 4.21785Z"/>
    <path d="M8.73327 8.15273C8.48378 7.17719 7.10437 7.15976 6.83032 8.12869L5.80761 11.7445C5.71203 12.0825 5.4438 12.344 5.10354 12.431L1.46303 13.3621C0.487493 13.6116 0.470067 14.991 1.43899 15.265L5.05483 16.2877C5.39278 16.3833 5.65431 16.6515 5.74133 16.9918L6.67238 20.6323C6.92188 21.6079 8.30128 21.6253 8.57533 20.6564L9.59804 17.0405C9.69362 16.7026 9.96185 16.441 10.3021 16.354L13.9426 15.423C14.9182 15.1735 14.9356 13.7941 13.9667 13.52L10.3508 12.4973C10.0129 12.4017 9.75134 12.1335 9.66432 11.7932L8.73327 8.15273Z"/>
    </svg>
    `,
    inactive: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" width="25" height="25" viewBox="0 0 25 25">
    <g clip-path="url(#clip0_1907_181)">
    <path d="M19.492 4.3299L19.1032 5.70469C18.9591 6.21412 18.5547 6.60835 18.0418 6.73953L16.6577 7.09353C16.1771 7.21643 16.1685 7.89594 16.6458 8.03094L18.0206 8.41978C18.53 8.56387 18.9243 8.9682 19.0555 9.4811L19.4095 10.8653C19.5324 11.3458 20.2119 11.3544 20.3469 10.8771L20.7357 9.50232C20.8798 8.9929 21.2841 8.59866 21.797 8.46749L23.1812 8.11349C23.6618 7.99059 23.6703 7.31108 23.193 7.17608L21.8183 6.78723C21.3088 6.64314 20.9146 6.23882 20.7834 5.72592L20.4294 4.34174C20.3065 3.86118 19.627 3.85259 19.492 4.3299Z" stroke="white" stroke-opacity="0.7"/>
    <path d="M8.15984 8.26477L7.13713 11.8806C6.99304 12.39 6.58872 12.7843 6.07582 12.9154L2.43531 13.8465C1.95474 13.9694 1.94616 14.6489 2.42347 14.7839L6.0393 15.8066C6.54872 15.9507 6.94296 16.355 7.07413 16.8679L8.00518 20.5084C8.12808 20.989 8.80759 20.9976 8.94259 20.5203L9.9653 16.9044C10.1094 16.395 10.5137 16.0008 11.0266 15.8696L14.6671 14.9386C15.1477 14.8157 15.1563 14.1361 14.679 14.0011L11.0631 12.9784C10.5537 12.8343 10.1595 12.43 10.0283 11.9171L9.09725 8.27661C8.97435 7.79605 8.29484 7.78747 8.15984 8.26477Z" stroke="white" stroke-opacity="0.7"/>
    </g>
    <defs>
    <clipPath id="clip0_1907_181">
    <rect width="24" height="24" fill="white" transform="translate(1) rotate(0.361844)"/>
    </clipPath>
    </defs>
    </svg>
    `,
  },
  "660294f57e2049d8e6fce0a0": {
    name: "Gallery",
    active: `<svg width="24" height="24" viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg">
    <path d="M7 8C7 5.64298 7 4.46447 7.73223 3.73223C8.46447 3 9.64298 3 12 3C14.357 3 15.5355 3 16.2678 3.73223C17 4.46447 17 5.64298 17 8L17 16C17 18.357 17 19.5355 16.2678 20.2678C15.5355 21 14.357 21 12 21C9.64298 21 8.46447 21 7.73223 20.2678C7 19.5355 7 18.357 7 16L7 8Z"/>
    <path d="M2 7C2.54697 7.10449 2.94952 7.28931 3.26777 7.61621C4 8.36835 4 9.5789 4 12C4 14.4211 4 15.6316 3.26777 16.3838C2.94952 16.7107 2.54697 16.8955 2 17" />
    <path d="M22 7C21.453 7.10449 21.0505 7.28931 20.7322 7.61621C20 8.36835 20 9.5789 20 12C20 14.4211 20 15.6316 20.7322 16.3838C21.0505 16.7107 21.453 16.8955 22 17" />
    </svg>
  `,
    inactive: `<svg width="25" height="25" viewBox="0 0 25 25"  fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.8984 8.08778C7.92817 5.73095 7.94305 4.55253 8.68447 3.8296C9.4259 3.10668 10.6043 3.12156 12.9611 3.15133C15.318 3.1811 16.4964 3.19599 17.2194 3.93741C17.9423 4.67884 17.9274 5.85725 17.8976 8.21409L17.7966 16.2134C17.7668 18.5703 17.7519 19.7487 17.0105 20.4717C16.269 21.1946 15.0906 21.1797 12.7338 21.1499C10.377 21.1201 9.19855 21.1052 8.47562 20.3639C7.7527 19.6224 7.76758 18.444 7.79735 16.0871L7.8984 8.08778Z" stroke="white" stroke-opacity="0.7" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2.91138 7.0247C3.45698 7.13609 3.85717 7.32598 4.17126 7.65688C4.89393 8.41821 4.87864 9.62866 4.84807 12.0496C4.81749 14.4705 4.8022 15.6809 4.06052 16.4238C3.73817 16.7466 3.33332 16.9263 2.78507 17.0239" stroke="white" stroke-opacity="0.7" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M22.9099 7.27731C22.3616 7.37488 21.9568 7.5546 21.6344 7.87745C20.8928 8.62029 20.8775 9.83074 20.8469 12.2516C20.8163 14.6726 20.801 15.883 21.5237 16.6443C21.8378 16.9752 22.2379 17.1651 22.7836 17.2765" stroke="white" stroke-opacity="0.7" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>


  `,
  },
  "660294fc73af1e94826ee3c6": {
    name: "Info",
    active: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M22.2014 12.8057C22.2014 7.28281 17.7242 2.80566 12.2014 2.80566C6.67857 2.80566 2.20142 7.28281 2.20142 12.8057C2.20142 18.3285 6.67857 22.8057 12.2014 22.8057C17.7242 22.8057 22.2014 18.3285 22.2014 12.8057ZM12.0921 11.0806C12.3114 11.1101 12.5916 11.1859 12.8274 11.4217C13.0632 11.6575 13.1391 11.9377 13.1686 12.1571C13.1938 12.3443 13.1937 12.5661 13.1936 12.7712V12.7712L13.1936 12.8057V17.8057C13.1936 18.2199 12.8578 18.5557 12.4436 18.5557C12.0294 18.5557 11.6936 18.2199 11.6936 17.8057V12.8057C11.6936 12.7066 11.6936 12.6263 11.6924 12.5568C11.623 12.5557 11.5427 12.5557 11.4436 12.5557C11.0294 12.5557 10.6936 12.2199 10.6936 11.8057C10.6936 11.3915 11.0294 11.0557 11.4436 11.0557L11.478 11.0557C11.6832 11.0556 11.9049 11.0555 12.0921 11.0806ZM12.1934 7.80566C11.6411 7.80566 11.1934 8.25338 11.1934 8.80566C11.1934 9.35795 11.6411 9.80566 12.1934 9.80566H12.2024C12.7546 9.80566 13.2024 9.35795 13.2024 8.80566C13.2024 8.25338 12.7546 7.80566 12.2024 7.80566H12.1934Z" fill="url(#paint0_linear_93_16949)"/>
  <defs>
  <linearGradient id="paint0_linear_93_16949" x1="2.20142" y1="-5.45521" x2="26.2381" y2="-2.75118" gradientUnits="userSpaceOnUse">
  <stop stop-color="#E4D5B4"/>
  <stop offset="1" stop-color="#E0BA67"/>
  </linearGradient>
  </defs>
  </svg>
  `,
    inactive: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M22.2014 12.2085C22.2014 6.68565 17.7242 2.2085 12.2014 2.2085C6.67857 2.2085 2.20142 6.68565 2.20142 12.2085C2.20142 17.7313 6.67857 22.2085 12.2014 22.2085C17.7242 22.2085 22.2014 17.7313 22.2014 12.2085Z" stroke="white" stroke-opacity="0.7" stroke-width="1.5"/>
  <path d="M12.4436 17.2085V12.2085C12.4436 11.7371 12.4436 11.5014 12.2971 11.3549C12.1507 11.2085 11.915 11.2085 11.4436 11.2085" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M12.1934 8.2085H12.2024" stroke="white" stroke-opacity="0.7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
  `,
  },

  "6645fc5d71fe460dd1011e6a": {
    id: "6645fc5d71fe460dd1011e6a",
    name: "Inventory",
    active: `<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.31779 14.2364C4.2753 15 5.68353 15 8.5 15C8.77614 15 9 15.2239 9 15.5C9 18.3165 9 19.7247 9.76359 20.6822C9.92699 20.8871 10.1129 21.073 10.3178 21.2364C11.2753 22 12.6835 22 15.5 22C18.3165 22 19.7247 22 20.6822 21.2364C20.8871 21.073 21.073 20.8871 21.2364 20.6822C22 19.7247 22 18.3165 22 15.5C22 12.6835 22 11.2753 21.2364 10.3178C21.073 10.1129 20.8871 9.92699 20.6822 9.76359C19.7247 9 18.3165 9 15.5 9C15.2239 9 15 8.77614 15 8.5C15 5.68353 15 4.2753 14.2364 3.31779C14.073 3.11289 13.8871 2.92699 13.6822 2.76359C12.7247 2 11.3165 2 8.5 2C5.68353 2 4.2753 2 3.31779 2.76359C3.11289 2.92699 2.92699 3.11289 2.76359 3.31779C2 4.2753 2 5.68353 2 8.5C2 11.3165 2 12.7247 2.76359 13.6822C2.92699 13.8871 3.11289 14.073 3.31779 14.2364Z"/>
    </svg>
  `,
    inactive: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.31779 14.2364C4.2753 15 5.68353 15 8.5 15C8.77614 15 9 15.2239 9 15.5C9 18.3165 9 19.7247 9.76359 20.6822C9.92699 20.8871 10.1129 21.073 10.3178 21.2364C11.2753 22 12.6835 22 15.5 22C18.3165 22 19.7247 22 20.6822 21.2364C20.8871 21.073 21.073 20.8871 21.2364 20.6822C22 19.7247 22 18.3165 22 15.5C22 12.6835 22 11.2753 21.2364 10.3178C21.073 10.1129 20.8871 9.92699 20.6822 9.76359C19.7247 9 18.3165 9 15.5 9C15.2239 9 15 8.77614 15 8.5C15 5.68353 15 4.2753 14.2364 3.31779C14.073 3.11289 13.8871 2.92699 13.6822 2.76359C12.7247 2 11.3165 2 8.5 2C5.68353 2 4.2753 2 3.31779 2.76359C3.11289 2.92699 2.92699 3.11289 2.76359 3.31779C2 4.2753 2 5.68353 2 8.5C2 11.3165 2 12.7247 2.76359 13.6822C2.92699 13.8871 3.11289 14.073 3.31779 14.2364Z" stroke="white" stroke-width="1.5"/>
    </svg>
  `,
  },

  "661f9cbe67526186d8abcd20": {
    id: "661f9cbe67526186d8abcd20",
    name: "Unitplan",
    active: `<svg width="21" height="21" viewBox="0 0 21 21" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.52979 0.0831635L6.52979 3.03946C6.52979 3.45367 6.86557 3.78946 7.27979 3.78946C7.694 3.78946 8.02979 3.45367 8.02979 3.03946L8.02979 0.0471251C8.71384 0.0394592 9.46128 0.0394592 10.2798 0.0394592C14.9938 0.0394592 17.3509 0.0394592 18.8153 1.50393C19.8255 2.51414 20.1389 3.94908 20.2361 6.28946L16.2798 6.28946C15.8656 6.28946 15.5298 6.62525 15.5298 7.03946C15.5298 7.45367 15.8656 7.78946 16.2798 7.78946L20.2721 7.78946C20.2798 8.47351 20.2798 9.22096 20.2798 10.0395C20.2798 14.7535 20.2798 17.1106 18.8153 18.575C17.9989 19.3915 16.9049 19.7528 15.2798 19.9126L11.0298 20.0204C11.0401 17.682 12.9389 15.7895 15.2798 15.7895C15.694 15.7895 16.0298 15.4537 16.0298 15.0395C16.0298 14.6252 15.694 14.2895 15.2798 14.2895C12.1043 14.2895 9.52991 16.8637 9.52979 20.0392C8.99829 20.0387 8.49912 20.0371 8.02979 20.0318L8.02979 7.78946L12.2798 7.78946C12.694 7.78946 13.0298 7.45367 13.0298 7.03946C13.0298 6.62525 12.694 6.28946 12.2798 6.28946L7.27979 6.28946L0.323489 6.28946C0.420685 3.94908 0.734035 2.51414 1.74426 1.50393C2.75447 0.493709 4.18941 0.180359 6.52979 0.0831635ZM0.287451 7.78946C0.279785 8.47351 0.279785 9.22096 0.279785 10.0395C0.279785 14.7535 0.279785 17.1106 1.74426 18.575C2.75447 19.5852 4.18941 19.8986 6.52979 19.9958L6.52979 7.78946L0.287451 7.78946Z"/>
    </svg>
  `,
    inactive: `
    <svg width="25" height="26" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.0244 22.9992C7.31075 22.9397 4.95392 22.9099 3.50807 21.427C2.06222 19.9442 2.09199 17.5873 2.15153 12.8737C2.21107 8.16004 2.24084 5.80321 3.72369 4.35736C5.20653 2.91151 7.56336 2.94128 12.277 3.00082C16.9907 3.06036 19.3476 3.09013 20.7934 4.57298C22.2392 6.05582 22.2095 8.41265 22.1499 13.1263C22.0904 17.8399 22.0606 20.1969 20.5777 21.6426C19.7511 22.4488 18.6526 22.7962 17.0256 22.9355" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M12.0245 22.9992C12.0594 20.238 14.3261 18.0279 17.0872 18.0628" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M2.18945 9.87393L14.1885 10.0255" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M18.1882 10.076L22.1879 10.1265" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M9.02453 22.9613L9.18872 9.96234" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M9.23945 5.96268L9.27734 2.96292" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    </svg>
  `,
  },
};
export const statusColorMap = {
  0: { bg: "bg-black-100", border: "border-black" },
  "1BHK": { bg: "bg-unit-b1-fill", border: "border-fuchsia-300" },
  "2BHK": { bg: "bg-unit-b2-fill", border: "border-cyan-500" },
  "2.5BHK": { bg: "bg-unit-b2.5-fill", border: "border-yellow-400 " },
  "3BHK": { bg: "bg-unit-b3-fill", border: "border-blue-600" },
  "3.5BHK": { bg: "bg-unit-b3.5-fill", border: "border-red-600 " },
  "4BHK": { bg: "bg-unit-b4-fill", border: "border-orange-500" },
  "5BHK": { bg: "bg-unit-b5-fill", border: "border-red-600" },
  "6BHK": { bg: "bg-unit-b6-fill", border: "border-orange-500" },
  "7BHK": { bg: "bg-unit-b7-fill", border: "border-unit-b7-fill" },
  studio: { bg: "bg-unit-s1-fill", border: "border-green-300" },
  penthouse: { bg: "bg-unit-b6-fill", border: "border-orange-500" },
  townhouse: { bg: "bg-unit-b7-fill", border: "border-unit-b7-fill" },
  duplex: { bg: "bg-unit-s1-fill", border: "border-green-300" },
  suite: { bg: "bg-unit-b5-fill", border: "border-red-600" },
};
export const bedroomType = {
  'studio': "studio",
  '1BHK': "1",
  '2BHK': "2",
  '3BHK': "3",
  '4BHK': "4",
  'penthouse': "penthouse",
  'townhouse': "townhouse",
  'plot': "plot",
  'duplex': "duplex",
  'suite': "suite",
  '2.5BHK': "2.5",
  '3.5BHK': "3.5",
  '5BHK': '5',
  '6BHK': '6',
  '7BHK': '7',
};

export const languageSet = {
  ab: 'Abkhaz, Аҧсуа',
  ace: 'Acehnese, Bahasa Aceh',
  ach: 'Acholi, Lwoo',
  af: 'Afrikaans, Afrikaans',
  sq: 'Albanian, Shqip',
  alz: 'Alur, Alur',
  am: 'Amharic, አማርኛ',
  ar: 'Arabic, العربية',
  hy: 'Armenian, Հայերեն',
  as: 'Assamese, অসমীয়া',
  awa: 'Awadhi, अवधी',
  ay: 'Aymara, Aymara',
  az: 'Azerbaijani, Azərbaycan dili',
  ban: 'Balinese, ꦧꦱꦗꦶ',
  bm: 'Bambara, Bamanankan',
  ba: 'Bashkir, Башҡорт',
  eu: 'Basque, Euskara',
  btx: 'Batak Karo, ᯅᯗᯏᯔᯰᯕ',
  bts: 'Batak Simalungun, ᯅᯤᯔᯰᯔᯐᯔᯅ',
  bbc: 'Batak Toba, ᯅᯤᯔᯰᯧᯂᯪ',
  be: 'Belarusian, Беларуская',
  bem: 'Bemba, Icibemba',
  bn: 'Bengali, বাংলা',
  bew: 'Betawi, Betawi',
  bho: 'Bhojpuri, भोजपुरी',
  bik: 'Bikol, Bikol',
  bs: 'Bosnian, Bosanski',
  br: 'Breton, Brezhoneg',
  bg: 'Bulgarian, Български',
  bua: 'Buryat, Буряад',
  yue: 'Cantonese, 廣東話',
  ca: 'Catalan, Català',
  ceb: 'Cebuano, Sinugbuanong Binisaya',
  ny: 'Chichewa (Nyanja), Chichewa',
  'zh-CN': 'Chinese (Simplified), 简体中文',
  'zh-TW': 'Chinese (Traditional), 繁體中文',
  cv: 'Chuvash, Чӑваш',
  co: 'Corsican, Corsu',
  crh: 'Crimean Tatar, Qırım Tatarca',
  hr: 'Croatian, Hrvatski',
  cs: 'Czech, Čeština',
  da: 'Danish, Dansk',
  din: 'Dinka, Dinka',
  dv: 'Divehi, ދިވެހި',
  doi: 'Dogri, डोगरी',
  dov: 'Dombe, Dombé',
  nl: 'Dutch, Nederlands',
  dz: 'Dzongkha, རྫོང་ཁ',
  en: 'English, English',
  eo: 'Esperanto, Esperanto',
  et: 'Estonian, Eesti',
  ee: 'Ewe, Eʋegbe',
  fj: 'Fijian, Vosa Vakaviti',
  fil: 'Filipino (Tagalog), Filipino',
  fi: 'Finnish, Suomi',
  fr: 'French, Français',
  'fr-FR': 'French (French), Français (France)',
  'fr-CA': 'French (Canadian), Français (Canada)',
  fy: 'Frisian, Frysk',
  ff: 'Fulfulde, Pulaar',
  gaa: 'Ga, Ga',
  gl: 'Galician, Galego',
  lg: 'Ganda (Luganda), Luganda',
  ka: 'Georgian, ქართული',
  de: 'German, Deutsch',
  el: 'Greek, Ελληνικά',
  gn: 'Guarani, Aña ñe’ẽ',
  gu: 'Gujarati, ગુજરાતી',
  ht: 'Haitian Creole, Kreyòl Ayisyen',
  cnh: 'Hakha Chin, Hakha Zomi',
  ha: 'Hausa, هَوُسَ',
  haw: 'Hawaiian, ʻŌlelo Hawaiʻi',
  he: 'Hebrew, עברית',
  hil: 'Hiligaynon, Hiligaynon',
  hi: 'Hindi, हिंदी',
  hmn: 'Hmong, Hmoob',
  hu: 'Hungarian, Magyar',
  hrx: 'Hunsrik, Hunsrücker',
  is: 'Icelandic, Íslenska',
  ig: 'Igbo, Igbo',
  ilo: 'Iloko, Ilokano',
  id: 'Indonesian, Bahasa Indonesia',
  ga: 'Irish, Gaeilge',
  it: 'Italian, Italiano',
  ja: 'Japanese, 日本語',
  jw: 'Javanese, ꦧꦱꦗꦶ',
  kn: 'Kannada, ಕನ್ನಡ',
  pam: 'Kapampangan, Kapampangan',
  kk: 'Kazakh, Қазақ тілі',
  km: 'Khmer, ភាសាខ្មែរ',
  cgg: 'Kiga, Ekiga',
  rw: 'Kinyarwanda, Ikinyarwanda',
  ktu: 'Kituba, Kituba',
  gom: 'Konkani, कोंकणी',
  ko: 'Korean, 한국어',
  kri: 'Krio, Krio',
  ku: 'Kurdish (Kurmanji), Kurdî',
  ckb: 'Kurdish (Sorani), کوردی (سۆرانی)',
  ky: 'Kyrgyz, Кыргызча',
  lo: 'Lao, ລາວ',
  ltg: 'Latgalian, Latgaļu',
  la: 'Latin, Latine',
  lv: 'Latvian, Latviešu',
  lij: 'Ligurian, Ligure',
  li: 'Limburgan, Limburgs',
  ln: 'Lingala, Lingála',
  lt: 'Lithuanian, Lietuvių',
  lmo: 'Lombard, Lombard',
  luo: 'Luo, Dholuo',
  lb: 'Luxembourgish, Lëtzebuergesch',
  mk: 'Macedonian, Македонски',
  mai: 'Maithili, मैथिली',
  mak: 'Makassar, ᨆᨗᨕᨗᨒ',
  mg: 'Malagasy, Malagasy',
  ms: 'Malay, Bahasa Melayu',
  'ms-Arab': 'Malay (Jawi), Bahasa Melayu (Jawi)',
  ml: 'Malayalam, മലയാളം',
  mt: 'Maltese, Il-L-Iżbid',
  mi: 'Maori, Māori',
  mr: 'Marathi, मराठी',
  chm: 'Meadow Mari, Мари',
  'mni-Mtei': 'Meiteilon (Manipuri), মৈতৈলোন',
  min: 'Minang, Minangkabau',
  lus: 'Mizo, Mizo',
  mn: 'Mongolian, Монгол',
  my: 'Myanmar (Burmese), မြန်မာစာ',
  nr: 'Ndebele (South), isiNdebele',
  new: 'Nepalbhasa (Newari), नेपालभाषा',
  ne: 'Nepali, नेपाली',
  nso: 'Northern Sotho (Sepedi), Sesotho sa Leboa',
  no: 'Norwegian, Norsk',
  nus: 'Nuer, ᠀᠋᠃᠄᠁᠈',
  oc: 'Occitan, Occitan',
  or: 'Odia (Oriya), ଓଡିଆ',
  om: 'Oromo, Oromoo',
  pag: 'Pangasinan, Pangasinan',
  pap: 'Papiamento, Papiamentu',
  ps: 'Pashto, پښتو',
  fa: 'Persian, فارسی',
  pl: 'Polish, Polski',
  pt: 'Portuguese, Português',
  'pt-PT': 'Portuguese (Portugal), Português (Portugal)',
  'pt-BR': 'Portuguese (Brazil), Português (Brasil)',
  pa: 'Punjabi, ਪੰਜਾਬੀ',
  'pa-Arab': 'Punjabi (Shahmukhi), پنجابی',
  qu: 'Quechua, Runa Simi',
  rom: 'Romani, Romani',
  ro: 'Romanian, Română',
  rn: 'Rundi, Kirundi',
  ru: 'Russian, Русский',
  sm: 'Samoan, Gagana Samoa',
  sg: 'Sango, Sängö',
  sa: 'Sanskrit, संस्कृत',
  gd: 'Scots Gaelic, Gàidhlig',
  sr: 'Serbian, Српски',
  st: 'Sesotho, Sesotho',
  crs: 'Seychellois Creole, Kreol Seselwa',
  shn: 'Shan, ၽႃႇႁႅႊ',
  sn: 'Shona, ChiShona',
  scn: 'Sicilian, Sicilianu',
  szl: 'Silesian, Ślůnski',
  sd: 'Sindhi, سنڌي',
  si: 'Sinhala (Sinhalese), සිංහල',
  sk: 'Slovak, Slovenčina',
  sl: 'Slovenian, Slovenščina',
  so: 'Somali, Soomaali',
  es: 'Spanish, Español',
  su: 'Sundanese, Sunda',
  sw: 'Swahili, Kiswahili',
  ss: 'Swati, siSwati',
  sv: 'Swedish, Svenska',
  tg: 'Tajik, Тоҷикӣ',
  ta: 'Tamil, தமிழ்',
  tt: 'Tatar, Татарча',
  te: 'Telugu, తెలుగు',
  tet: 'Tetum, Tetun',
  th: 'Thai, ไทย',
  ti: 'Tigrinya, ትግርኛ',
  ts: 'Tsonga,Xitsonga',
  tn: 'Tswana, Setswana',
  tr: 'Turkish, Türkçe',
  tk: 'Turkmen, Türkmen',
  ak: 'Twi (Akan), Twi',
  uk: 'Ukrainian, Українська',
  ur: 'Urdu, اردو',
  ug: 'Uyghur, ئۇيغۇرچە',
  uz: 'Uzbek, O‘zbek',
  vi: 'Vietnamese, Tiếng Việt',
  cy: 'Welsh, Cymraeg',
  xh: 'Xhosa, isiXhosa',
  yi: 'Yiddish, ייִדיש',
  yo: 'Yoruba, Yorùbá',
  yua: 'Yucatec Maya, Maaya Tʼàan',
  zu: 'Zulu, isiZulu',

};
export const countryList = {
  AED: "AE",
  AFN: "AF",
  XCD: "AG",
  ALL: "AL",
  AMD: "AM",
  ANG: "AN",
  AOA: "AO",
  AQD: "AQ",
  ARS: "AR",
  AUD: "AU",
  AZN: "AZ",
  BAM: "BA",
  BBD: "BB",
  BDT: "BD",
  XOF: "BE",
  BGN: "BG",
  BHD: "BH",
  BIF: "BI",
  BMD: "BM",
  BND: "BN",
  BOB: "BO",
  BRL: "BR",
  BSD: "BS",
  NOK: "BV",
  BWP: "BW",
  BYR: "BY",
  BZD: "BZ",
  CAD: "CA",
  CDF: "CD",
  XAF: "CF",
  CHF: "CH",
  CLP: "CL",
  CNY: "CN",
  COP: "CO",
  CRC: "CR",
  CUP: "CU",
  CVE: "CV",
  CYP: "CY",
  CZK: "CZ",
  DJF: "DJ",
  DKK: "DK",
  DOP: "DO",
  DZD: "DZ",
  ECS: "EC",
  EEK: "EE",
  EGP: "EG",
  ETB: "ET",
  EUR: "FR",
  FJD: "FJ",
  FKP: "FK",
  GBP: "GB",
  GEL: "GE",
  GGP: "GG",
  GHS: "GH",
  GIP: "GI",
  GMD: "GM",
  GNF: "GN",
  GTQ: "GT",
  GYD: "GY",
  HKD: "HK",
  HNL: "HN",
  HRK: "HR",
  HTG: "HT",
  HUF: "HU",
  IDR: "ID",
  ILS: "IL",
  INR: "IN",
  IQD: "IQ",
  IRR: "IR",
  ISK: "IS",
  JMD: "JM",
  JOD: "JO",
  JPY: "JP",
  KES: "KE",
  KGS: "KG",
  KHR: "KH",
  KMF: "KM",
  KPW: "KP",
  KRW: "KR",
  KWD: "KW",
  KYD: "KY",
  KZT: "KZ",
  LAK: "LA",
  LBP: "LB",
  LKR: "LK",
  LRD: "LR",
  LSL: "LS",
  LTL: "LT",
  LVL: "LV",
  LYD: "LY",
  MAD: "MA",
  MDL: "MD",
  MGA: "MG",
  MKD: "MK",
  MMK: "MM",
  MNT: "MN",
  MOP: "MO",
  MRO: "MR",
  MTL: "MT",
  MUR: "MU",
  MVR: "MV",
  MWK: "MW",
  MXN: "MX",
  MYR: "MY",
  MZN: "MZ",
  NAD: "NA",
  XPF: "NC",
  NGN: "NG",
  NIO: "NI",
  NPR: "NP",
  NZD: "NZ",
  OMR: "OM",
  PAB: "PA",
  PEN: "PE",
  PGK: "PG",
  PHP: "PH",
  PKR: "PK",
  PLN: "PL",
  PYG: "PY",
  QAR: "QA",
  RON: "RO",
  RSD: "RS",
  RUB: "RU",
  RWF: "RW",
  SAR: "SA",
  SBD: "SB",
  SCR: "SC",
  SDG: "SD",
  SEK: "SE",
  SGD: "SG",
  SKK: "SK",
  SLL: "SL",
  SOS: "SO",
  SRD: "SR",
  STD: "ST",
  SVC: "SV",
  SYP: "SY",
  SZL: "SZ",
  THB: "TH",
  TJS: "TJ",
  TMT: "TM",
  TND: "TN",
  TOP: "TO",
  TRY: "TR",
  TTD: "TT",
  TWD: "TW",
  TZS: "TZ",
  UAH: "UA",
  UGX: "UG",
  USD: "US",
  UYU: "UY",
  UZS: "UZ",
  VEF: "VE",
  VND: "VN",
  VUV: "VU",
  YER: "YE",
  ZAR: "ZA",
  ZMK: "ZM",
  ZWD: "ZW",
};

export const exchangeRates = [
  { currency: "USD", rate: 0.2723 },
  { currency: "AED", rate: 1.0 },
  { currency: "EUR", rate: 0.2597 },
  { currency: "INR", rate: 23.781 },
  { currency: "GBP", rate: 0.2145 },
];
export const availUnitOrgs= ["yRnIS3"];

export const ancesterDomainToRestrict = [
  "login.damaclabs.com",
  "pixelstreaming.damaclabs.com",
];

export const standaloneDomainToRestrict = [
  "propvr-ui-library-env-k5aul2eu6q-uc.a.run.app",
  "http://localhost",
  ".run.app",
];
