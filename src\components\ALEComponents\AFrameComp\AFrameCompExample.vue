<template>
  <div class="aframe-example">
    <h2>AFrame Component Example</h2>
    
    <!-- Example usage with the new object format -->
    <div class="scene-container">
      <AFrameComp
        :sceneData="exampleSceneData"
        @progress-data="handleProgress"
        @hotspot-click="handleHotspotClick"
      />
    </div>
    
    <!-- Progress indicator -->
    <div v-if="loadingProgress !== false" class="progress-bar">
      Loading: {{ loadingProgress }}%
    </div>
    
    <!-- Hotspot click feedback -->
    <div v-if="lastClickedHotspot" class="hotspot-info">
      Last clicked hotspot: {{ lastClickedHotspot.text }} 
      (Destination: {{ lastClickedHotspot.destination_img_id }})
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AFrameComp from './AframeComp.vue';

const loadingProgress = ref(0);
const lastClickedHotspot = ref(null);

// Example scene data object (same format as provided by user)
const exampleSceneData = ref({
  "_id": "67455fd5e83433098cba1d1d",
  "project_id": "673db6162fa83b3e755e5d14",
  "name": "Saloooon",
  "category": "asada",
  "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F7T1pfE%2Fprojects%2F673db6162fa83b3e755e5d14%2Famenities%2F67455fd5e83433098cba1d1d%2FSaloon_thumb_1732599765630.webp?alt=media",
  "media_type": "360_image",
  "file": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F7T1pfE%2Fprojects%2F673db6162fa83b3e755e5d14%2Famenities%2F67455fd5e83433098cba1d1d%2FSaloon_1732599765629.jpg?alt=media",
  "media": [],
  "__v": 0,
  "modified": "2025-07-29T10:36:25.284Z",
  "order": 1,
  "links": {
    "6888a4481f308d97e67fa6af": {
      "_id": "6888a4481f308d97e67fa6af",
      "position": {
        "x": "4.7700446372678424",
        "y": "0.7536656392312024",
        "z": "1.2956419935516132"
      },
      "text": "Pool",
      "destination_img_id": "67455faee83433098cba1d02"
    }
  },
  "rotation": "-4.47 -93.85 0"
});

const handleProgress = (progress) => {
  loadingProgress.value = progress;
};

const handleHotspotClick = (linkData) => {
  lastClickedHotspot.value = linkData;
  console.log('Hotspot clicked:', linkData);
  
  // Here you can handle navigation to the destination scene
  // For example, emit an event to parent component or use router
  // this.$emit('navigate-to-scene', linkData.destination_img_id);
};
</script>

<style scoped>
.aframe-example {
  width: 100%;
  height: 100vh;
  position: relative;
}

.scene-container {
  width: 100%;
  height: 80vh;
  position: relative;
}

.progress-bar {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 5px;
  z-index: 10;
}

.hotspot-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 5px;
  z-index: 10;
}
</style>
