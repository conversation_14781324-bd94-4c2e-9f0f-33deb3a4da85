<script setup>
import globeComp  from '../components/globeComp.vue';
import { defineProps, ref, defineEmits, defineExpose} from 'vue';
import router from '../router';
import { creationToolStore } from '../store';
import breadCrumb from '../components/ALEComponents/BreadCrumb/BreadCrumb.vue';
const Store = creationToolStore();
const globeRef = ref(null);
const props = defineProps({'sceneId': {type: String, default: ""}, 'scenes': {
  type: Object,
  default () {
    return {};
  },
}});
const scene_data = ref(props.scenes[props.sceneId]);
Store.$patch({breadCrumb: []});
if (Store.breadCrumb.length!==0){
  Store.breadCrumb.push({_id: props.sceneId, name: scene_data.value.sceneData.name});
} else {
  Store.breadCrumb.push({_id: props.sceneId, name: scene_data.value.sceneData.name});
}
const emit = defineEmits(['unMount', 'hideLoader', 'showInfoCard', 'globeAnimationEnd']);
const gData = ref(false);
const hideImage = ref(true);
const checkAssetloaded = ref({
  image: false,
  texture: false,
});

async function readSVGFromUrl (){
  gData.value = scene_data.value.sceneData.coordinates;
}

readSVGFromUrl();

function emitClickData (id){
  emit('unMount', id);
  router.push({name: 'masterScene', params: {sceneId: id}});
}
function disableImage (){
  if (checkAssetloaded.value.texture && checkAssetloaded.value.image){
    hideImage.value = false;
    globeRef.value.startInitialAnimation();
  }
}
function imageloaded (){
  emit('hideLoader');
  checkAssetloaded.value.image = true;
  disableImage();
}

function startAnimation (){
  globeRef.value.startAnimate();
}

function animationEnd (){

  emit("showInfoCard", "Image");
}

function globeAnimationEnd (){
  emit("globeAnimationEnd");
}

function textureLoaded (){
  checkAssetloaded.value.texture = true;
  disableImage();
}

defineExpose({
  startAnimation,
});
</script>
<template>
  <div style="height:100%;width:100%;overflow: hidden;position: relative;">
    <div
      v-if="hideImage"
      class="absolute top-0 left-0 z-[5] h-full w-full overflow-hidden"
    >
      <img
        src="/assets/Scene1/initialBg.jpg"
        class="h-full w-full object-cover"
        @load="imageloaded"
      >
    </div>
    <div
      v-if="hideImage"
      class="absolute top-1/2 -translate-y-1/2 left-8 z-[5]"
    >
      <div class="w-60 md:w-72 lg:w-96 ">
        <!-- <img src="/assets/Icons/KAFDLogo.svg" alt="KAFD" class="w-auto min-w-[50%] h-full"/> -->
        <div class="w-full mt-8 bg-bgcolor overflow-hidden h-1 relative">
          <div class="bg-button-active absolute top-0 left-0 w-1/2 h-full progressBar" />
        </div>
      </div>
    </div>
    <div
      v-if="!showAnimation &&!showLoader && Store.breadCrumb.length>0"
      class="absolute w-full  top-0 z-[2]"
    >
      <!-- <DamacHeader
        :property-data="Store.unitData"
        @open-favourites="openFavourites"
      /> -->
      <div class="left-0 top-[0.45rem] z-[2]  w-full ">
        <breadCrumb
          :logo="Store.organization_thumbnail"
          :bread-crumb-data="Store.breadCrumb"
          @move-to-scene="moveToLocation"
          @to-full-screen="onClickButton"
        />
      </div>
    </div>
    <globeComp
      ref="globeRef"
      :data="gData"
      class="h-full w-full overflow-hidden"
      @emit-click-data="emitClickData"
      @initial-animation-end="animationEnd"
      @selected-animation-end="globeAnimationEnd"
      @all-texture-loaded="textureLoaded"
    />
  </div>
</template>

<style scoped>
.bgcolor{
  color: '#28334A'
}
.progressBar{
    animation-duration: 1.5s;
    animation-iteration-count: infinite;
    animation-name: progress-bar;
}

@keyframes progress-bar {
    from {
        left: -50%;
    }
    to {
        left: 100%;
    }
}
</style>
