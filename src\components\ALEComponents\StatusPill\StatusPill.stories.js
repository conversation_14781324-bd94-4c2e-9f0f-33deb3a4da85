import StatusPill from './StatusPill.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/StatusPill',
  component: StatusPill,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    availabilityStatus: "Sold",
    availableUnits: 8,
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-6595&mode=dev",
    },
  },

};
