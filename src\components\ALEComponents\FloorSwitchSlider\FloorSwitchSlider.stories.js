import FloorSwitchSlider from './FloorSwitchSlider.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/FloorSwitchSlider',
  component: FloorSwitchSlider,
  tags: ['autodocs'],
};

export const Primary = {
  args: {
    numberOfFloors: {

      "1": {
        "floor_id": 1,
        "floor_name": "Floor 1",
        "units": [],
      },
      "2": {
        "floor_id": 2,
        "floor_name": "Floor 2",
        "units": [],
      },
      "3": {
        "floor_id": 3,
        "floor_name": "Floor 3",
        "units": [],
      },
      "4": {
        "floor_id": 4,
        "floor_name": "Floor 4",
        "units": [
          "65a12ec646c8b45224e3a341",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
      "5": {
        "floor_id": 5,
        "floor_name": "Floor 5",
        "units": [
          "65a12ec646c8b45224e3a355",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
      "6": {
        "floor_id": 6,
        "floor_name": "Floor 6",
        "units": [
          "65a12ec646c8b45224e3a361",
        ],
        "project_id": "659f886821bd11d5d9218378",
      },
    },
    defaultId: "1",
    perSlideView: "5",
    enabledFloors: [4, 5],
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/3ScwDHZPtk2FRiRLsSU2T2/ALE-V.2?type=design&node-id=1530-1572&mode=dev",
    },
  },
};
