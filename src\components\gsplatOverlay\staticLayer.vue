<script setup>
import { ref, defineProps, onMounted, defineEmits} from 'vue';
import { cdn } from '../../helpers/helper';
import { layer_icons } from '../../config/masterdata';
const emit = defineEmits(['appendObject']);
const props = defineProps({layer: {
  type: Object,
  default () {
    return {};
  },
}});
const staticLayer = ref(null);
onMounted(async () => {
  const url =  cdn(props.layer.svg_url);
  var svgString;
  if (props.layer.svg_url !== undefined && props.layer.svg_url !== ""){
    const requestOptions = {
      method: "GET",
      redirect: "follow",
    };
    const response = await fetch(url, requestOptions);
    svgString = await response.text();
  } else {
    svgString = layer_icons[props.layer.type][props.layer.name].icon;
  }
  const div = document.createElement('div');
  div.id=props.layer.layer_id;
  div.innerHTML=svgString;
  div.setAttribute('clickaway', "true");
  div.setAttribute('class', "staticLayer");
  staticLayer.value=div;
  document.body.appendChild(div);
  emit('appendObject', staticLayer.value);
});
</script>
