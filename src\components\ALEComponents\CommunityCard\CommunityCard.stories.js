import CommunityCard from './CommunityCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/CommunityCard',
  component: CommunityCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    communityName: "Lanvender",
    numberOfTowers: 12,
    numberOfUnits: 2000,
    numberOfFloors: 23,
    numberOfAmenities: 16,
    measurementType: 'sqft',
    area: "2500 - 3500",
    thumbnail: "https://imgs.search.brave.com/j620SE33GRlp0kR43xgHHt9hz9F0qqTPWzUPgZ9X4Z4/rs:fit:860:0:0/g:ce/aHR0cHM6Ly9zdDMu/ZGVwb3NpdHBob3Rv/cy5jb20vMTAwMTE0/Ni8xNzk5Mi9pLzYw/MC9kZXBvc2l0cGhv/dG9zXzE3OTkyMjgw/OC1zdG9jay1waG90/by1jb2xvdXJmdWwt/aG9tZXMtY2VsbGUt/Z2VybWFueS5qcGc",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1071-656&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
