<script setup>
import { ref, computed, defineProps, defineEmits, nextTick } from 'vue';
import InventoryFloorPlateCard from '../InventoryFloorPlateCard/InventoryFloorPlateCard.vue';
// Import BackTopBar from '../BackBar/BackTopBar.vue';
const showAllUnits = ref(false);
const searchedUnit = ref('');
const isClicked=ref(false);
const search=ref(null);

const props =  defineProps({
  data: {
    type: Object,
    default () {
      return {};
    },
  },
  buildingData: {
    type: Object,
    default () {
      return {};
    },
  },
  favUnits: {
    type: Array,
    default: () => [],
  },
  hideStatus: {type: String, default: "false"},
});
const emit = defineEmits(['gotoUnit', 'toggleButton']);

const displayAllUnits = () => {
  isClicked.value = true;
  showAllUnits.value= true;
  if (showAllUnits.value){
    nextTick(() => {
      search.value.focus();
    });
  }
};
const totalUnits = Object.keys(props.data).length;

const filteredUnits = computed(() => {
  if (!searchedUnit.value.trim()) {
    return props.data;
  }

  const searchTerm = searchedUnit.value.trim().toLowerCase();
  return Object.values(props.data).filter((unit) => {
    for (const key in unit) {
      if (Object.prototype.hasOwnProperty.call(unit, key)) {
        if (unit[key].toString().toLowerCase().includes(searchTerm)) {
          return unit;
        }
      }
    }
    return false;
  });
});
const emitEvent = (unit_id, unitplan_id) => {
  emit("gotoUnit", unit_id, unitplan_id);
};
</script>

<template>
  <!-- <BackTopBar
    v-if="isClicked"
    @go-back="isClicked=false;showAllUnits=false;searchedUnit=''"
  /> -->
  <div
    class="absolute top-10"
    :class="isClicked ? 'bg-blend-luminosity backdrop-blur-[50px] bg-SearchComponentOverlay' : ''"
  >
    <div class="flex flex-col gap-[35px] p-5 w-full">
      <div
        class="flex bg-SearchComponentBg backdrop-blur-[10px] items-center"
        :class="isClicked ? 'pl-4 pr-6 py-3 w-fit h-10 rounded-[50px]' : 'p-3 w-fit h-fit rounded-[50%] border border-white'"
      >
        <input
          ref="search"
          v-model="searchedUnit"
          type="text"
          placeholder="Search for Units"
          class="text-white text-base not-italic font-normal leading-[22px] capitalize bg-transparent outline-none md:w-full"
          :class="isClicked ? 'block' : 'hidden'"
          @keyup.enter="displayAllUnits()"
        >
        <div
          class="hover:cursor-pointer w-5 h-5 relative flex-col items-center flex"
          @click="displayAllUnits()"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.33333 14.1667C11.555 14.1667 14.1667 11.555 14.1667 8.33333C14.1667 5.11167 11.555 2.5 8.33333 2.5C5.11167 2.5 2.5 5.11167 2.5 8.33333C2.5 11.555 5.11167 14.1667 8.33333 14.1667Z"
              stroke="#D8DBDF"
              stroke-width="1.4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12.5 12.5L17.5 17.5"
              stroke="#D8DBDF"
              stroke-width="1.4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
      <div v-if="showAllUnits">
        <p class="text-white text-base not-italic font-normal leading-[normal]">
          Showing <span v-if="filteredUnits">{{ filteredUnits.length || totalUnits }}</span>
          Units
        </p>
        <div class="body w-full overflow-y-scroll no-scrollbar flex flex-col mt-8 gap-3 items-center">
          <div
            v-for="(value, _id) in filteredUnits"
            :key="_id"
            class="flex flex-col items-center"
          >
            <InventoryFloorPlateCard
              :name="value.name"
              :status="value.status"
              :tower="buildingData[value.building_id]?buildingData[value.building_id].name:''"
              :floor="value.floor_id"
              :type="value.type"
              :price="value.price"
              :currency="value.currency"
              :measurement="value.measurement"
              :measurementType="value.measurement_type"
              :bedrooms="value.bedroom"
              :favUnits="favUnits"
              :unitId="value._id"
              :hideStatus="hideStatus"
              @emit-event="emitEvent(value._id,value.unitplan_id)"
              @toggle-button="emit('toggleButton',value)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.no-scrollbar::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
}

.no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
}

.body{
    min-height: calc(100vh - 170px) ;
   max-height: calc(100vh - 170px);
}
</style>
