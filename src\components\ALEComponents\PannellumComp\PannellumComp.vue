<script setup>
import { onMounted, ref } from 'vue';
import { defineProps } from 'vue';
import OverflowSlider from "../OverflowSlider/OverflowSlider.vue";

const props = defineProps({
  tourData: {
    type: Object,
    default () {
      return {};
    },
  },
});
const currentSceneId = ref(Object.keys(props.tourData.scenes)[0]);
const activeScene = ref(null);

const config = {
  autoLoad: true,
  compass: false,
  scenes: props.tourData.scenes,
};

const changeScene = (sceneId) => {
  activeScene.value = sceneId;
  const panoramaContainer = document.getElementById('panorama');
  panoramaContainer.innerHTML = '';
  currentSceneId.value = sceneId;
  const viewer = window.pannellum.viewer('panorama', config);
  viewer.loadScene(sceneId);
};

onMounted(() => {
  activeScene.value = Object.keys(props.tourData.scenes)[0];
  const viewer = window.pannellum.viewer('panorama', config);
  viewer.loadScene(currentSceneId.value);
});
</script>

<template class="p-[0px] m-0 ">
  <div class="p-[0px] m-0 relative">
    <div id="panorama" />
    <OverflowSlider
      arrows
      inline-arrow-styles="width:10px;height:10px;"
      slides-per-view="auto"
      space-between="30"
      class="scene-boxes w-full md:w-auto "
    >
      <template #options>
        <swiper-slide
          v-for="(scene, key) in props.tourData.scenes"
          :key="key"
          class="slider"
        >
          <div @click="changeScene(scene._id)">
            <div
              :class="{ 'active-slide': scene._id === activeScene }"
              class="scene-title"
            >
              {{ scene.title }}
            </div>
          </div>
        </swiper-slide>
      </template>
    </OverflowSlider>
  </div>
</template>

<style scoped>
.slider {
    width: 100px;
    display: flex;
    justify-content: space-between;
}

.slider1 {
    width: 100px;
}
.active-slide {
  background-color: white;
  color: black;
}

#panorama {
    margin: 0px !important;
    padding: 0px !important;
    width: 100%;
    height: 100vh;
    position: relative;
}

.scene-boxes {
    position: absolute;
    bottom: 3%;
    left: 0;
    z-index: 99999999;
    padding: 0px 10px;
}

.scene-title {
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    display: inline-block;
    padding: 2px;
    color: #fff;
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(19px);
    -webkit-backdrop-filter: blur(19px);
    width: fit-content;
    padding: 6px 25px;
    border-radius: 15px;
}

.scene-title:hover,.scene-title.active-slide {
    background-color: white;
    color: black;
}
</style>
