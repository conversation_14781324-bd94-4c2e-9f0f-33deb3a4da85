<script setup>
import { addOrdinalSuffix } from "../../../helpers/helper";
import { defineEmits } from "vue";

defineProps({
  unitNo: {type: Number, default: 0},
  availabilityStatus: Boolean,
  isFavourite: Boolean,
  area: {type: Number, default: 0},
  floor: {type: Number, default: 0},
  bed: {type: Number, default: 0},
  thumbnail: {type: String, default: ""},
});

defineEmits(["toggleFavourite", "enterVr"]);

</script>

<template>
  <div
    class="card-shadow flex p-2 rounded-lg bg-white w-full sm:w-[500px] gap-2 sm:gap-3 h-40 sm:h-48 text-xs sm:text-[16px] mb-3"
  >
    <div class="w-3/5 text-center rounded-lg">
      <img
        :src="thumbnail"
        alt=""
        class="h-full m-auto rounded-lg w-full"
      >
    </div>
    <div class="w-2/5 flex flex-col gap-[3px] sm:gap-2 h-full">
      <div>
        <div class="flex justify-between">
          <p class="font-semibold my-auto">
            <span class="text-[#6B7280]">Unit No</span>
            {{ unitNo }}
          </p>
          <div>
            <button
              class="bg-black p-1 sm:p-2 rounded-[3px] m-0 mt-[1px]"
              @click="$emit('toggleFavourite', !isFavourite)"
            >
              <svg
                v-if="isFavourite"
                data-v-8e1253e5=""
                viewBox="0 0 20 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="w-3 h-3"
              >
                <path
                  id="Star 3"
                  data-v-8e1253e5=""
                  d="M9.04894 0.927052C9.3483 0.00574112 10.6517 0.00573993 10.9511 0.927051L12.4697 5.60081C12.6035 6.01284 12.9875 6.2918 13.4207 6.2918H18.335C19.3037 6.2918 19.7065 7.53141 18.9228 8.10081L14.947 10.9894C14.5966 11.244 14.4499 11.6954 14.5838 12.1074L16.1024 16.7812C16.4017 17.7025 15.3472 18.4686 14.5635 17.8992L10.5878 15.0106C10.2373 14.756 9.7627 14.756 9.41221 15.0106L5.43648 17.8992C4.65276 18.4686 3.59828 17.7025 3.89763 16.7812L5.41623 12.1074C5.55011 11.6954 5.40345 11.244 5.05296 10.9894L1.07722 8.10081C0.293507 7.53141 0.696283 6.2918 1.66501 6.2918H6.57929C7.01252 6.2918 7.39647 6.01284 7.53035 5.60081L9.04894 0.927052Z"
                  fill="none"
                  stroke="white"
                />
              </svg>
              <svg
                v-else
                viewBox="0 0 14 13"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="w-3 h-3"
              >
                <path
                  d="M6.52447 1.08156C6.67415 0.620904 7.32585 0.620906 7.47553 1.08156L8.32058 3.68237C8.52139 4.30041 9.09733 4.71885 9.74717 4.71885H12.4818C12.9662 4.71885 13.1676 5.33866 12.7757 5.62336L10.5633 7.23075C10.0376 7.61271 9.81762 8.28976 10.0184 8.9078L10.8635 11.5086C11.0132 11.9693 10.4859 12.3523 10.0941 12.0676L7.88168 10.4602C7.35595 10.0783 6.64405 10.0783 6.11832 10.4602L3.90594 12.0676C3.51408 12.3523 2.98684 11.9693 3.13652 11.5086L3.98157 8.9078C4.18238 8.28976 3.9624 7.61271 3.43667 7.23075L1.22428 5.62336C0.832426 5.33865 1.03382 4.71885 1.51818 4.71885H4.25283C4.90267 4.71885 5.4786 4.30041 5.67942 3.68237L6.52447 1.08156Z"
                  fill="white"
                  stroke="white"
                />
              </svg>
            </button>
          </div>
        </div>
        <div>
          <p
            class="font-semibold"
            :class="
              availabilityStatus
                ? 'text-[#2AA100]'
                : 'text-red-600'
            "
          >
            {{
              availabilityStatus
                ? "Available"
                : "Sold"
            }}
          </p>
        </div>
      </div>
      <div class="text-[#6B7280] flex align-middle gap-2">
        <svg
          width="20"
          height="20"
          class="w-4 h-5"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M3.45491 5.06728C3.16158 5.3409 2.70198 5.32492 2.42837 5.03159L0.951924 3.44878C0.691039 3.1691 0.691747 2.73502 0.953544 2.4562L2.42999 0.883702C2.70456 0.591268 3.16421 0.576788 3.45664 0.85136C3.74908 1.12593 3.76356 1.58558 3.48898 1.87802L3.12474 2.26596H11.3346L10.973 1.87833C10.6994 1.585 10.7154 1.1254 11.0087 0.851782C11.302 0.578166 11.7616 0.594146 12.0353 0.887475L13.5117 2.47029C13.7726 2.74997 13.7719 3.18404 13.5101 3.46287L12.0336 5.03536C11.7591 5.3278 11.2994 5.34228 11.007 5.06771C10.7145 4.79313 10.7001 4.33348 10.9746 4.04105L11.3367 3.65543H3.13119L3.4906 4.04074C3.76422 4.33407 3.74824 4.79367 3.45491 5.06728ZM12.078 7.75002H1.85742C1.78839 7.75002 1.73242 7.80599 1.73242 7.87503V18.0788C1.73242 18.1478 1.78839 18.2038 1.85742 18.2038H12.078C12.1471 18.2038 12.203 18.1478 12.203 18.0788V7.87502C12.203 7.80599 12.1471 7.75002 12.078 7.75002ZM1.85742 6.75002C1.2361 6.75002 0.732422 7.2537 0.732422 7.87503V18.0788C0.732422 18.7001 1.2361 19.2038 1.85742 19.2038H12.078C12.6993 19.2038 13.203 18.7001 13.203 18.0788V7.87502C13.203 7.2537 12.6993 6.75002 12.078 6.75002H1.85742ZM14.9164 7.95812C14.6231 8.23174 14.6071 8.69134 14.8807 8.98467C15.1544 9.278 15.614 9.29398 15.9073 9.02036L16.2961 8.65771V17.2901L15.9104 16.928C15.618 16.6534 15.1584 16.6679 14.8838 16.9603C14.6092 17.2528 14.6237 17.7124 14.9161 17.987L16.4886 19.4634C16.7675 19.7252 17.2015 19.726 17.4812 19.4651L19.064 17.9886C19.3574 17.715 19.3733 17.2554 19.0997 16.9621C18.8261 16.6687 18.3665 16.6528 18.0732 16.9264L17.6855 17.288V8.65776L18.07 9.01874C18.3624 9.29331 18.8221 9.27883 19.0967 8.9864C19.3712 8.69397 19.3568 8.23432 19.0643 7.95974L17.4918 6.4833C17.213 6.2215 16.7789 6.22079 16.4992 6.48168L14.9164 7.95812Z"
            fill="#6B7280"
          />
        </svg>
        <p class="my-auto">
          {{ area }} Sqft
        </p>
      </div>
      <div class="text-[#6B7280] flex align-middle gap-2">
        <svg
          width="20"
          height="16"
          viewBox="0 0 20 18"
          class="w-4 h-5"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask
            id="path-1-inside-1_312_43596"
            fill="white"
          >
            <path
              d="M0 12.6318C0 12.1532 0.38802 11.7651 0.866667 11.7651H19.9998V17.6474H0.866667C0.38802 17.6474 0 17.2594 0 16.7808V12.6318Z"
            />
          </mask>
          <path
            d="M0 12.6318C0 12.1532 0.38802 11.7651 0.866667 11.7651H19.9998V17.6474H0.866667C0.38802 17.6474 0 17.2594 0 16.7808V12.6318Z"
            stroke="#6B7280"
            stroke-width="2"
            mask="url(#path-1-inside-1_312_43596)"
          />
          <mask
            id="path-2-inside-2_312_43596"
            fill="white"
          >
            <path
              d="M4.70508 6.7485C4.70508 6.26986 5.0931 5.88184 5.57174 5.88184H19.9991V11.7641H4.70508V6.7485Z"
            />
          </mask>
          <path
            d="M4.70508 6.7485C4.70508 6.26986 5.0931 5.88184 5.57174 5.88184H19.9991V11.7641H4.70508V6.7485Z"
            stroke="#6B7280"
            stroke-width="2"
            mask="url(#path-2-inside-2_312_43596)"
          />
          <mask
            id="path-3-inside-3_312_43596"
            fill="white"
          >
            <path
              d="M9.41211 0.866668C9.41211 0.388021 9.80013 0 10.2788 0H20.0003V5.8823H9.41211V0.866668Z"
            />
          </mask>
          <path
            d="M9.41211 0.866668C9.41211 0.388021 9.80013 0 10.2788 0H20.0003V5.8823H9.41211V0.866668Z"
            stroke="#6B7280"
            stroke-width="2"
            mask="url(#path-3-inside-3_312_43596)"
          />
        </svg>
        <p class="my-auto">
          {{ addOrdinalSuffix(floor) }}
        </p>
      </div>
      <div class="text-[#6B7280] flex align-middle gap-2">
        <svg
          width="20"
          height="18"
          viewBox="0 0 24 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="w-4 h-5"
        >
          <path
            d="M19.5 3.75H2.25V1.5C2.25 1.30109 2.17098 1.11032 2.03033 0.96967C1.88968 0.829018 1.69891 0.75 1.5 0.75C1.30109 0.75 1.11032 0.829018 0.96967 0.96967C0.829018 1.11032 0.75 1.30109 0.75 1.5V16.5C0.75 16.6989 0.829018 16.8897 0.96967 17.0303C1.11032 17.171 1.30109 17.25 1.5 17.25C1.69891 17.25 1.88968 17.171 2.03033 17.0303C2.17098 16.8897 2.25 16.6989 2.25 16.5V13.5H21.75V16.5C21.75 16.6989 21.829 16.8897 21.9697 17.0303C22.1103 17.171 22.3011 17.25 22.5 17.25C22.6989 17.25 22.8897 17.171 23.0303 17.0303C23.171 16.8897 23.25 16.6989 23.25 16.5V7.5C23.25 6.50544 22.8549 5.55161 22.1516 4.84835C21.4484 4.14509 20.4946 3.75 19.5 3.75ZM2.25 5.25H9V12H2.25V5.25ZM10.5 12V5.25H19.5C20.0967 5.25 20.669 5.48705 21.091 5.90901C21.5129 6.33097 21.75 6.90326 21.75 7.5V12H10.5Z"
            fill="#6B7280"
          />
        </svg>
        <p class="my-auto">
          {{ bed }}
        </p>
      </div>
      <div class="w-full mt-auto">
        <button
          class="flex justify-center align-middle gap-2 self-end w-full border border-black rounded-md py-[7px] sm:py-2 bg-black text-white"
          @click="$emit('enterVr')"
        >
          <svg

            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="border border-black h-5 w-5"
          >
            <path
              d="M15.75 3.375H2.25C1.95163 3.375 1.66548 3.49353 1.4545 3.7045C1.24353 3.91548 1.125 4.20163 1.125 4.5V13.5C1.125 13.7984 1.24353 14.0845 1.4545 14.2955C1.66548 14.5065 1.95163 14.625 2.25 14.625H6.51727C6.66506 14.6255 6.81147 14.5966 6.94799 14.54C7.08451 14.4833 7.20841 14.4002 7.3125 14.2952L8.80102 12.8067C8.82714 12.7806 8.85815 12.7598 8.8923 12.7457C8.92644 12.7315 8.96304 12.7242 9 12.7242C9.03696 12.7242 9.07356 12.7315 9.1077 12.7457C9.14185 12.7598 9.17286 12.7806 9.19898 12.8067L10.6875 14.2959C10.7917 14.4006 10.9157 14.4836 11.0522 14.5401C11.1887 14.5966 11.335 14.6254 11.4827 14.625H15.75C16.0484 14.625 16.3345 14.5065 16.5455 14.2955C16.7565 14.0845 16.875 13.7984 16.875 13.5V4.5C16.875 4.20163 16.7565 3.91548 16.5455 3.7045C16.3345 3.49353 16.0484 3.375 15.75 3.375ZM5.625 10.6875C5.29124 10.6875 4.96498 10.5885 4.68748 10.4031C4.40997 10.2177 4.19368 9.95413 4.06595 9.64578C3.93823 9.33743 3.90481 8.99813 3.96992 8.67078C4.03504 8.34344 4.19576 8.04276 4.43176 7.80676C4.66776 7.57076 4.96844 7.41004 5.29578 7.34492C5.62313 7.27981 5.96243 7.31323 6.27078 7.44095C6.57913 7.56868 6.84268 7.78497 7.0281 8.06248C7.21353 8.33998 7.3125 8.66624 7.3125 9C7.3125 9.44755 7.13471 9.87678 6.81824 10.1932C6.50178 10.5097 6.07255 10.6875 5.625 10.6875ZM12.375 10.6875C12.0412 10.6875 11.715 10.5885 11.4375 10.4031C11.16 10.2177 10.9437 9.95413 10.816 9.64578C10.6882 9.33743 10.6548 8.99813 10.7199 8.67078C10.785 8.34344 10.9458 8.04276 11.1818 7.80676C11.4178 7.57076 11.7184 7.41004 12.0458 7.34492C12.3731 7.27981 12.7124 7.31323 13.0208 7.44095C13.3291 7.56868 13.5927 7.78497 13.7781 8.06248C13.9635 8.33998 14.0625 8.66624 14.0625 9C14.0625 9.44755 13.8847 9.87678 13.5682 10.1932C13.2518 10.5097 12.8226 10.6875 12.375 10.6875Z"
              fill="white"
            />
          </svg>
          <div class="w-fit border border-black h-fit">
            Enter VR
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.card-shadow {
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}
</style>
