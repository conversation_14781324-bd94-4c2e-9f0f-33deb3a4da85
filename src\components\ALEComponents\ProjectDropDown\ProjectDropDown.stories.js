import ProjectDropDown from './ProjectDropDown.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/ProjectDropDown',
  component: ProjectDropDown,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    title: "hello world",
    lists: [
      { id: 1, value: 'Club House' },
      { id: 2, value: 'Gym' },
      { id: 3, value: 'Swimming Pool' },
    ],
  },

  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=448-1097&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
