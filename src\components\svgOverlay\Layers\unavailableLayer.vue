<script setup>
import {defineProps} from 'vue';
defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
}});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="(layer,index) in layerData"
    :id="layer.layer_data.layer_id"
    :key="index"
    :class="[
      'bg-opacity-40' ,
      layer.layer.getAttribute('class') +' '+layer.layer_data.type,
      clickedId==layer.layer_data.layer_id && clickedId!=false?'fill-white':'bg-black'
    ]"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
  <portal to="unavailable">
    <div />
  </portal>
</template>
<style scoped>

</style>
