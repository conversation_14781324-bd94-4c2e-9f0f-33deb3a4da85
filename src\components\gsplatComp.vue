
<script setup>
import { WebG<PERSON><PERSON>er, Scene, PerspectiveCamera, Mesh, PlaneGeometry, MeshBasicMaterial, Raycaster } from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import { LumaSplatsThree } from "@lumaai/luma-web";
import { onMounted, ref, onUnmounted, watch } from 'vue';
import AmenityLayer from '../components/gsplatOverlay/amenityLayer.vue';
import LandmarkLayer from './gsplatOverlay/landmarkLayer.vue';
import AmenityCatergories from '../components/ALEComponents/AmenityCatergories/AmenityCatergories.vue';
import PinLayer from './gsplatOverlay/pinLayer.vue';
import LabelLayer from './gsplatOverlay/labelLayer.vue';
import { creationToolStore } from '../store/index';
import { getCookie } from '../helpers/helper';
import router from '../router';
import { useRoute } from 'vue-router';
// Import exploreIcon from '../../public/assets/Icons/360.gif';
import StaticLayer from './gsplatOverlay/staticLayer.vue';
var camera, controls, scene, svgLayers;
const route = useRoute();
const Store = creationToolStore();

const props = defineProps({
  data: {
    type: Object,
    default () {
      return {};
    },
  },
  layers: {
    type: Object,
    default () {
      return {};
    },
  },
});
const containerVal = ref(null), amenityId = ref();
const gsplat = ref(null), position = ref(false);
const mousedown = ref(1);
const autoRotateTimeout = ref(null);
const cameraRef = ref(null);
camera = cameraRef.value;
const controlsRef = ref(null);
controls = controlsRef.value;
const sceneRef = ref(null);
scene = sceneRef.value;
svgLayers=ref();
const closeFullscreenModal = ref(false);
const checkAutoExit = ref(false);
const show_modal = ref(false), svgElems = ref([]), raycast = ref(), planeMesh= ref([]);

Object.keys(props.layers).forEach((key) => {
  svgLayers.value=props.layers[key].layers;
});
const renderer= ref();
function surroundings (refElm, id) {
  const loc1 = new CSS2DObject(refElm);
  loc1.name=id;
  scene.add(loc1);
  loc1.position.set(svgLayers.value[id].position.x, svgLayers.value[id].position.y, svgLayers.value[id].position.z);
  svgElems.value.push(loc1);
}
onMounted(() => {
  const container = containerVal.value;
  renderer.value = new WebGLRenderer({ antialias: false, enableThreeShaderIntegration: false, powerPreference: "high-performance", precision: "lowp" });
  renderer.value.setPixelRatio(1);
  renderer.value.setSize(window.innerWidth, window.innerHeight, false);
  container.appendChild(renderer.value.domElement);
  renderer.value.domElement.style.width = "inherit";
  renderer.value.domElement.style.height = "inherit";
  const labelRenderer = new CSS2DRenderer();
  labelRenderer.setSize(window.innerWidth, window.innerHeight);
  labelRenderer.domElement.style.position = 'absolute';
  labelRenderer.domElement.style.top = '0px';
  document.body.appendChild(labelRenderer.domElement);
  labelRenderer.domElement.style.pointerEvents = 'none';
  scene = new Scene();
  sceneRef.value = scene;
  camera = new PerspectiveCamera(props.data.fov, window.innerWidth / window.innerHeight, 1, 10000);
  window.camera = camera;
  controls = new OrbitControls(camera, renderer.value.domElement);
  document.addEventListener('gsplt_change', (data) => {
    camera.position.set(data.detail.position.x, data.detail.position.y, data.detail.position.z);
  });
  controls.autoRotateSpeed = 1;
  controls.autoRotate = props.data.autoRotate;
  controls.enablePan = false;
  controls.addEventListener('change', function () {

    // If you specifically want to handle rotation,
    // You can check for changes in camera rotation here.
    // For example:
    const update_config = {
      actiontype: "gsplt_change",
      rotation: camera.rotation,
      position: camera.position,
    };
    window.parent.postMessage(JSON.stringify(update_config), '*'); // Send the datas to iframe ( Origin)
  });
  if (props.data.cameraPosition){
    camera.position.set(props.data.cameraPosition.x, props.data.cameraPosition.y, props.data.cameraPosition.z);
  } else {
    camera.position.set(2.107234919667583, 1.1339903035639423, -0.7236207464939785);
  }
  controls.update();
  controls.maxPolarAngle = props.data.polarAngle? props.data.polarAngle.max:1.1;
  if (props.data.polarAngle) {
    controls.minPolarAngle = props.data.polarAngle.min;
  }
  if (props.data.distance){
    controls.maxDistance = props.data.distance.max;
    controls.minDistance = props.data.distance.min;
  } else {
    controls.minDistance = 3; // Set the minimum distance the camera can be from the center
    controls.maxDistance = 3.5; // Set the maximum distance the camera can be from the center
  }
  controls.enableDamping = true;
  controls.dampingFactor = 0.2;
  const splats = new LumaSplatsThree({
    source: props.data.source,
    // Controls the particle entrance animation
    particleRevealEnabled: true,
  });
  scene.add(splats);
  raycast.value=new Raycaster();
  Object.keys(svgLayers.value)
    .forEach((key) => {
      if (svgLayers.value[key].type==="plane"){
        const plane =new Mesh(new PlaneGeometry(svgLayers.value[key].scale.width, svgLayers.value[key].scale.height), new MeshBasicMaterial({colorWrite: false, depthWrite: false}));
        plane.position.set(svgLayers.value[key].position.x, svgLayers.value[key].position.y, svgLayers.value[key].position.z);
        scene.add(plane);
        plane.layers.enableAll();
        plane.material.side=2;
        planeMesh.value.push(plane);
      }
    });
  window.onresize = function () {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.value.setSize(window.innerWidth, window.innerHeight);
  };
  function hideIntersect (){
    svgElems.value.forEach((item) => {
      item.getWorldPosition(raycast.value.ray.origin);
      const rd=camera.position.clone().sub(raycast.value.ray.origin).normalize();
      raycast.value.ray.direction.set(rd.x, rd.y, rd.z);
      const hits=raycast.value.intersectObjects(planeMesh.value);
      if (hits.length>0){
        item.visible=false;
      } else {
        item.visible=true;
      }
    });
  }
  function animate () {
    requestAnimationFrame(animate);
    // Required if controls.enableDamping or controls.autoRotate are set to true
    controls.update();
    renderer.value.render(scene, camera);
    labelRenderer.render(scene, camera);
    hideIntersect();
  }
  animate();
  function setAutoRotate (rotate) {
    if (!rotate) {
      mousedown.value = 1;
      controls.autoRotate = false;
      clearTimeout(autoRotateTimeout.value);
    } else {
      mousedown.value = 0;
      autoRotateTimeout.value = setTimeout(() => {
        if (!mousedown.value) {
          controls.autoRotate = true;
        }
      }, 5000);
    }
  }
  if (props.data.autoRotate){
    container.onmousedown = () => {
      setAutoRotate(false);
    };
    container.ontouchstart = () => {
      setAutoRotate(false);
    };
    container.onmouseup = () => {
      setAutoRotate(true);
    };
    container.ontouchend = () => {
      setAutoRotate(true);
    };
  }
});
onUnmounted(() => {
  scene.clear();
});
function setFullScreenCookie () {
  if (!getCookie('fullscreen')) {
    const expiryTime = new Date(Date.now() + (30 * 6000)); // 30 minutes in milliseconds
    document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
    closeFullscreenModal.value = true;
  }
}
const onClickButton = (id) => {
  if (id === 'fullscreen') {
    setFullScreenCookie();
    if (!document.fullscreenElement &&
      !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.mozRequestFullScreen) {
        document.documentElement.mozRequestFullScreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
      } else if (document.documentElement.msRequestFullscreen) {
        document.documentElement.msRequestFullscreen();
      }
      checkAutoExit.value = false;
      Store.isFullScreen = !Store.isFullScreen;

    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
      checkAutoExit.value = true;
      Store.isFullScreen = !Store.isFullScreen;
    }
  }
};
// Emit Added
// Const handleGoToProject = (list) => {
//   AmenityId.value = list._id;
//   Router.push({ name: "projectScene", query: { amenity_id: list._id } });
//   Position.value = { x: 2, y: 2 };
//   Show_modal.value = true;
// };
// Function closeModal (e){
//   If ((e===undefined || e) && (e.target.getAttribute("clickaway")==="true")){
//     Show_modal.value=false;
//   }
// }
function handleImageClick (amenity){
  router.push({name: 'amenityview', params: {amenityId: amenity._id}});
}
function onClick (id){
  amenityId.value = id;
  router.push({ name: "projectScene", query: { amenity_id: id } });
  position.value = { x: 2, y: 2 };
  show_modal.value = true;
}

watch(() => show_modal, (newVal) => {
  show_modal.value = newVal;
});

watch(() => amenityId.value, (newValue) => {
  amenityId.value = newValue;
});

</script>

<template>
  <div
    id="container"
    ref="containerVal"
  />
  <div
    id="gsplat"
    ref="gsplat"
  />
  <div v-if="sceneRef">
    <div
      v-for="(layer, layerIndex) in svgLayers"
      :key="layerIndex"
      class="relative"
    >
      <AmenityLayer
        v-if="layer.type=='amenity'"
        :layer="layer"
        @append-object="(refElm)=>{ surroundings(refElm,layer.layer_id)}"
        @on-click="onClick"
      />
      <LandmarkLayer
        v-if="layer.type=='landmark'"
        :layer="layer"
        @append-object="(refElm)=>{ surroundings(refElm,layer.layer_id)}"
      />
      <PinLayer
        v-if="layer.type=='pin'"
        :layer="layer"
        @append-object="(refElm)=>{ surroundings(refElm,layer.layer_id)}"
      />
      <LabelLayer
        v-if="layer.type=='label'"
        :layer="layer"
        @append-object="(refElm)=>{ surroundings(refElm,layer.layer_id)}"
      />
      <StaticLayer
        v-if="layer.type=='static'"
        :layer="layer"
        @append-object="(refElm)=>{ surroundings(refElm,layer.layer_id)}"
      />
    </div>
  </div>
  <div
    v-if="!route.query.isGsplat"
    class="md:flex hidden justify-center items-center w-full "
  >
    <div class="absolute top-10 z-10">
      <PopUpMessageBox
        v-if="!closeFullscreenModal && !getCookie('fullscreen') "
        message="Enter to full screen mode for best experience"
        button="Enter"
        @close-x="setFullScreenCookie"
        @enter="onClickButton('fullscreen')"
      />
    </div>
  </div>
  <div
    v-if="!route.query.isGsplat"
    class="z-10 w-[100%] px-4 pb-8 flex md:hidden justify-end items-center"
  >
    <PopUpMessageBox
      v-if="!closeFullscreenModal && !getCookie('fullscreen') && !width "
      message="Enter to full screen mode for best experience"
      button="Enter"
      @close-x="setFullScreenCookie"
      @enter="onClickButton('fullscreen')"
    />
  </div>
  <div>
    <AmenityCatergories
      class="amenity-categories"
      :categories="Store.amenityCardData"
      :data="Store.amenityCardData"
      :amenityId="amenityId"
      :scrollIntoView="show_modal"
      @goto-tour="handleImageClick"
    />
  </div>
</template>
<style scoped>

.slide-animation
{
  animation:slide 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@keyframes slide
{
  0%
  {
    bottom :-12em;
  }

  100%
  {
   @apply md:bottom-[0.4em];

  }
}
</style>
<style>
.amenityIcons{
	opacity: 0.7;
	cursor: pointer;
    pointer-events:auto;
}

.amenityIcons:hover{
	opacity: 1;
}

.amenityIcons svg{
  width: auto;
  height: auto;
}

@media (orientation:landscape) and (max-height: 600px) {
  .staticLayer svg {
    width: 130px;
    height: 80px;
  }
}

@media screen and (max-width: 600px) {
  .staticLayer svg {
    width: 150px;
    height: 100px;
  }
}
</style>
