import BottomBar from './BottomBar.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/BottomBar',
  component: BottomBar,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    data: {
      "65f998cb7448011e5a0747db": {
        "_id": "65f998cb7448011e5a0747db",
        "type": "video",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F9jV7y3%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65f998cb7448011e5a0747db%2Fistockphoto-1199873461-170667a.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F9jV7y3%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65f998cb7448011e5a0747db%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "__v": 0,
        'name': 'Living room',
        "category": "abc",
      },
      "65fab61282f795c850b87d43": {
        "_id": "65fab61282f795c850b87d43",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d44": {
        "_id": "65fab61282f795c850b87d44",
        "type": "360",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d45": {
        "_id": "65fab61282f795c850b87d45",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d46": {
        "_id": "65fab61282f795c850b87d46",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d47": {
        "_id": "65fab61282f795c850b87d47",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d48": {
        "_id": "65fab61282f795c850b87d48",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d49": {
        "_id": "65fab61282f795c850b87d49",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d50": {
        "_id": "65fab61282f795c850b87d50",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
      "65fab61282f795c850b87d51": {
        "_id": "65fab61282f795c850b87d51",
        "type": "image",
        "url": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpngtree-mediterranean-interior-home-png-image_2214479.jpg?alt=media",
        "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fundefined%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Fgallery%2F65fab61282f795c850b87d43%2Fpexels-pixabay-280222.jpg?alt=media",
        "__v": 0,
        'name': 'Full view',
        "category": "abcd",
      },
    },
    categories: ['Show all', 'Interior', 'exterior', 'club house', 'cbs', 'abcd', 'qwei'],
    selectedImageId: '65fab61282f795c850b87d43',
    category: 'Show all',
    type: 'gallery',
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/3ScwDHZPtk2FRiRLsSU2T2/ALE-V.2?type=design&node-id=138-13074&mode=dev",
    },
  },

};
