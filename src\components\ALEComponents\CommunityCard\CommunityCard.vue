<script setup>
/* Props */
import { defineProps } from "vue";
defineProps({
  communityName: {type: String, default: ""},
  numberOfTowers: {type: Number, default: 0},
  numberOfFloors: {type: Number, default: 0},
  numberOfUnits: {type: Number, default: 0},
  numberOfAmenities: {type: Number, default: 0},
  area: {type: String, default: ""},
  thumbnail: {type: String, default: ""},
  measurementType: {type: String, default: ""},
});
const emit = defineEmits(['Explore']);

function ExploreButton (project_id) {
  emit('Explore', project_id);
}
</script>

<template>
  <div
    class="bg-white relative rounded-[20px] p-4 flex flex-col justify-between items-start gap-4 md:w-96 "
    aria-label="Community Card"
  >
    <img
      v-if="thumbnail"
      :src="thumbnail"
      alt=""
      class=" hidden rounded-lg w-full h-40 md:block"
    >

    <div class="overflow-hidden flex gap-3 justify-start items-start flex-col w-full">
      <h2 class="text-unitName font-bold text-2xl">
        {{ communityName }}
      </h2>
      <div class="flex justify-between items-center w-full">
        <span class="flex w-auto justify-start items-center gap-x-1">

          <svg
            v-if="numberOfTowers!=undefined"
            class="w-5 h-6"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <mask
              id="mask0_911_22306"
              style="mask-type:alpha"
              maskUnits="userSpaceOnUse"
              x="3"
              y="1"
              width="12"
              height="17"
            >
              <rect
                x="3.58691"
                y="1"
                width="10.5195"
                height="16.3637"
                fill="#D9D9D9"
              />
            </mask>
            <g mask="url(#mask0_911_22306)">
              <path
                d="M21.12 16.1949H19.9511V8.01302C19.9511 7.70302 19.828 7.40573 19.6088 7.18653C19.3896 6.96733 19.0923 6.84418 18.7823 6.84418H14.107V3.33768C14.1071 3.12603 14.0498 2.9183 13.9411 2.73668C13.8324 2.55507 13.6764 2.40637 13.4898 2.30647C13.3032 2.20657 13.093 2.15922 12.8816 2.16946C12.6702 2.1797 12.4655 2.24716 12.2894 2.36463L6.44526 6.25977C6.28494 6.36673 6.15355 6.51168 6.0628 6.6817C5.97206 6.85173 5.92476 7.04156 5.92513 7.23428V16.1949H4.75629C4.60129 16.1949 4.45265 16.2564 4.34305 16.366C4.23345 16.4756 4.17188 16.6243 4.17188 16.7793C4.17188 16.9343 4.23345 17.0829 4.34305 17.1925C4.45265 17.3021 4.60129 17.3637 4.75629 17.3637H21.12C21.275 17.3637 21.4236 17.3021 21.5332 17.1925C21.6428 17.0829 21.7044 16.9343 21.7044 16.7793C21.7044 16.6243 21.6428 16.4756 21.5332 16.366C21.4236 16.2564 21.275 16.1949 21.12 16.1949ZM18.7823 8.01302V16.1949H14.107V8.01302H18.7823ZM14 5L12.9381 3.33768V16.1949L13.5 16.5L14 5ZM11.7693 9.18185V10.3507C11.7693 10.5057 11.7077 10.6543 11.5981 10.7639C11.4885 10.8735 11.3399 10.9351 11.1849 10.9351C11.0299 10.9351 10.8812 10.8735 10.7716 10.7639C10.662 10.6543 10.6005 10.5057 10.6005 10.3507V9.18185C10.6005 9.02685 10.662 8.87821 10.7716 8.76861C10.8812 8.65901 11.0299 8.59743 11.1849 8.59743C11.3399 8.59743 11.4885 8.65901 11.5981 8.76861C11.7077 8.87821 11.7693 9.02685 11.7693 9.18185ZM9.43163 9.18185V10.3507C9.43163 10.5057 9.37005 10.6543 9.26045 10.7639C9.15085 10.8735 9.00221 10.9351 8.84721 10.9351C8.69221 10.9351 8.54356 10.8735 8.43396 10.7639C8.32436 10.6543 8.26279 10.5057 8.26279 10.3507V9.18185C8.26279 9.02685 8.32436 8.87821 8.43396 8.76861C8.54356 8.65901 8.69221 8.59743 8.84721 8.59743C9.00221 8.59743 9.15085 8.65901 9.26045 8.76861C9.37005 8.87821 9.43163 9.02685 9.43163 9.18185ZM9.43163 13.2728V14.4416C9.43163 14.5966 9.37005 14.7452 9.26045 14.8548C9.15085 14.9644 9.00221 15.026 8.84721 15.026C8.69221 15.026 8.54356 14.9644 8.43396 14.8548C8.32436 14.7452 8.26279 14.5966 8.26279 14.4416V13.2728C8.26279 13.1178 8.32436 12.9691 8.43396 12.8595C8.54356 12.7499 8.69221 12.6884 8.84721 12.6884C9.00221 12.6884 9.15085 12.7499 9.26045 12.8595C9.37005 12.9691 9.43163 13.1178 9.43163 13.2728ZM11.7693 13.2728V14.4416C11.7693 14.5966 11.7077 14.7452 11.5981 14.8548C11.4885 14.9644 11.3399 15.026 11.1849 15.026C11.0299 15.026 10.8812 14.9644 10.7716 14.8548C10.662 14.7452 10.6005 14.5966 10.6005 14.4416V13.2728C10.6005 13.1178 10.662 12.9691 10.7716 12.8595C10.8812 12.7499 11.0299 12.6884 11.1849 12.6884C11.3399 12.6884 11.4885 12.7499 11.5981 12.8595C11.7077 12.9691 11.7693 13.1178 11.7693 13.2728Z"
                fill="#6B7280"
              />
            </g>
          </svg>
          <svg
            v-else
            class="w-5 h-6"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.1806 0.749878H12.5693C12.388 0.749878 12.241 0.89691 12.241 1.0782C12.241 1.25947 12.388 1.40651 12.5693 1.40651H12.8976V3.37633H9.61446C9.43319 3.37633 9.28614 3.52336 9.28614 3.70465C9.28614 3.88592 9.43317 4.03296 9.61446 4.03296H9.94277V6.33107H6.6596C6.47833 6.33107 6.33128 6.4781 6.33128 6.65939C6.33128 6.84066 6.47831 6.9877 6.6596 6.9877H6.98792V9.61412H4.03318C3.85191 9.61412 3.70486 9.76116 3.70486 9.94244C3.70486 10.1237 3.85189 10.2708 4.03318 10.2708H4.36149V12.5689H1.07832C0.897047 12.5689 0.75 12.7159 0.75 12.8972C0.75 13.0785 0.897032 13.2255 1.07832 13.2255H1.40664V15.8519H1.07832C0.897047 15.8519 0.75 15.999 0.75 16.1802C0.75 16.3615 0.897032 16.5086 1.07832 16.5086C1.08336 16.5086 16.1771 16.5087 16.1801 16.5087C16.3614 16.5087 16.5084 16.3616 16.5084 16.1804V1.07828C16.5084 0.897012 16.3619 0.749878 16.1806 0.749878ZM15.8523 15.8517L16.1801 15.8519L16.1889 13.1999L4.68981 13.2252C4.87108 13.2252 16.1889 13.3812 16.1889 13.1999L16.1801 10.7999L7.31623 10.2705C7.4975 10.2705 16.1801 10.9812 16.1801 10.7999L16.1889 7.19988L10.271 6.98743C10.4522 6.98743 16.1889 7.38116 16.1889 7.19988V4.19988L13.2257 4.03269C13.407 4.03269 16.1889 4.38116 16.1889 4.19988L15.8521 1.79988L15.8521 1.40627L15.8523 15.8517Z"
              fill="#6B7280"
            />
            <path
              d="M10.5996 13.8819C10.5996 14.0632 10.7466 14.2103 10.9279 14.2103H14.2111C14.3924 14.2103 14.5394 14.0632 14.5394 13.8819V8.95747C14.5394 8.7762 14.3924 8.62915 14.2111 8.62915C14.0298 8.62915 13.8828 8.77618 13.8828 8.95747V13.5536H10.928C10.7468 13.5536 10.5997 13.7006 10.5997 13.8819L10.5996 13.8819Z"
              fill="#6B7280"
            />
          </svg>
          <p class="text-numberOfUnitsType font-medium"> {{ numberOfTowers!=undefined?numberOfTowers+' Towers':numberOfFloors+' Floors' }}</p>
        </span>

        <span class="flex w-auto justify-start items-center gap-x-1">

          <svg
            class="w-4 h-6"
            width="15"
            height="15"
            viewBox="0 0 15 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_911_22313)">
              <path
                d="M7.02778 6.77778V1H2.69444C1.9 1 1.25 1.65 1.25 2.44444V6.77778H7.02778ZM8.47222 6.77778H14.25V2.44444C14.25 1.65 13.6 1 12.8056 1H8.47222V6.77778ZM7.02778 8.22222H1.25V12.5556C1.25 13.35 1.9 14 2.69444 14H7.02778V8.22222ZM8.47222 8.22222V14H12.8056C13.6 14 14.25 13.35 14.25 12.5556V8.22222H8.47222Z"
                fill="#6B7280"
              />
            </g>
            <defs>
              <clipPath id="clip0_911_22313">
                <rect
                  width="15"
                  height="15"
                  fill="white"
                />
              </clipPath>
            </defs>
          </svg>

          <p class="text-numberOfUnitsType font-medium"> {{ numberOfUnits }} Units</p>
        </span>

        <span class="flex w-auto justify-start items-center gap-x-1">

          <svg
            v-if="numberOfAmenities!=undefined"
            class="w-4 h-6"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_911_22323)">
              <path
                d="M7.53107 12.0536V15.0629H3.6665V16H12.3327V15.0629H8.46819V12.0536H7.53107Z"
                fill="#6B7280"
              />
              <path
                d="M5.81509 8.48351L6.47773 7.82087L7.53125 8.87439V6.81942L5.81512 5.10329L6.47776 4.44065L7.53128 5.49413V4.0791H8.46841V7.18428L9.52193 6.13079L10.1846 6.79343L8.46844 8.50956V12.0528H12.1993C13.677 12.0528 14.8791 10.8507 14.8791 9.37303C14.8791 8.76524 14.6699 8.16995 14.2899 7.69689C14.0282 7.37095 13.6926 7.11093 13.3162 6.93772C13.4181 6.60316 13.4695 6.25568 13.4695 5.89963C13.4695 5.04578 13.1634 4.21933 12.6075 3.57255C12.1395 3.02811 11.5198 2.63849 10.8347 2.45206C10.7465 1.8379 10.4622 1.27137 10.0139 0.827859C9.47417 0.293977 8.75892 0 7.99981 0C7.24071 0 6.52543 0.293977 5.9858 0.827797C5.53748 1.27131 5.25325 1.83783 5.16506 2.452C4.4799 2.63842 3.86021 3.02805 3.39227 3.57249C2.83636 4.21926 2.5302 5.04572 2.5302 5.89957C2.5302 6.25561 2.58162 6.6031 2.68352 6.93766C2.30717 7.11087 1.97152 7.37089 1.70978 7.69682C1.32984 8.16992 1.12061 8.76518 1.12061 9.37297C1.12061 10.8506 2.32275 12.0528 3.80039 12.0528H7.53128V10.1996L5.81509 8.48351Z"
                fill="#6B7280"
              />
            </g>
            <defs>
              <clipPath id="clip0_911_22323">
                <rect
                  width="16"
                  height="16"
                  fill="white"
                />
              </clipPath>
            </defs>
          </svg>
          <svg
            v-else
            class="w-4 h-6"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3.19144 3.91587C2.93114 4.15868 2.52329 4.1445 2.28048 3.8842L0.970282 2.47961C0.738772 2.23142 0.7394 1.84622 0.971719 1.59879L2.28192 0.203358C2.52557 -0.0561489 2.93347 -0.0689986 3.19297 0.174657C3.45248 0.418313 3.46533 0.826207 3.22167 1.08571L2.8985 1.42992H10.1841L9.86271 1.08538C9.6199 0.825083 9.63408 0.417233 9.89438 0.174425C10.1547 -0.0683822 10.5625 -0.0542016 10.8053 0.206099L12.1155 1.61069C12.347 1.85888 12.3464 2.24408 12.1141 2.49151L10.8039 3.88694C10.5602 4.14645 10.1524 4.1593 9.89285 3.91564C9.63334 3.67199 9.62049 3.26409 9.86414 3.00459L10.1849 2.66294H2.90411L3.22311 3.00492C3.46592 3.26522 3.45174 3.67307 3.19144 3.91587ZM0.773438 6.91113C0.773438 6.0827 1.44501 5.41113 2.27344 5.41113H10.3399C11.1683 5.41113 11.8399 6.08271 11.8399 6.91113V14.9626C11.8399 15.791 11.1683 16.4626 10.3399 16.4626H2.27344C1.44501 16.4626 0.773438 15.791 0.773438 14.9626V6.91113ZM13.3592 6.4818C13.0989 6.72461 13.0847 7.13246 13.3275 7.39276C13.5703 7.65306 13.9782 7.66724 14.2385 7.42444L14.5834 7.10271V14.763L14.2408 14.4413C13.9813 14.1976 13.5734 14.2105 13.3297 14.47C13.086 14.7295 13.0989 15.1374 13.3584 15.381L14.7538 16.6912C15.0013 16.9235 15.3865 16.9242 15.6347 16.6927L17.0392 15.3825C17.2995 15.1396 17.3137 14.7318 17.0709 14.4715C16.8281 14.2112 16.4203 14.197 16.16 14.4398L15.8164 14.7603L15.8164 7.10257L16.1577 7.423C16.4172 7.66666 16.8251 7.65381 17.0687 7.3943C17.3124 7.13479 17.2995 6.7269 17.04 6.48324L15.6446 5.17304C15.3972 4.94072 15.012 4.9401 14.7638 5.17161L13.3592 6.4818Z"
              fill="#6B7280"
            />
          </svg>
          <p class="text-numberOfUnitsType font-medium">{{ numberOfAmenities!=undefined?numberOfAmenities+' Amenities':area+' '+measurementType }} </p>
        </span>
      </div>
      <button
        class="bg-unitsCardExploreBtn text-white w-full rounded-lg p-1.5 outline-none border-none"
        @click="ExploreButton()"
      >
        Explore
      </button>
    </div>
  </div>
</template>
