<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { defineProps } from 'vue';

const props = defineProps({type: {type: String, default: ""}, panorama: {type: String, default: ""}});
const renderer = ref(null);
const config =  {
  autoLoad: true,
  compass: false,
  default: {
    sceneFadeDuration: 1000,
  },
  type: ["equirectangular", "cubemap", "multires"].includes(props.type)
    ? props.type
    : "equirectangular",
  panorama: props.panorama,
  autoRotate: -2,
  hfov: 250,
};

onMounted(() => {
  renderer.value =  window.pannellum.viewer("panorama", config);
});
onUnmounted(() => {
  if (renderer.value){
    renderer.value.destroy();

  }
});
</script>

<template>
  <div>
    <div id="panorama" />
  </div>
</template>

<style scoped>
    * {
      margin: 0;
      padding: 0;
    }
    #panorama {
    width: 100%;
    height: 100vh;
    }
</style>
