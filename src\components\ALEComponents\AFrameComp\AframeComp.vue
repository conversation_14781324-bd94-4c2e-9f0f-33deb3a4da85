<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { cdn, loadImageData } from '../../../helpers/helper';

const emit = defineEmits(['progressData', 'hotspotClick']);

const props = defineProps({
  sceneData: {
    type: Object,
    required: true,
  },
});

const highResDataLoaded = ref(false);
const SceneTag = ref();
// Computed properties for easier access
const thumbnail = computed(() => props.sceneData.thumbnail);
const highResFile = computed(() => props.sceneData.file);
const rotation = computed(() => props.sceneData.rotation || "0 0 0");
const links = computed(() => props.sceneData.links || {});

// Default hotspot icon
const defaultHotspotIcon = "https://storagecdn.propvr.tech/assets%2Fhotspot.svg";

const loadHighResImage = (imgURL) => {
  loadImageData(imgURL, (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false)).then((highRes) => {
    highResDataLoaded.value = highRes;
  });
};

let isDragging = false;
let dragStartTime = 0;
let cameraTrackingInterval = null;
const cameraRotation = ref({ x: 0, y: 0, z: 0 });

const handleHotspotClick = (linkData) => {
  console.log('Hotspot click detected, isDragging:', isDragging);
  console.log('Link data:', linkData);

  // Only emit if it's not a drag operation
  if (!isDragging) {
    console.log('Emitting hotspot click');
    emit('hotspotClick', linkData);
  } else {
    console.log('Click ignored due to dragging');
  }
};

const handleMouseDown = () => {
  isDragging = false;
  dragStartTime = Date.now();
};

const handleMouseMove = () => {
  if (Date.now() - dragStartTime > 100) { // 100ms threshold
    isDragging = true;
  }
};

const handleMouseUp = () => {
  setTimeout(() => {
    isDragging = false;
  }, 50); // Small delay to prevent immediate clicks after drag
};

// Function to check if hotspot is visible based on camera rotation
const isHotspotVisible = (position) => {
  // For now, always return true to debug visibility issues
  // We'll implement proper visibility logic once hotspots are showing
  return true;

  // TODO: Implement proper visibility logic later
  // const hotspotX = parseFloat(position.x);
  // const hotspotY = parseFloat(position.y);
  // const hotspotZ = parseFloat(position.z);
  //
  // const angle = Math.atan2(hotspotX, hotspotZ) * (180 / Math.PI);
  // const currentCameraY = cameraRotation.value.y;
  //
  // const normalizedAngle = ((angle % 360) + 360) % 360;
  // const normalizedCameraY = ((currentCameraY % 360) + 360) % 360;
  //
  // let angleDiff = Math.abs(normalizedAngle - normalizedCameraY);
  // if (angleDiff > 180) {
  //   angleDiff = 360 - angleDiff;
  // }
  //
  // return angleDiff <= 90;
};

// Track camera rotation changes
const updateCameraRotation = () => {
  const camera = document.querySelector('#player');
  if (camera) {
    const rotation = camera.getAttribute('rotation');
    if (rotation) {
      cameraRotation.value = {
        x: rotation.x || 0,
        y: rotation.y || 0,
        z: rotation.z || 0
      };
    }
  }
};

onMounted(() => {
  emit('progressData', 0);
  loadHighResImage(cdn(highResFile.value));

  // Debug: Log links data
  console.log('Scene data:', props.sceneData);
  console.log('Links:', links.value);
  console.log('Number of links:', Object.keys(links.value).length);
  console.log('Initial rotation:', rotation.value);
  console.log('Thumbnail:', thumbnail.value);
  console.log('High res file:', highResFile.value);

  // Set up camera rotation tracking
  setTimeout(() => {
    const camera = document.querySelector('#player');
    if (camera) {
      // Update camera rotation every 100ms
      cameraTrackingInterval = setInterval(updateCameraRotation, 100);

      // Also listen for rotation events
      camera.addEventListener('componentchanged', (evt) => {
        if (evt.detail.name === 'rotation') {
          updateCameraRotation();
        }
      });
    }
  }, 1000); // Wait for A-Frame to initialize
});

onBeforeUnmount(() => {
  if (cameraTrackingInterval) {
    clearInterval(cameraTrackingInterval);
  }
});

</script>

<template>
  <a-scene
    id="VR_scene"
    ref="SceneTag"
    class="aScene_Tour h-[100vh]"
    embedded
    loading-screen="enabled: false"
    vr-mode-ui="enabled: false"
    device-orientation-permission-ui="enabled: false"
    renderer="colorManagement: true; sortObjects: true; maxCanvasWidth: 1920; maxCanvasHeight: 1920;"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
    @touchstart="handleMouseDown"
    @touchmove="handleMouseMove"
    @touchend="handleMouseUp"
  >
    <!-- Mouse Cursor (cursor & raycaster for interactive el) -->
    <a-entity
      id="mouseCursor"
      cursor="rayOrigin: mouse; fuse: false;"
      raycaster="objects: [data-raycastable];"
    />

    <a-entity id="rig" :rotation="rotation">
      <!-- Player (Camera) -->
      <a-entity
        id="player"
        camera="far: 10000; near: 0.5; fov: 100"
        look-controls
        position="0 0 1e-5"
      />
      <!-- A-Sky -->
      <a-sky
        v-if="!highResDataLoaded"
        :src="'#sky'"
        @materialtextureloaded="()=>$emit('progressData',false)"
      />
      <a-sky
        v-else
        :src="'#skyHigh'"
        @materialtextureloaded="()=>$emit('progressData',false)"
      />
    </a-entity>

    <!-- Assets (Hotspot Icons) -->
    <a-assets>
      <img
        v-if="!highResDataLoaded"
        id="sky"
        :src="cdn(thumbnail)"
        crossorigin="anonymous"
        @load="()=>$emit('progressData',50)"
      >
      <img
        v-else
        id="skyHigh"
        :src="highResDataLoaded"
        crossorigin="anonymous"
      >
      <img
        id="defaultHotspotIcon"
        crossorigin="anonymous"
        :src="defaultHotspotIcon"
        alt="defaultHotspotIcon"
      />
    </a-assets>

    <!-- Debug: Show links count -->
    <a-text
      v-if="Object.keys(links).length > 0"
      :value="`Links: ${Object.keys(links).length}`"
      position="-2 2 -3"
      color="red"
      width="10"
    />

    <!-- Test hotspot at fixed position for debugging -->
    <a-image
      look-at="[camera]"
      src="#defaultHotspotIcon"
      position="0 0 -3"
      opacity="1.0"
      scale="1 1"
      data-raycastable
      @click="handleHotspotClick({text: 'Test', destination_img_id: 'test'})"
      class="hotspot"
    >
      <a-text
        value="TEST"
        position="0 0.9 0"
        align="center"
        color="red"
        width="6"
      />
    </a-image>

    <!-- Hotspots -->
    <a-entity v-if="links && Object.keys(links).length > 0">
      <a-image
        v-for="(link, id) in links"
        :key="id"
        look-at="[camera]"
        src="#defaultHotspotIcon"
        :position="link.position"
        opacity="1.0"
        scale="1 1"
        :data-id="id"
        data-raycastable
        hotspot-listener
        @click="handleHotspotClick(link)"
        class="hotspot"
        visible="true"
      >
        <a-entity
          class="hotspot-label"
          position="0 0.9 0"
          visible="true"
        >
          <a-plane
            width="3"
            height="0.7"
            material="color: white;"
            position="0 0 0"
          />
          <a-text
            :value="link.text"
            align="center"
            width="6.9"
            color="black"
            position="0 0 0"
            font="dejavu"
            scale="1.9 1.9"
          />
        </a-entity>
      </a-image>
    </a-entity>
  </a-scene>
</template>

<style scoped>
.hotspot {
  cursor: pointer;
}

.sample {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure hotspots are always visible */
.hotspot a-sphere {
  pointer-events: auto;
}
</style>
