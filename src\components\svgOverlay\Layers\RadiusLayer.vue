<script setup>
import { ref, defineProps, watch, onMounted, onUnmounted} from 'vue';
import { useRoute } from 'vue-router';
import { creationToolStore } from '../../../store';
import { cdn, addSVGDeepZoom } from '../../../helpers/helper';
const route = useRoute();
const isRadius = ref(route.query.radius);
const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
}});
const Store = creationToolStore();
const radiusSVGElem = ref({});
Store.showRadius = true;
watch(() => {
  return route.query;
}, (newPath) => {
  isRadius.value = newPath.radius;
  if (props.sceneType==='deep_zoom'){
    Object.values(radiusSVGElem.value).forEach((item) => {
      if (isRadius.value !== 'true') {
        item.g.classList.add('!hidden');
      } else {
        item.g.classList.remove('!hidden');
      }
    });
  }
});
if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        radiusSVGElem.value[item.layer_data.layer_id] = {'g': obj.svgElement, 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel};
        if (item.layer_data.maxZoomLevel <= Store.currentZoomlevel || isRadius.value === 'true'){
          obj.svgElement.classList.remove('!hidden');
        } else {
          obj.svgElement.classList.add('!hidden');
        }
      });
    }
  });
}
if (route.query.radius === 'true'){
  props.sceneType==='deep_zoom'?document.querySelector('.openseadragon-canvas canvas').classList.add("opacity-50"):document.getElementById("svg_highres_image")?document.getElementById("svg_highres_image").classList.add("opacity-50"):document.getElementById("svg_lowres_image")?.classList.add("opacity-50");
}
// Watch(() => Store.currentZoomlevel, () => {
//   SetActiveElem(radiusSVGElem.value, Store.currentZoomlevel);
// });

onUnmounted(() => {
  Store.showRadius = false;
});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="(layer,index) in layerData"
    v-show="isRadius=='true'"
    :id="index"
    :key="index"
    :class="sceneType!=='deep_zoom'?[layer.layer.getAttribute('class') +' '+layer.layer_data.type+' cursor-pointer opacity-80 hover:opacity-100']:''"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
</template>
