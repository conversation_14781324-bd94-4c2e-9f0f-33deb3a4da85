import { createApp, h } from 'vue';
import router from '../router';
import { availUnitOrgs } from '../config/masterdata';
export function addOrdinalSuffix (number) {
  if (number % 100 >= 11 && number % 100 <= 13) {
    return number + "th";
  }
  switch (number % 10) {
    case 1:
      return number + "st";
    case 2:
      return number + "nd";
    case 3:
      return number + "rd";
    default:
      return number + "th";
  }

}
export function cdn (path) {
  if (path !== undefined){
    if (path.includes(import.meta.env.VITE_APP_BUCKET) ) {
      if (path.includes('?alt')) {
        path = path.substring(0, path.lastIndexOf('?alt'));
      }
      if (path.includes('storage.googleapis.com/download/storage/v1/b')){
        path = path.replace(`storage.googleapis.com/download/storage/v1/b/${import.meta.env.VITE_APP_BUCKET}/o`, import.meta.env.VITE_APP_BUCKET_CDN);
        return path;
      }
      path = path.replace('firebasestorage.googleapis.com/v0/b/' + import.meta.env.VITE_APP_BUCKET + '/o', import.meta.env.VITE_APP_BUCKET_CDN);
      return path;
    }
    if (path.includes('propvr-in-31420.appspot.com') ) {
      if (path.includes('?alt')) {
        path = path.substring(0, path.lastIndexOf('?alt'));
      }
      if (path.includes('storage.googleapis.com/download/storage/v1/b')){
        path = path.replace(`storage.googleapis.com/download/storage/v1/b/propvr-in-31420.appspot.com/o`, 'propvr-in-31420.appspot.com');
        return path;
      }
      path = path.replace('firebasestorage.googleapis.com/v0/b/' + 'propvr-in-31420.appspot.com' + '/o', 'storagecdn.propvr.ai');
      return path;
    }
  } else {
    return path;
  }
  return path;
}
export function  loadImageData (imageUrl, progresscallback, loadedCallback) {
  return new Promise((resolve, reject) => {
    // Console.log(imageUrl)
    const xhr = new XMLHttpRequest();
    xhr.open('GET', imageUrl, true);
    xhr.responseType = 'blob';

    xhr.onprogress = (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded / event.total) * 100);
        // Console.log(progress)
        if (progresscallback) {
          progresscallback(progress);
        }
        xhr.dispatchEvent(new CustomEvent('progress', { detail: { progress } }));
      }
    };

    xhr.onload = function () {
      if (xhr.status === 200) {
        const blob = xhr.response;
        const reader = new FileReader();
        reader.onloadend = function () {
          resolve(reader.result);
          if (loadedCallback){
            loadedCallback();
          }
        };
        reader.readAsDataURL(blob);
      } else {
        reject(new Error(`Failed to fetch image. Status: ${xhr.status}`));
      }
    };

    xhr.onerror = function (error) {
      console.log(error)
      reject(new Error('Network error occurred while fetching image.'));
    };

    xhr.send();
  });
}
export function setCookie (cname, cvalue, exdays) {
  const d = new Date();
  d.setTime(d.getTime() + (exdays*24*60*60*1000));
  const expires = "expires="+ d.toUTCString();
  document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}
export function delCookie (name1){
  return new Promise((resolve) => {
    document.cookie =  name1 + '=null;' + 'expires=Thu, 01 Jan 1970 00:00:01 GMT;'+';path=/';
    resolve();
  });
}
export function getCookie (name) {
  var nameEQ = name + "=";
  var ca = document.cookie.split(';');
  for (let i=0;i < ca.length;i++) {
    let c = ca[i];
    while (c.charAt(0)===' ') {
      c = c.substring(1, c.length);
    }
    if (c.indexOf(nameEQ) === 0) {
      return c.substring(nameEQ.length, c.length);
    }
  }
  return null;
}
export function getSplashCookie () {
  try {
    const cookie = getCookie('splashLoader');
    const parsed = cookie ? JSON.parse(cookie) : {};
    if (typeof parsed !== 'object' || parsed === null || Array.isArray(parsed)) {
      return {};
    }
    return parsed;
  } catch {
    return {};
  }
}

export function thousandSeparator (number) {
  const numberString = number.toString();

  // Check if the number has more than 3 digits
  if (numberString.length > 3) {
    // Use regular expressions to add commas
    return numberString.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  // If the number has 3 or fewer digits, return the original number
  return numberString;

}

export function listenVRTourPost () {
  window.addEventListener('message', ({data}) => {
    try {
      const Data = (typeof data === 'string' ? JSON.parse(data) : data);

      if (!Data || typeof Data !== 'object') {
        return;
      }

      // Only process VR tour related messages
      if (Data.type === 'imageChange' ||
          Data.type === 'cameraRotation' ||
          Data.type === 'hotspotClick' ||
          Data.type === 'tourLoaded' ||
          Data.type === 'loadingStatus') {

        window.parent.postMessage(Data, "*");
      }
    } catch (error) {
      console.warn('Error processing VR Tour message:', error);
    }
  });
}

export function getStatusClass (Status) {
  const statusColorMap = {
    available: 'text-green-500',
    resolved: 'text-blue-500',
    sold: 'text-red-500',
    onhold: 'text-yellow-500',
  };

  const colorClass = statusColorMap[Status.toLowerCase()] || 'text-neutral-800';
  return `text-lg font-medium capitalize ${colorClass}`;
}

export function htmlGenerator (MyComponent, props){
  const container = document.createElement('div');

  // Vue app instance
  const app = createApp({
    render: () => h(MyComponent, { ...props }),
  });
  app.mount(container);
  return container.innerHTML;
}
// Listening from matterport and posting data to salestool
export function listenPost (){
  window.addEventListener('message', ({data}) => {
    const Data = (typeof data === 'string'?JSON.parse(data):data);
    if (Data.data && typeof Data.data === 'object') {
      if (Data.simulate === true && Data.actiontype.startsWith('matterport_')) {
        const contentWindow  = document.getElementById('showcase_frame').contentWindow;
        contentWindow.postMessage({ ...Data }, '*');
      } else if (Data.actiontype.startsWith('matterport_')) {
        // Const contentWindow  = document.getElementById('showcase_frame').contentWindow;
        window.parent.postMessage({...Data}, "*");
      } else if (Data.simulate === true && Data.actiontype==="routechange"){
        const page = new URL (Data.url);
        const router_path = page.href.replace(window.location.origin, "");
        router.push(router_path);
      }
    }
  });
}

export function findCenterOfDiv (rect) {
  const left = rect.left + (rect.width / 2);
  const top = rect.top + (rect.height / 2);
  return { left, top, width: rect.width, height: rect.height };
}
// Export function centerChildDiv (id) {
//   Const child = document.getElementById(id);
//   If (!child) {
//     Return;
//   }

//   // Get viewport dimensions
//   Const viewportWidth = window.innerWidth;
//   Const viewportHeight = window.innerHeight;

//   // Get child dimensions and position
//   Const childRect = child.getBoundingClientRect();

//   // Calculate the center position
//   Let centeredLeft = (viewportWidth - childRect.width) / 2;
//   Let centeredTop = (viewportHeight - childRect.height) / 2;

//   // Adjust position to prevent the child from going beyond the viewport
//   CenteredLeft = Math.max(centeredLeft, 0); // Prevent negative value
//   CenteredTop = Math.max(centeredTop, 0); // Prevent negative value
//   Return {x: centeredLeft, y: centeredTop};
// }
export async function Googleanalytics (event, eventParams){
  if (window.gtag){
    window.gtag('event', event, eventParams);
  }
}

export function openDatabase (dbName, storeName ) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(dbName, 1);

    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains(storeName)) {
        db.createObjectStore(storeName, { keyPath: "url" });
      }
    };

    request.onsuccess = (event) => {
      resolve(event.target.result);
    };

    request.onerror = (event) => {
      reject(event.target.error);
    };
  });
}

export function debounce (func, wait) {
  let timeout;
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
}

export function isRealValue (obj) {
  return obj && obj !== "null" && obj !== "undefined" && Object.keys(obj).length !== 0;
}

export function addSVGDeepZoom (item, store){
  const svgElement = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "svg",
  );
  const gElement = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "g",
  );

  svgElement.setAttribute("version", "1.1");
  svgElement.setAttribute("x", "0px");
  svgElement.setAttribute("y", "0px");
  svgElement.setAttribute("xml:space", "preserve");
  svgElement.setAttribute("id", item.layer_id);
  svgElement.setAttribute("class", `openseadragon ${item.class || ''}`);
  // svgElement.style.pointerEvents="none";
  // gElement.style.pointerEvents="all";
  svgElement.setAttribute("clickaway", true);
  svgElement.setAttribute("viewBox", `0 0 ${item.width} ${item.height}`);
  gElement.innerHTML = item.g;

  svgElement.appendChild(gElement);

  // SvgElement.style.opacity = 0.8; // Initial opacity
  svgElement.style.zIndex = item.zIndex; // ZIndex for element

  if (item.reSize === undefined || item.reSize === true){
    // Scalable Elements
    store.addOverlay({
      element: svgElement,
      px: item.x,
      py: item.y,
      width: item.width,
      height: item.height,
    });
  } else {
    // Unscalable
    svgElement.setAttribute("width", item.width + "px");
    svgElement.setAttribute("height", item.height + "px");

    store.addOverlay({
      element: svgElement,
      px: item.x + (item.width / 2),
      py: item.y + item.height,
      placement: String(item.placement),
      checkResize: item.reSize,
    });
  }

  return {svgElement: svgElement, width: item.width};
}
export async function setActiveElem (svgElem, zoomLevel){
  Object.keys(svgElem).forEach((key) => {
    if (zoomLevel >= svgElem[key].minZoomLevel
      && zoomLevel<= svgElem[key].maxZoomLevel){
      svgElem[key].g.classList.add('!visible');
      svgElem[key].g.classList.remove('!hidden');
    } else {
      svgElem[key].g.classList.add('!hidden');
      svgElem[key].g.classList.remove('!visible');
    }
  });
}
export const removeQueryParams = (query, orgId) => {
  if (!availUnitOrgs.includes(orgId)) {
    /* eslint-disable-next-line no-unused-vars */
    const { status, min_price, max_price, min_area, max_area, unitplan_type, style_types, bedrooms, ...remainingParams } = query;
    return remainingParams;
  }
  /* eslint-disable-next-line no-unused-vars */
  const { min_price, max_price, min_area, max_area, unitplan_type, style_types, bedrooms, ...remainingParams } = query;
  return remainingParams;

};

export const removeCurrencyParam = (query) => {
  /* eslint-disable-next-line no-unused-vars */
  const { currency, ...remainingParams } = query;
  return remainingParams;
};
// Export function getMaxAgeInMilliseconds (header) {
//   Const maxAgeInSeconds = header.split(',').reduce((acc, part) => {
//     Const [key, value] = part.split('=');
//     If (key.trim() === 'max-age') {
//       Return parseInt(value, 10);
//     }
//     Return acc;
//   }, null);

//   // Convert seconds to milliseconds if max-age was found
//   Return maxAgeInSeconds !== null ? maxAgeInSeconds * 1000 : null;
// }
// Store a URL with an expiry date
// Export async function storeUrl (url, expiryDate, dbName, storeName) {
//   Const db = await openDatabase(dbName, storeName);
//   Const transaction = db.transaction([storeName], "readwrite");
//   Const store = transaction.objectStore(storeName);
//   Const urlObject = {
//     Url: url,
//     Expiry: expiryDate,
//   };

//   Const request = store.put(urlObject);

//   Request.onsuccess = () => {
//     Console.log("URL stored successfully");
//   };

//   Request.onerror = (event) => {
//     Console.error("Error storing URL:", event.target.error);
//   };
// }
// // Fetch a single URL by key
// Export async function fetchUrl (dbName, storeName, key) {
//   Return new Promise(async (resolve, reject) => {
//     Const db = await openDatabase(dbName, storeName);
//     Const transaction = db.transaction([storeName], "readonly");
//     Const store = transaction.objectStore(storeName);

//     Const request = store.get(key);

//     Request.onsuccess = (event) => {
//       Const urlObject = event.target.result;
//       If (urlObject) {
//         Var now = new Date().getTime();
//         Const expiryDate = now + urlObject.expiry;
//         If (now > new Date(expiryDate)) {
//           RemoveUrl(dbName, storeName, urlObject.url);
//           Reject('URL expired');
//         } else {
//           Resolve (urlObject.url);
//         }
//       } else {
//         Reject(`URL with key ${key} not found`);
//       }
//     };

//     Request.onerror = (event) => {
//       Console.error("Error fetching URL:", event.target.error);
//     };
//   });
// }
// Remove an expired URL
// Async function removeUrl (dbName, storeName, url) {
//   Const db = await openDatabase(dbName, storeName);
//   Const transaction = db.transaction([storeName], "readwrite");
//   Const store = transaction.objectStore(storeName);

//   Const request = store.delete(url);

//   Request.onsuccess = () => {
//     Console.log(`URL removed: ${url}`);
//   };

//   Request.onerror = (event) => {
//     Console.error("Error removing URL:", event.target.error);
//   };
// }

// src/helpers/colorHelper.js
export function hexToRgb (hex) {
  const hexWithoutHash = hex.replace('#', '');
  let mixStyle ='';

  if (hexWithoutHash.length === 6) {
    const r = parseInt(hexWithoutHash.substring(0, 2), 16);
    const g = parseInt(hexWithoutHash.substring(2, 4), 16);
    const b = parseInt(hexWithoutHash.substring(4, 6), 16);
    const rgbcode =  `rgb(${r}, ${g}, ${b})`;
    const rgbcodeVal =  (r + g + b)/3;
    let logo = '';
    const tertiary50opacity = `rgba(${r}, ${g}, ${b},0.1)`;

    if (rgbcodeVal === 255) {   // For Dark background
      mixStyle = `color-mix(in oklab, ${hex} 100%, #000000 30%)`;    // blending with black
      logo = 'light';
    } else if (rgbcodeVal === 0) {     // For light background
      mixStyle = `color-mix(in oklab,  ${hex} 100%, #ffffff 80%)`;   // blending with white
      logo = 'dark';
    } else if (rgbcodeVal > 128) {
      mixStyle = `color-mix(in oklab,  ${hex} 100%, #000000 30%)`;  // blending with black
      logo = 'dark';
    } else {
      mixStyle = `color-mix(in oklab,  ${hex} 100%, #ffffff 80%)`;   // blending with white
      logo = 'dark';
    }

    return { rgbcode, mixStyle: mixStyle, tertiary50opacity: tertiary50opacity, logo: logo};

  }

  return null;
}

export function calcBrightness (color) {

  const hexWithoutHash = color.replace('#', '');
  let logo = '';

  if (hexWithoutHash.length === 6) {
    const r = parseInt(hexWithoutHash.substring(0, 2), 16);
    const g = parseInt(hexWithoutHash.substring(2, 4), 16);
    const b = parseInt(hexWithoutHash.substring(4, 6), 16);
    const rgbcodeVal =  (r + g + b)/3;

    if (rgbcodeVal === 255) {
      logo = 'dark';
    } else if (rgbcodeVal === 0) {
      logo = 'light';
    } else if (rgbcodeVal >= 128) {
      logo = 'dark';
    } else {
      logo = 'light';
    }

  }
  return  logo ;
}

export const getUnitType =(data) => {
  if (!data || data.length === 0) {
    return null;
  }

  return data.reduce((max, current) => (current.count > max.count ? current : max), data[0])._id;
};

export const getBedroomRange = (bedrooms) => {
  if (bedrooms){
    const numbers = [...new Set(bedrooms
      .map((bedroom) => parseInt(bedroom.match(/\d+/)?.[0] || '', 10))
      .filter((num) => !isNaN(num)),
    )].sort((a, b) => a - b);

    if (numbers.length === 0) {
      return '';
    } // Return empty string if no valid numbers are found
    if (numbers.length === 1) {
      return `${numbers[0]}`;
    } // Return the single number if only one is present

    const start = numbers[0];
    const end = numbers[numbers.length - 1];

    return end - start + 1 === numbers.length
      ? `${start} to ${end}`
      : `${numbers.slice(0, -1).join(', ')} & ${end}`;
  }
  return null;
};

export async function loadFont (linkHref) {
  const link = document.createElement('link');
  if (linkHref === 'null' || linkHref === undefined) {
    link.href = 'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap';
  } else {
    link.href = linkHref;
  }
  link.rel = 'stylesheet';
  document.head.appendChild(link);
}

// extract bedroom number for Googleanalytics
export async function formatBedrooms (bedrooms) {
  if (bedrooms === undefined || bedrooms === null) {
    return '';
  }
  const bedroomsStr = String(bedrooms);
  const match = bedroomsStr.match(/^(\d+)/);
  return match ? match[1] : bedroomsStr;
}

export async function getOrderedBedroomLegend (list) {
  return [...list].sort((a, b) => {
    const numA = parseFloat(a);
    const numB = parseFloat(b);

    const isNumA = !isNaN(numA);
    const isNumB = !isNaN(numB);

    if (isNumA && isNumB) {
      return numA - numB;
    } else if (isNumA) {
      return -1;
    } else if (isNumB) {
      return 1;
    }
    return a.localeCompare(b);

  });
}
