// Store.js
import { provide, inject, reactive } from 'vue';

const eventBusSymbol = Symbol();

const createEventBus = () => {
  const state = reactive({
    data: null,
  });

  const setData = (newData) => {
    state.data = newData;
  };

  return {
    state,
    setData,
  };
};

const provideEventBus = () => {
  const eventBus = createEventBus();
  provide(eventBusSymbol, eventBus);
  return eventBus;
};

const useEventBus = () => {
  const eventBus = inject(eventBusSymbol);
  if (!eventBus) {
    throw new Error('useEventBus must be used within a component that has provided the event bus store.');
  }
  return eventBus;
};

export { provideEventBus, useEventBus };
