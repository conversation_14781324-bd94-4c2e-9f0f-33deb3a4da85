import PannellumComp from "./PannellumComp.vue";

export default {
  title: 'Design System/ALE/PannellumComp',
  component: PannellumComp,
  tags: ['autodocs'],

};

export const Primary = {
  args: {
    tour_data: {
      "scenes": {
        "6583fbf5cd59b7ddb5d0f070": {
          "_id": "6583fbf5cd59b7ddb5d0f070",
          "title": "Building1",
          "hfov": 123,
          "pitch": 123,
          "yaw": 123,
          "type": "equirectangular",
          "panorama": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FHIPUat%2Fprojects%2F123%2Ftours%2F7.jpg?alt=media",
          "hotSpots": [{
            "_id": "6583fc32cd59b7ddb5d0f07f",
            "pitch": 20,
            "yaw": 20,
            "type": "scene",
            "text": "scene of pannellum",
            "targetYaw": 20,
            "targetPitch": 20,
            "tourId": "6583fbe7cd59b7ddb5d0f06b",
            "sceneId": "6583fc0ccd59b7ddb5d0f075",
          },
          {
            "_id": "6583fc32cd59b7ddb5d0f07f",
            "pitch": 30,
            "yaw": 30,
            "type": "scene",
            "text": "scene of pannellum",
            "targetYaw": 30,
            "targetPitch": 30,
            "tourId": "6583fbe7cd59b7ddb5d0f06b",
            "sceneId": "6583fc0ccd59b7ddb5d0f075",
          },
          ],
          "tourId": "6583fbe7cd59b7ddb5d0f06b",
        },
        "6583fc0ccd59b7ddb5d0f075": {
          "_id": "6583fc0ccd59b7ddb5d0f075",
          "title": " Building2",
          "hfov": 123,
          "pitch": 123,
          "yaw": 123,
          "type": "equirectangular",
          "panorama": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FHIPUat%2Fprojects%2F123%2Ftours%2F5.jpg?alt=media",
          "hotSpots": [ {
            "_id": "6583fc3bcd59b7ddb5d0f084",
            "pitch": 134,
            "yaw": 121,
            "type": "scene",
            "text": "scene of pannellum",
            "targetYaw": 20,
            "targetPitch": 20,
            "tourId": "6583fbe7cd59b7ddb5d0f06b",
            "sceneId": "6583fc1acd59b7ddb5d0f07a",
          },
          ],
          "tourId": "6583fbe7cd59b7ddb5d0f06b",
        },
        "6583fc1acd59b7ddb5d0f07a": {
          "_id": "6583fc1acd59b7ddb5d0f07a",
          "title": " Building3",
          "hfov": 123,
          "pitch": 123,
          "yaw": 123,
          "type": "equirectangular",
          "panorama": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FHIPUat%2Fprojects%2F123%2Ftours%2F3.jpg?alt=media",
          "hotSpots": [ {
            "_id": "6583fc45cd59b7ddb5d0f089",
            "pitch": 12,
            "yaw": 22,
            "type": "scene",
            "text": "scene of pannellum",
            "targetYaw": 20,
            "targetPitch": 20,
            "tourId": "6583fbe7cd59b7ddb5d0f06b",
            "sceneId": "6583fbf5cd59b7ddb5d0f070",
          },
          ],
          "tourId": "6583fbe7cd59b7ddb5d0f06b",
        },
      },
    },
  },
};
