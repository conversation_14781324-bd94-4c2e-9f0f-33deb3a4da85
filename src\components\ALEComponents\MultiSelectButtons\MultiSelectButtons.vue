<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { creationToolStore } from '../../../store';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

// Props
const props = defineProps({
  arrayValues: {
    type: Array,
    required: true,
  },
  selectedArrayItems: {
    type: Array,
    required: true,
  },
});

const store = creationToolStore();

// Emits
const emit = defineEmits(['update:selectedItems', 'reset']);

const selectedItems = ref([]);

const toggleItemSelection = (arrayValue) => {
  if (selectedItems.value.includes(arrayValue)) {
    selectedItems.value = selectedItems.value.filter((val) => val !== arrayValue);
  } else {
    selectedItems.value.push(arrayValue);
  }
  emit('update:selectedItems', selectedItems.value);
};

function getClass (value) {
  const isSelected = props.selectedArrayItems.includes(value);
  const colorMap = {
    '1BHK': 'bg-[#feecdc] text-[#ff8a4c] border-2 border-[#ff8a4c]',
    '2BHK': 'bg-[#fdfdea] text-[#e3a008] border-2 border-[#e3a008]',
    '3BHK': 'bg-[#def7ec] text-[#31c48d] border-2 border-[#31c48d]',
    '4BHK': 'bg-[#fdfdea] text-[#8e4b10] border-2 border-[#8e4b10]',
    '5BHK': 'bg-[#fdf2f2] text-[#9b1c1c] border-2 border-[#9b1c1c]',
    '6BHK': 'bg-[#f6f5ff] text-[#ac94fa] border-2 border-[#ac94fa]',
    '7BHK': 'bg-[#fdf2f8] text-[#f17eb8] border-2 border-[#f17eb8]',
    'studio': 'bg-[#ebf5ff] text-[#1a56db] border-2 border-[#1a56db]',
    'penthouse': 'bg-[#f6f5ff] text-[#ac94fa] border-2 border-[#ac94fa]',
    'townhouse': 'bg-[#fdf2f8] text-[#f17eb8] border-2 border-[#f17eb8]',
    'duplex': 'bg-[#ebf5ff] text-[#1a56db] border-2 border-[#1a56db]',
    'suite': 'bg-[#fdf2f2] text-[#9b1c1c] border-2 border-[#9b1c1c]',
  };

  return `${isSelected ? colorMap[value] || 'bg-primary text-primaryText' : 'bg-tertiary50opacity text-secondaryText'}`;
}

function resetButtons () {
  selectedItems.value = [ ];
  emit('reset', selectedItems.value);
}

defineExpose({
  resetButtons,
});

</script>

<template>
  <div class="flex flex-row flex-wrap gap-2 ">
    <div
      v-for="arrayValue, in arrayValues"
      :key="arrayValue"
      class="flex-1"
    >
      <input
        :id="'checkbox-' + arrayValue"
        v-model="selectedItems"
        type="checkbox"
        :value="arrayValue"
        class="hidden"
        @click="toggleItemSelection(arrayValue)"
      >
      <label
        :for="'checkbox-' + arrayValue"
        class="border rounded-lg cursor-pointer transition-colors w-full text-sm capitalize"
        :class="[store.isMobile ? 'px-8 py-2 min-w-16' : 'px-6 py-2 text-sm hover:border-[white]',
                 getClass(arrayValue),
        ]"
        style="display: inline-block;text-align: center;"
      >
        <TranslationComp
          :text="arrayValue"
        />

      </label>
    </div>
  </div>
</template>

<style scoped></style>
