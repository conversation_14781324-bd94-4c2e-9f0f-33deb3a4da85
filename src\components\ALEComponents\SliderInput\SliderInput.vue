<script setup>
import * as slider from '@zag-js/slider';
import { normalizeProps, useMachine } from '@zag-js/vue';
import { computed, ref, defineEmits } from "vue";
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { creationToolStore } from '../../../store';

const Store = creationToolStore();
/* Props & Emits */
const props = defineProps({
  labelType: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  minValue: {
    type: Number,
    required: true,
  },
  maxValue: {
    type: Number,
    required: true,
  },
  initialMin: {
    type: Number,
    required: true,
  },
  initialMax: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits(['update:range']);

// Refs
const rangeSlider = ref(null);

const [minMaxState, minMaxSend] = useMachine(
  slider.machine({
    id: `minmax${props.labelType}`,
    name: `minmax${props.labelType}`,
    min: props.minValue,
    max: props.maxValue,
    value: [props.minValue, props.maxValue],
    onValueChangeEnd (details) {
      emit('update:range', details);
    },
  }),
);

const minAndMaxComputed = computed(() => slider.connect(minMaxState.value, minMaxSend, normalizeProps));
const minMaxComputed = computed(() => (minMaxState.value.context.value));

const formattedMinValue = computed({
  get () {
    return new Intl.NumberFormat('en-US').format(minMaxComputed.value[0]);
  },
  set (value) {
    const numericValue = Number(value.replace(/,/g, ''));
    if (!isNaN(numericValue)) {
      minMaxState.value.context.value = [
        numericValue,
        minMaxComputed.value[1],
      ];
    }
  },
});

const formattedMaxValue = computed({
  get () {
    return new Intl.NumberFormat('en-US').format(minMaxComputed.value[1]);
  },
  set (value) {
    const numericValue = Number(value.replace(/,/g, ''));
    if (!isNaN(numericValue)) {
      minMaxState.value.context.value = [
        minMaxComputed.value[0],
        numericValue,
      ];
    }
  },
});

// initial Value Setter
if (props.initialMin && props.initialMax) {
  minMaxSend({ type: 'SET_VALUE', value: [props.initialMin, props.initialMax] });
}

// Clearing Values
function clear () {
  minMaxSend({ type: 'SET_VALUE', value: [props.minValue, props.maxValue] });
}

// Function Exposing
defineExpose({
  clear,
});
</script>

<template>
  <div>
    <label
      v-if="props.type && Object.keys(props.type).length > 0"
      class="label-primary"
    >
      <TranslationComp
        :text="labelType"
      />
      (
      <TranslationComp
        :text="type"
      />
      )</label>
    <label
      v-else
      class="label-primary"
    >
      <TranslationComp
        :text="labelType"
      />
    </label>
    <div class="px-1">
      <div
        ref="rangeSlider"
        v-bind="minAndMaxComputed.getRootProps()"
      >
        <div v-bind="minAndMaxComputed.getControlProps()">
          <div v-bind="minAndMaxComputed.getTrackProps()">
            <div v-bind="minAndMaxComputed.getRangeProps()" />
          </div>
          <div
            v-for="(_, index) in minAndMaxComputed.value"
            :key="index"
            v-bind="minAndMaxComputed.getThumbProps({ index })"
          >
            <input v-bind="minAndMaxComputed.getHiddenInputProps({ index })">
          </div>
        </div>
      </div>
    </div>
    <div class="flex justify-around gap-3">
      <div :class="Store.isLandscape? 'w-1/2' :''">
        <label
          for="From"
          class="label-primary"
        >
          <TranslationComp
            text="From"
          /> </label>
        <div class="px-2">
          <input
            v-model="formattedMinValue"
            readonly
            class="input-box"
          >
        </div>
      </div>
      <div :class="Store.isLandscape? 'w-1/2' :''">
        <label
          for="to"
          class="label-primary"
        >
          <TranslationComp
            text="To"
          /> </label>
        <div class="px-2">
          <input
            v-model="formattedMaxValue"
            readonly
            class="input-box"
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.label-primary {
  @apply block text-white text-sm font-medium;
}

.input-box {
  width: 100%;
  padding: 8px;
  color: var(--colormix);
  border: 1px solid var(--secondaryText);
  border-radius: 5px;
  text-align: left;
  font-size: 14px;
}

</style>

<style scoped>
/* Range Slider */
[data-scope="slider"][data-part="root"] {
  width: 100%;
}

[data-scope="slider"][data-part="root"]>div:first-child {
  display: flex;
  justify-content: space-between;
}

[data-scope="slider"][data-part="label"] {
  margin-right: 0.5rem;
}

[data-scope="slider"][data-part="control"] {
  display: flex;
  align-items: center;
  margin-top: 0rem;
  position: relative;
  padding-block: 0.625rem;
}

[data-scope="slider"][data-part="track"] {
  height: 7px;
  border-radius: 9999px;
  flex: 1;
  background: var(--tertiary50opacity);
}

[data-scope="slider"][data-part="range"] {
  height: 100%;
  border-radius: inherit;
  background: var(--primary);
}

[data-scope="slider"][data-part="thumb"] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 9999px;
  background: var(--primary);
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

[data-scope="slider"][data-part="thumb"]:is(:focus, [data-focus]) {
  outline: 2px solid var(--primary);
}

[data-scope="slider"][data-part="thumb"][data-disabled] {
  background: var(--secondaryText);
}
</style>
