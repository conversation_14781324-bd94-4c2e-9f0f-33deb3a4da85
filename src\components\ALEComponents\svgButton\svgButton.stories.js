import SvgButton from './svgButton.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/svgButton',
  component: SvgButton,
  tags: ['autodocs'],
  argTypes: {

  },

};

const Template = (args) => ({
  components: { SvgButton },
  setup () {
    return { args };
  },
  template: `
    <SvgButton :title=args.title :theme=args.theme>
        <template v-slot:svg>
        <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6.02447 1.08156C6.17415 0.620904 6.82585 0.620906 6.97553 1.08156L7.82058 3.68237C8.02139 4.30041 8.59733 4.71885 9.24717 4.71885H11.9818C12.4662 4.71885 12.6676 5.33866 12.2757 5.62336L10.0633 7.23075C9.5376 7.61271 9.31762 8.28976 9.51843 8.9078L10.3635 11.5086C10.5132 11.9693 9.98592 12.3523 9.59406 12.0676L7.38168 10.4602C6.85595 10.0783 6.14405 10.0783 5.61832 10.4602L3.40594 12.0676C3.01408 12.3523 2.48684 11.9693 2.63652 11.5086L3.48157 8.9078C3.68238 8.28976 3.4624 7.61271 2.93667 7.23075L0.724284 5.62336C0.332426 5.33865 0.533816 4.71885 1.01818 4.71885H3.75283C4.40267 4.71885 4.9786 4.30041 5.17942 3.68237L6.02447 1.08156Z" fill="#262626" stroke="#262626"/>
        </svg>
     
        </template>
    </SvgButton>
  `,
});

export const Primary = Template.bind({});
Primary.args = {
  title: 'hello world',
  theme: "dark",
};

export const Design={
  args:
  {
    title: 'hello world',
    theme: "dark",
  },
  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-1041&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
