import OverflowSlider from './OverflowSlider.vue';

/* Sample */
const sampleTemplate = `
  <template v-slot:options>

    <swiper-slide class="w-[108px] h-[190px] bg-[rgba(150,150,150,0.3)] flex justify-center items-center">
                                                                                                                      
                              <p class="whitespace-nowrap text-ellipsis inline overflow-hidden capitalize select-none text-white"> 
                                                                                  Item 1
                              </p>

          

    </swiper-slide>

    <swiper-slide class="w-[108px] h-[100px] bg-[rgba(150,150,150,0.3)]  flex justify-center items-center ">
                                                                                                                       
                              <p class="whitespace-nowrap text-ellipsis inline overflow-hidden capitalize select-none text-white"> 
                                                                                  Item 2
                              </p>

          

    </swiper-slide>

    <swiper-slide class="w-[108px] h-[190px] bg-[rgba(150,150,150,0.3)]  flex justify-center items-center text-white">
                                                                                                                 
                              <p class="whitespace-nowrap text-ellipsis inline overflow-hidden capitalize select-none"> 
                                                                                  Item 3
                              </p>

      
    </swiper-slide>

    <swiper-slide class="w-[108px] h-[190px] bg-[rgba(150,150,150,0.3)]  flex justify-center items-center text-white">
                                                                                                                 
    <p class="whitespace-nowrap text-ellipsis inline overflow-hidden capitalize select-none"> 
                                                        Item 4
    </p>


</swiper-slide>


<swiper-slide class="w-[108px] h-[190px] bg-[rgba(150,150,150,0.3)]  flex justify-center items-center text-white">
                                                                                                                 
<p class="whitespace-nowrap text-ellipsis inline overflow-hidden capitalize select-none"> 
                                                    Item 5
</p>


</swiper-slide>
<swiper-slide class="w-[108px] h-[190px] bg-[rgba(150,150,150,0.3)]  flex justify-center items-center text-white">
                                                                                                                 
<p class="whitespace-nowrap text-ellipsis inline overflow-hidden capitalize select-none"> 
                                                    Item 6
</p>


</swiper-slide>

  </template>
`;

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories

export default {
  title: 'Design System/ALE/OverflowSlider',
  component: OverflowSlider,
  tags: ['autodocs'],
};

const Template = (args) => ({
  components: { OverflowSlider },
  setup () {
    return { args };
  },
  template: `
    <OverflowSlider :slidesPerGroup="args.slidesPerGroup" :inlineArrowWrapperStyles="args.inlineArrowWrapperStyles"  :inlineArrowStyles="args.inlineArrowStyles" :arrows="args.arrows" :pagination="args.pagination" :inlineIndicatorsWrapperStyles="args.inlineIndicatorsWrapperStyles" :inlineIndicatorsStyles="args.inlineIndicatorsStyles" :spaceBetween="args.spaceBetween" :slidesPerView="args.slidesPerView" :direction="args.direction" :initialSlide="args.initialSlide" :verticalFixedHeight="args.verticalFixedHeight">
        ${sampleTemplate}
    </OverflowSlider>
  `,
});

export const Primary = Template.bind({});

Primary.args = {
  inlineArrowWrapperStyles: 'background-color:teal;padding:10px;border-radius:50%;height:fit-content;width:fit-content;',
  inlineArrowStyles: 'width:20px;height:20px;color:white;',
  inlineIndicatorsWrapperStyles: 'background-color:transparent;height:fit-content;',
  inlineIndicatorsStyles: 'width:10px;height:10px;background-color:white;',
  arrows: true,
  pagination: true,
  spaceBetween: '15',
  slidesPerView: '2',
  slidesPerGroup: '2',
  direction: 'horizontal',
  initialSlide: 2,
  /* VerticalFixedHeight:true, */

}; // Props
