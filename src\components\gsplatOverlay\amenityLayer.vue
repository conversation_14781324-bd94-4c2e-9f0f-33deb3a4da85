<script setup>
import { ref, defineProps, onMounted, defineEmits } from 'vue';
import { layer_icons } from '../../config/masterdata';
import { cdn } from '../../helpers/helper';
const props = defineProps({layer: {
  type: Object,
  default () {
    return {};
  },
}});

const emit = defineEmits(['appendObject', 'onClick']);
const amenityLayer = ref(null);

function onClick (id){
  emit('onClick', id);
}
onMounted(async () => {
  const url =  cdn(props.layer.svg_url);
  var svgString;
  if (props.layer.svg_url !== undefined && props.layer.svg_url !== ""){
    const requestOptions = {
      method: "GET",
      redirect: "follow",
    };
    const response = await fetch(url, requestOptions);
    svgString = await response.text();
  } else {
    svgString = layer_icons[props.layer.type][props.layer.name].icon;
  }
  const div = document.createElement('div');
  div.id=props.layer.layer_id;
  div.innerHTML=svgString;
  div.setAttribute('class', 'amenityIcons');
  amenityLayer.value=div;
  document.body.appendChild(div);
  document.getElementById(props.layer.layer_id).addEventListener( 'click', () => onClick(props.layer.amenity_id) );
  emit('appendObject', amenityLayer.value);
});
</script>
