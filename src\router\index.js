/* eslint-disable eol-last */
import { createRouter, createWeb<PERSON><PERSON>ory } from "vue-router";
import { setCookie } from "../helpers/helper";

const routes = [
  {
    path: "/:organizationId/root",
    name: "root",
    props: true,
    component: () => import("../views/rootComponent.vue"),
  },
  {
    path: "/:organizationId",
    name: "root",
    props: true,
    component: () => import("../views/projectListView.vue"),
  },
  {
    path: "/",
    name: "pannellumVidoe360",
    props: true,
    component: () =>
      import(
        "../components/ALEComponents/PannellumVideo360/PannellumVideo360.vue"
      ),
  },
  {
    path: "/:project_id",
    name: "virtualTourComp",
    props: true,
    component: () => import("../components/VirtualTourComp.vue"),
  },
  {
    path: "/:organizationId/masterscene/:sceneId",
    name: "masterScene",
    props: true,
    component: () => import("../views/masterView.vue"),
    beforeEnter: (to, from) => {
      setCookie("oldSceneId", from.params.sceneId);
    },
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId",
    name: "project",
    props: true,
    component: () => import("../views/projectView.vue"),
    beforeEnter: (to, from) => {
      setCookie("oldSceneId", from.params.sceneId);
    },
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/:sceneId",
    name: "projectScene",
    props: true,

    component: () => import("../views/projectView.vue"),
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/:sceneId/:unitplanId",
    name: "unit",
    props: true,
    component: () => import("../views/unitParentView.vue"),
    children: [
      {
        path: "",
        name: "unit.Child",
        props: true,
        component: () => import("../views/unitplanView.vue"),
      },
    ],
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/:sceneId/:unitplanId/:tourId",
    name: "vrtour",
    props: true,
    component: () => import("../views/VRtourView.vue"),
  },
  // {
  //   path: "/:language?/:organizationId/projectscene/:projectId/tourscene/:tourId",
  //   name: "vrtour",
  //   props: true,
  //   component: () => import("../views/VRtourView.vue"),
  // },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/:sceneId/amenityview/:amenityId",
    name: "amenityview",
    props: true,
    component: () => import("../views/amenityView.vue"),
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/amenity",
    name: "amenity",
    props: true,
    component: () => import("../views/amenityView.vue"),
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/galleryview",
    name: "galleryview",
    props: true,
    component: () => import("../views/galleryView.vue"),
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/unitplan/",
    name: "unitplansviewer",
    props: true,
    component: () => import("../views/unitplansViewer.vue"),
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/mapview",
    name: "mapview",
    props: true,
    component: () => import("../views/mapsView.vue"),
  },
  {
    path: "/:organizationId/:projectId/tourview/:tourId",
    name: "tourview",
    props: true,
    component: () => import("../views/mleView.vue"),
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/map",
    name: "map",
    props: true,
    component: () => import("../views/googleMapView.vue"),
  },
  {
    path: "/:language?/:organizationId/projectscene/:projectId/:sceneId/unitview",
    name: "unitview",
    props: true,
    component: () => import("../views/unitplanView.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

const handleRouteChanges = (id, path) => {
  if (path !== null && path !== undefined){
    const newObject = {
      actiontype: "routechange",
      url: window.location.origin + path,
    };
    window.parent.postMessage(JSON.stringify(newObject), '*'); // Send the datas to iframe ( Origin)
  }
};
window.addEventListener('message', ({data}) => {
  let Data = data;
  if (typeof data === 'string') {
    try {
      Data = JSON.parse(data);
    } catch (e) {
      return; // Ignore non-JSON messages
    }
  }
  if (data.simulate === true){
    const event = new CustomEvent(Data.actiontype, {detail: Data});
    document.dispatchEvent(event);

    if (data.actiontype === "routechange"){
      const page = new URL (data.url);
      const router_path = page.href.replace(window.location.origin, "");
      router.push(router_path);
    }

  }

});
// Global Guards
router.beforeEach((to, from, next) => {
  handleRouteChanges(to.params.id, to.fullPath);

  if (!to.params.language && from.params.language) {
    next({...to, params: {...to.params, language: from.params.language}});
  } else {
    next();
  }
});

export default router;