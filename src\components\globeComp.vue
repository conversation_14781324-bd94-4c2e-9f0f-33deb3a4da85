<script setup>
import { ref, onMounted, defineEmits, defineProps, defineExpose,  onBeforeUnmount } from 'vue';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import {
  WebGLRenderer,
  Scene, PerspectiveCamera,
  TextureLoader, PointLight,
  Color,
  MeshStandardMaterial, WebGLCubeRenderTarget, ACESFilmicToneMapping, EquirectangularReflectionMapping, SRGBColorSpace,
  Cache, Euler,
} from 'three';
// Mesh
import { Lensflare, LensflareElement } from 'three/examples/jsm/objects/Lensflare.js';
import ThreeGlobe from 'three-globe';
import * as TWEEN from '@tweenjs/tween.js';
import { htmlGenerator } from '../helpers/helper';
import EarthLabel from '../components/ALEComponents/EarthLabel/EarthLabel.vue';
import { creationToolStore } from '../store';
import { useRoute } from 'vue-router';

const route = useRoute();
const Store = creationToolStore();
const globeViz = ref(null);
const globeMesh = ref();
const svgIcon = ref([]);
let renderers = [new WebGLRenderer(), new CSS2DRenderer()];
let scene = new Scene();
let camera = new PerspectiveCamera();
const props = defineProps({data: {type: Array, default () {
  return [];
}}, isMobile: {type: String, default: ""}});
const emits = defineEmits(['emitClickData', 'initialAnimationEnd', 'selectedAnimationEnd', 'allTextureLoaded']);
const texturePromises = [];
let controls = 0;
let Globe;
let lensflare;
const activeElm = ref(false);

document.title=  route.params.organizationId + '-'+Store.breadCrumb[Store.breadCrumb.length-1].name;

const handleClick = (id, status, name) => {
  if (status) {
    emits('emitClickData', id);
  } else {
    activeElm.value= name;
    document.getElementById(name+"_CS").style.display="block";
  }

};
const animateCameraPosition = (currentPosition, targetPosition, duration, setDistance = true) => {
  return new Promise((resolve) => {
    if (!setDistance) {
      controls.minDistance = 0;
      controls.maxDistance = Infinity;
    }
    new TWEEN.Tween(currentPosition)
      .to(targetPosition, duration)
      .easing(TWEEN.Easing.Sinusoidal.InOut)
      .onStart(() => {
        controls.enabled = false;
      })
      .start()
      .onComplete(() => {
        // Controls.target.set(targetPosition.x, targetPosition.y, targetPosition.z - 0.001);
        if (setDistance) {
          controls.minDistance = props.isMobile?490:300;
          controls.maxDistance = props.isMobile?470:280;
        } else {
          controls.minDistance = 0;
          controls.maxDistance = Infinity;
        }
        controls.enabled = true;
        resolve();
      });
  });
};
const animateOpacity = (el, currentOpacity, targetOpacity, duration) => {
  return new Promise((resolve) => {
    new TWEEN.Tween(currentOpacity)
      .to(targetOpacity, duration)
      .easing(TWEEN.Easing.Sinusoidal.InOut)
      .start()
      .onUpdate(() => {
        el.map((item) => {
          return item.style.opacity = currentOpacity.opacity;
        });
      })
      .onComplete(() => {
        el.map((item) => {
          return item.style.opacity = targetOpacity.opacity;
        });
        resolve();
      });
  });

};
async function startInitialAnimation () {
  window.animateCameraPosition = animateCameraPosition;
  window.position = camera.position;
  window.target = camera.target;
  Promise.all([
    animateCameraPosition(camera.position, { x: 197.587, y: 79.459, z: props.isMobile?440.293:211.293 }, 2000),
    animateCameraPosition(controls.target, { x: 0, y: 0, z: 0 }, 2000),
    animateOpacity(svgIcon.value, { opacity: 0 }, { opacity: 1 }, 2000)]).then(() => {
    emits('initialAnimationEnd');
  });

}
function onWindowResize () {

  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();

  renderers.forEach((r, idx) => {
    r.setSize(window.innerWidth, window.innerHeight);
    if (idx > 0) {
      r.domElement.style.position = 'absolute';
      r.domElement.style.top = '0px';
      r.domElement.style.pointerEvents = 'none';
      r.domElement.style.cursor = 'pointer';
    }
  });
}
function init (){
  renderers[0].toneMapping = ACESFilmicToneMapping;
  renderers[0].toneMappingExposure = 1.5;
  Globe = new ThreeGlobe()
    .globeImageUrl('/assets/globe/earthTexture.jpg')
    .htmlElementsData(props.data)
    .htmlElement((d) => {
      const el = document.createElement('div');
      el.style.opacity = 0;
      svgIcon.value.push(el);
      // El.innerHTML = props.markerIcon;
      const label = htmlGenerator(EarthLabel, {name: d.name, active: d.active});
      //   Const activeClass = d.active === true ? 'earthLabelContainerActive' : 'earthLabelContainer';
      //   El.innerHTML=`
      //   <div class="flex items-center gap-x-2">
      //   <div   class="group ${activeClass} text-[#D4D4D4] h-fit w-fit relative px-2.5 py-4 rounded-[30px]   hover:cursor-pointer">
      //   <div class="bg-transparent w-full h-fit flex justify-center items-center gap-2">
      //     <svg class="h-6 w-6  viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      //     <path d="M5.76911 6.27046C5.6842 6.34667 5.6162 6.44098 5.56975 6.54693C5.52331 6.65288 5.49952 6.76797 5.50001 6.88432V18.1923C5.50001 18.4065 5.58219 18.612 5.72846 18.7634C5.87474 18.9149 6.07314 19 6.28001 19C6.48688 19 6.68527 18.9149 6.83155 18.7634C6.97783 18.612 7.06001 18.4065 7.06001 18.1923V15.3464C8.54461 14.1941 9.7965 14.7326 11.6542 15.683C12.7092 16.2215 13.9123 16.8407 15.2162 16.8407C16.173 16.8407 17.1844 16.5082 18.2309 15.5699C18.3151 15.4943 18.3828 15.4009 18.4292 15.2959C18.4756 15.1909 18.4997 15.0769 18.5 14.9614V6.88432C18.5 6.72945 18.4571 6.57784 18.3762 6.44754C18.2954 6.31725 18.1801 6.21377 18.044 6.14944C17.908 6.0851 17.757 6.06262 17.6089 6.08467C17.4609 6.10673 17.3221 6.17238 17.2091 6.27382C15.603 7.71492 14.3147 7.16971 12.3458 6.16074C10.5174 5.21976 8.24106 4.05329 5.76911 6.27046ZM16.94 14.5751C15.4554 15.7281 14.2035 15.1883 12.3458 14.2385C10.8404 13.4645 9.03146 12.5383 7.06001 13.4719V7.26731C8.54461 6.11497 9.7965 6.65345 11.6542 7.60386C12.7092 8.14233 13.9123 8.76158 15.2162 8.76158C15.8114 8.7625 16.3997 8.62905 16.94 8.37051V14.5751Z" fill=""/>
      //     </svg>

      //     <p class="text-inherit text-base font-medium w-fit z-10 relative"> ${d.name} </p>
      //   </div>
      // </div>
      // <div id="${d.name}_CS" class="absolute right-[-110px] hidden px-3 py-2 bg-neutral-800 bg-opacity-75 rounded-[30px] justify-center items-center gap-2.5 inline-flex">
      //       <div class="text-center text-white text-xs font-medium">Coming Soon.</div>
      //   </div>
      // </div>`
      el.innerHTML = `${label}`;
      el.style.width = `${d.size}px`;
      el.style.pointerEvents = 'auto';
      el.id = d.name+"_id";
      el.addEventListener('click', () => handleClick(d.link, d.active, d.name));
      return el;
    });

  texturePromises.push(
    new Promise((resolve) => {
      Globe.onGlobeReady(() => {
        resolve();
      });
    }),
  );

  Globe.showAtmosphere(true);
  Globe.atmosphereColor("#c6e7f7");
  Globe.atmosphereAltitude(0.15);
  Globe.traverse((node) => {
    if (node.type === 'Mesh') {
      globeMesh.value = node;
    }
  });
  globeMesh.value.material = new MeshStandardMaterial();
  const GlobeMaterial = globeMesh.value.material;
  const targetCube = new WebGLCubeRenderTarget(512, 512);
  // GlobeMaterial.color = new Color(0x3a228a);
  texturePromises.push(new Promise((resolve) => {
    new TextureLoader().load('/assets/globe/earthEmissive.jpg', (texture) => {
      GlobeMaterial.emissiveMap = texture;
      GlobeMaterial.emissiveIntensity = 0.5;
      GlobeMaterial.emissive = new Color(1, 1, 1);
      resolve();
    });
  }));

  texturePromises.push(new Promise((resolve) => {
    new TextureLoader().load('/assets/globe/earthNormal.jpg', (texture) => {
      GlobeMaterial.normalMap = texture;
      resolve();
    });
  }));

  texturePromises.push(new Promise((resolve) => {
    new TextureLoader().load('/assets/globe/earthRough.jpg', (texture) => {
      GlobeMaterial.roughnessMap = texture;
      resolve();
    });
  }));

  texturePromises.push(new Promise((resolve) => {
    new TextureLoader().load("/assets/globe/spaceenv.jpg", (texture) => {
      // Create a cube texture from the panorama
      const cubeTex = targetCube.fromEquirectangularTexture(renderers[0], texture);
      GlobeMaterial.envMap = cubeTex.texture;
      //  GlobeMaterial.envMap.rotation=45
      resolve();
    });
  }));

  GlobeMaterial.roughness = 0.5;
  // GlobeMaterial.metalness=0.1
  GlobeMaterial.envMapIntensity = 2;

  renderers.forEach((r, idx) => {
    r.setSize(window.innerWidth, window.innerHeight);
    if (idx > 0) {
      r.domElement.style.position = 'absolute';
      r.domElement.style.top = '0px';
      r.domElement.style.pointerEvents = 'none';
      r.domElement.style.cursor = 'pointer';
    }
    globeViz.value.appendChild(r.domElement);
  });

  scene.add(Globe);

  const textureLoader = new TextureLoader();
  const textureEquirec = textureLoader.load('/assets/globe/spacebg.jpg');
  textureEquirec.mapping = EquirectangularReflectionMapping;
  textureEquirec.colorSpace = SRGBColorSpace;
  scene.background = textureEquirec;

  const sunLight = new PointLight(0xffffff, 0.2, 20000);
  const sunTextureLoader = new TextureLoader();
  const textureFlare = sunTextureLoader.load("/assets/globe/sun3.webp");
  const textureFlare0 = sunTextureLoader.load('/assets/globe/lensflare.png');
  lensflare = new Lensflare();
  lensflare.addElement(new LensflareElement(textureFlare, 512, 0));
  lensflare.addElement(new LensflareElement(textureFlare0, 512, 0));
  sunLight.position.set(250, 90, 50);
  sunLight.add(lensflare);
  scene.add(sunLight);

  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  camera.position.z = 500;

  controls = new OrbitControls(camera, renderers[0].domElement);
  camera.position.set(131.88064239814327, 64.1848257090222, 120.89015872538002);
  controls.target.set(-13.657160256049494, 14.83606980678348, 121.60382806417424);
  controls.screenSpacePanning = false;
  controls.enablePan = false;
  controls.dampingFactor = 0.05;
  controls.minPolarAngle =  1.2;
  controls.maxPolarAngle =  1.2;
  controls.minAzimuthAngle = -0.75;
  controls.maxAzimuthAngle = 1.2;
  controls.enableDamping = true;
  controls.autoRotate = false;
  controls.autoRotateSpeed *= 0.25;
  camera.lookAt(1, 1, 1);
  window.asd = (x, y, z) => {
    const rotation = new Euler(x, y, z, true);
    Globe.setRotationFromEuler(rotation);

  };
  Globe.setPointOfView(camera.position, Globe.position);
  controls.addEventListener('change', () => Globe.setPointOfView(camera.position, Globe.position));

  Promise.all(texturePromises).then(() => {
    // Initial camera and marker animation
    emits("allTextureLoaded");
    startInitialAnimation();
  });

  window.addEventListener('resize', onWindowResize, false);

}

function animate (){
  controls.update();
  renderers.forEach((r) => r.render(scene, camera));
  requestAnimationFrame(animate); // Enable mouse drag
}
onMounted(() => {

  init();
  animate();

});

document.addEventListener('click', function (event) {
  var myElement = document.getElementById(activeElm.value+"_id");
  var targetElement = event.target;
  var hidelem = document.getElementById(activeElm.value+"_CS");
  // Check if the clicked element is not the element itself or a descendant of the element
  if (myElement) {
    if (targetElement !== myElement && !myElement.contains(targetElement)) {
      hidelem.style.display = 'none';
    }
  }
});

const startAnimate = () => {
  Promise.all([
    animateCameraPosition(camera.position, { "x": 80.66770691334082, "y": 39.40691406419208, "z": 86.68052099645045 }, 2000, false),
    animateCameraPosition(controls.target, { "x": -0.14623203298954673, "y": 37.84872465037691, "z": -0.879793351262042 }, 2000, false),
    animateOpacity(globeViz.value, { opacity: 1 }, { opacity: 0 }, 2000),
  ]).then(() => {
    emits('selectedAnimationEnd');
  });
};

onBeforeUnmount(() => {

  if (globeMesh.value) {
    scene.traverse(function (obj) {
      if (obj.material) {
        obj.material.dispose();
        if (obj.material.map) {
          obj.material.map.dispose();
        }
        if (obj.material.lightMap) {
          obj.material.lightMap.dispose();
        }
        if (obj.material.aoMap) {
          obj.material.aoMap.dispose();
        }
        if (obj.material.emissiveMap) {
          obj.material.emissiveMap.dispose();
        }
        if (obj.material.bumpMap) {
          obj.material.bumpMap.dispose();
        }
        if (obj.material.normalMap) {
          obj.material.normalMap.dispose();
        }
        if (obj.material.displacementMap) {
          obj.material.displacementMap.dispose();
        }
        if (obj.material.roughnessMap) {
          obj.material.roughnessMap.dispose();
        }
        if (obj.material.metalnessMap) {
          obj.material.metalnessMap.dispose();
        }
        if (obj.material.alphaMap) {
          obj.material.alphaMap.dispose();
        }
      }
      if (obj.geometry) {
        obj.geometry.dispose();
        obj.geometry.attributes.color = {};
        obj.geometry.attributes.normal = {};
        obj.geometry.attributes.position = {};
        obj.geometry.attributes.uv = {};
        obj.geometry.attributes = {};
        obj.material = {};
      }
    });
  }

  for (const elem in Cache.files) {
    Cache.files[elem] = "";
    Cache.remove(elem);
  }

  cancelAnimationFrame(animate);

  renderers[0].dispose();
  controls.dispose();
  TWEEN.removeAll();
  Globe._destructor();
  lensflare.dispose();

  camera = null;
  scene = null;
  Globe = null;
  lensflare = null;
  renderers = [];
  globeMesh.value = null;
  globeMesh.value = null;
  svgIcon.value = null;

  window.removeEventListener('resize', onWindowResize);

});

defineExpose({
  startAnimate,
  startInitialAnimation,
});
</script>
<template>
  <div
    id="globeViz"
    ref="globeViz"
    style="height:100%;width:100%;overflow: hidden;cursor:grab;"
  />
</template>
<style>
/* Container */
/* .earthLabelContainer{
  background: rgba( 0,  0, 0, 1);
  opacity: 0.4;
  backdrop-filter: blur(19px);
  -webkit-backdrop-filter: blur(19px);
}

.earthLabelContainerActive{
  background: white;
  color: black;
  opacity: 1;
  backdrop-filter: blur(0px);
} */

/* After Shape */
/* .earthLabelContainer::after {
     content:'\2726';
  position:absolute;
  bottom:-46px;
  left:11px;
  font-size:70px;
  color: rgba(0, 0, 0, 1);
  opacity:1;
  transform:rotate(0deg);
  z-index:1;
}

.earthLabelContainerActive::after{
  color: white;
  content:'\2726';
  position:absolute;
  bottom:-46px;
  left:11px;
  font-size:70px;
  opacity:1;
  transform:rotate(0deg);
  z-index:1;
}

.earthLabelContainerActive svg{
  fill: black;
}
.earthLabelContainer svg{
  fill: #D4D4D4;
} */
</style>
