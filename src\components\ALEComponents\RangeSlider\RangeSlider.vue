<script setup>
import { defineEmits, ref, watch } from 'vue';

const props = defineProps({
  totalCountOfFrame: Number,
});
const sliderValue = ref(props.totalCountOfFrame);
const emit = defineEmits(['update:sliderValue']);
const intervalId = ref(null);
const isPlaying = ref(false);
const isDragging = ref(false);

watch(sliderValue, (newValue) => {
  sliderValue.value = newValue;
  emit('update:sliderValue', newValue);

});

function increaseSliderValue () {
  if (sliderValue.value < props.totalCountOfFrame - 1) {
    sliderValue.value += 1;
    // console.log("Slider value if: " + sliderValue.value);
  } else {
    sliderValue.value = 0;
    // console.log("Slider value else: " + sliderValue.value);
  }
}

function startPlayback () {
  const totalAnimationTime = 9000; // 2 ms in microseconds
  const delayPerFrame = totalAnimationTime / (props.totalCountOfFrame - 1);

  if (intervalId.value) {
    return;
  }
  intervalId.value = setInterval(increaseSliderValue, delayPerFrame);
  isPlaying.value = true;
}

function stopPlayback () {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
  isPlaying.value = false;
}
function onInput () {
  isDragging.value = false;
  if (isPlaying.value) {
    stopPlayback();
  }
}
const togglePlayback = () => (isPlaying.value ? stopPlayback() : startPlayback());
// startPlayback();
</script>

<template>
  <div class="flex items-center gap-4">
    <div
      class="flex w-10 h-10 items-center cursor-pointer justify-center border border-none rounded-full bg-secondary"
      @click="togglePlayback"
    >
      <button>
        <svg
          v-if="!isPlaying"
          xmlns="http://www.w3.org/2000/svg"
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M15.4137 13.059L10.6935 15.8458C9.93371 16.2944 9 15.7105 9 14.7868V9.21316C9 8.28947 9.93371 7.70561 10.6935 8.15419L15.4137 10.941C16.1954 11.4026 16.1954 12.5974 15.4137 13.059Z"
            fill="#1f2a37"
          />
        </svg>
        <svg
          v-if="isPlaying"
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 16 16"
          fill="none"
        >
          <path
            d="M4.79961 0H3.73294C2.55474 0 1.59961 0.89543 1.59961 2V14C1.59961 15.1046 2.55474 16 3.73294 16H4.79961C5.97782 16 6.93294 15.1046 6.93294 14V2C6.93294 0.89543 5.97782 0 4.79961 0Z"
            fill="#1f2a37"
          />
          <path
            d="M12.2663 0H11.1996C10.0214 0 9.06628 0.89543 9.06628 2V14C9.06628 15.1046 10.0214 16 11.1996 16H12.2663C13.4445 16 14.3996 15.1046 14.3996 14V2C14.3996 0.89543 13.4445 0 12.2663 0Z"
            fill="#1f2a37"
          />
        </svg>
      </button>
    </div>

    <input
      v-model="sliderValue"
      :min="0"
      :max="totalCountOfFrame - 1"
      type="range"
      class="w-44 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
      :style="{
        background: `linear-gradient(to right, #ffffff ${sliderValue * 100 / (totalCountOfFrame - 1)}%, #b4b4b4c9 ${sliderValue * 100 / (totalCountOfFrame - 1)}%)`,
      }"
      @input="onInput"
      @change="onDragEnd"
    >
    <!-- <div class="flex w-9 h-9 items-center justify-center border border-none rounded-full bg-secondary">
      <button>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="22"
          height="22"
          viewBox="0 0 22 22"
          fill="none"
        >
          <g
            opacity="0.26"
            filter="url(#filter0_df_477_3429)"
          >
            <ellipse
              cx="16.8414"
              cy="8.06653"
              rx="0.0816013"
              ry="0.0850535"
              fill="black"
            />
          </g>
          <path
            d="M11.5984 12.9365C12.297 12.9365 12.8132 12.9643 13.4562 13.0165M14.9929 13.1966C17.4336 13.5782 19.0984 14.3405 19.0984 15.2186C19.0984 16.479 15.6687 17.5007 11.4379 17.5007C7.20709 17.5007 3.77734 16.479 3.77734 15.2186C3.77734 14.6018 4.59868 14.0422 5.93331 13.6315M8.58239 13.1003C8.14423 13.1528 7.7261 13.217 7.33239 13.2916"
            stroke="black"
            stroke-width="0.388889"
          />
          <path
            d="M8.58239 13.0997C8.14423 13.1522 7.7261 13.2164 7.33239 13.291M5.93331 13.6309C4.59868 14.0416 3.77734 14.6012 3.77734 15.218C3.77734 16.4784 7.20709 17.5001 11.4379 17.5001C12.1365 17.5001 12.8132 17.4723 13.4562 17.4201"
            stroke="black"
            stroke-width="0.388889"
          />
          <g filter="url(#filter1_d_477_3429)">
            <path
              d="M16.0371 11.8473C14.9885 11.8473 14.1354 10.9928 14.1354 9.94255V8.10654C14.2402 5.57903 17.8351 5.58097 17.9389 8.10654V9.94258C17.9389 10.9928 17.0858 11.8473 16.0371 11.8473ZM16.0371 7.18363C15.5298 7.18363 15.117 7.59764 15.117 8.10654V9.94258C15.1677 11.1673 16.9071 11.1663 16.9573 9.94258V8.10654C16.9573 7.59764 16.5445 7.18363 16.0371 7.18363ZM13.252 9.9375C13.2587 8.5072 11.6659 7.5766 10.4301 8.28091V8.10654C10.4301 7.59766 10.8429 7.18363 11.3503 7.18363C11.8621 7.18363 12.2386 7.44756 12.2424 7.45027C12.5566 7.69769 13.0351 7.46443 13.0312 7.06088C13.0312 6.87403 12.9268 6.71164 12.7732 6.62868C12.5986 6.51166 12.0684 6.20178 11.3503 6.20178C10.3017 6.20178 9.44854 7.05626 9.44854 8.10652V9.94744C9.44854 9.94911 9.44854 9.95082 9.44857 9.95249C9.56444 12.4669 13.1371 12.465 13.252 9.95249C13.252 9.95082 13.252 9.94911 13.252 9.94744V9.94255C13.252 9.94088 13.252 9.93919 13.252 9.9375ZM11.3503 10.8655C10.8437 10.8655 10.4314 10.4528 10.4301 9.94501C10.4835 8.72355 12.2176 8.72447 12.2705 9.94501C12.2692 10.4528 11.8569 10.8655 11.3503 10.8655ZM7.9817 9.04116C8.29677 8.7529 8.51608 8.35452 8.51608 7.90645C8.51608 6.96652 7.74883 6.20183 6.80574 6.20183C6.09937 6.20183 5.59969 6.61606 5.54504 6.66328L5.5461 6.66451C5.44157 6.75454 5.37514 6.88758 5.37514 7.03638C5.37337 7.45957 5.88392 7.68362 6.19311 7.40152C6.2452 7.36185 6.49648 7.18365 6.80574 7.18365C7.20758 7.18365 7.53453 7.5079 7.53453 7.90645C7.53453 8.24051 7.10629 8.53366 6.84745 8.53366C6.5764 8.53366 6.35668 8.75344 6.35668 9.02457C6.35668 9.2957 6.5764 9.51548 6.84745 9.51548C6.91967 9.51548 7.10278 9.57592 7.28667 9.73808C7.43724 9.87083 7.53453 10.0297 7.53453 10.1427C7.53453 10.5412 7.20761 10.8655 6.80574 10.8655C6.4965 10.8655 6.24523 10.6873 6.19311 10.6476C5.88373 10.3654 5.37337 10.5896 5.37511 11.0127C5.37511 11.1615 5.44154 11.2946 5.54608 11.3846L5.54502 11.3858C5.59967 11.4331 6.09935 11.8473 6.80571 11.8473C7.74881 11.8473 8.51605 11.0826 8.51605 10.1427C8.51608 9.72756 8.29428 9.33473 7.9817 9.04116ZM9.7927 15.7501C9.77177 15.7501 9.814 15.7528 9.7927 15.7501V15.7501ZM17.3255 5.71089C17.6643 5.71089 17.9389 5.43615 17.9389 5.09725C17.9066 4.28327 16.7442 4.28349 16.712 5.09725C16.712 5.43615 16.9866 5.71089 17.3255 5.71089ZM13.6195 15.6452C13.597 15.6482 13.6416 15.6452 13.6195 15.6452V15.6452Z"
              fill="black"
            />
          </g>
          <defs>
            <filter
              id="filter0_df_477_3429"
              x="16.6222"
              y="7.84386"
              width="0.439292"
              height="0.445334"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood
                flood-opacity="0"
                result="BackgroundImageFix"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset />
              <feGaussianBlur stdDeviation="0.0688073" />
              <feComposite
                in2="hardAlpha"
                operator="out"
              />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.917647 0 0 0 0 0.827451 0 0 0 0 0.0470588 0 0 0 1 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_477_3429"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect1_dropShadow_477_3429"
                result="shape"
              />
              <feGaussianBlur
                stdDeviation="0.0229358"
                result="effect2_foregroundBlur_477_3429"
              />
            </filter>
            <filter
              id="filter1_d_477_3429"
              x="2.68269"
              y="4.48685"
              width="17.9491"
              height="16.6491"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood
                flood-opacity="0"
                result="BackgroundImageFix"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="2.69231" />
              <feGaussianBlur stdDeviation="1.34615" />
              <feComposite
                in2="hardAlpha"
                operator="out"
              />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.06 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_477_3429"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect1_dropShadow_477_3429"
                result="shape"
              />
            </filter>
          </defs>
        </svg>
      </button>
    </div> -->
  </div>
</template>

<style scoped>

.slider::-webkit-slider-thumb {
    -webkit-appearance: none; /* Override default appearance */
    appearance: none; /* Override default appearance */
    width: 18px; /* Width of the thumb */
    height: 18px; /* Height of the thumb */
    background: var(--primary); /* Thumb color */
    border-radius: 50%; /* Rounded thumb */
    cursor: pointer; /* Change cursor to pointer */
}
svg path{
  stroke:var(--secondaryText);
}

</style>
