<script setup>
import { defineEmits, ref, computed, onMounted, onUnmounted } from 'vue';
import { sidebar_icons } from "../../../config/masterdata";
import { creationToolStore } from '../../../store';
import { Googleanalytics } from '../../../helpers/helper';
import { onClickOutside } from '@vueuse/core';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const Store = creationToolStore();
const list = ref();
const emit = defineEmits(['selectOption', 'closeX']);

const props = defineProps({
  sidebarList: {type: Object, default () {
    return {};
  }},
});

const startTime = ref(new Date), timespent = ref(0);
const showMore = ref(false);
const morePanelRef = ref(null);

const isMoreMenuOpen = computed({
  get: () => showMore.value,
  set: (val) => {
    showMore.value = val;
  },
});

const availableHeight = ref(window.innerHeight * 0.85); // 85vh as per your sidebar

const updateAvailableHeight = () => {
  availableHeight.value = window.innerHeight * 0.85;
};

onMounted(() => {
  window.addEventListener('resize', updateAvailableHeight);
});
onUnmounted(() => {
  window.removeEventListener('resize', updateAvailableHeight);
});

function selectOption (id, scene_id, type){
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  timespent.value = difference;
  startTime.value=new Date();
  Googleanalytics("menu_clicked", {
    menu_name: props.sidebarList[id].name,
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  Store.activeOptionId = id;
  emit('selectOption', scene_id, type);
  isMoreMenuOpen.value = false;
}

const initialSidebarList = computed(() => {
  const list = Object.values(props.sidebarList);
  if (Store.isLandscape) {
    // Show only first 4 in sidebar, rest go to More
    return list.slice(0, 3);
  }
  if (Store.isMobile) {
    return list.slice(0, 4);
  }
  return list.slice(0, 5);
});

const additionalSidebarList = computed(() => {
  if (Store.isLandscape) {
    return Object.values(props.sidebarList).slice(3);
  }
  if (Store.isMobile) {
    return Object.values(props.sidebarList).slice(4);
  }
  return Object.values(props.sidebarList).slice(5);
});

const isAnyAdditionalItemSelected = computed(() => {
  return additionalSidebarList.value.some((item) => item._id === Store.activeOptionId);
});

const gridClasses = computed(() => {
  const visibleCount = initialSidebarList.value.length;
  const hasMoreButton = additionalSidebarList.value.length > 0;
  const totalVisible = hasMoreButton ? visibleCount + 1 : visibleCount;
  if (Store.isLandscape) {
    // Estimate how many items can fit
    const availableHeight = window.innerHeight - parseInt(getComputedStyle(document.documentElement).getPropertyValue('--navbar-height') || 56);
    const itemHeight = 48; // px, reduced for compact sidebar
    const canFit = totalVisible * itemHeight <= availableHeight;
    if (canFit) {
      const columnClasses = { 5: 'grid-rows-5', 4: 'grid-rows-4', 3: 'grid-rows-3', 2: 'grid-rows-2', 1: 'grid-rows-1', 0: 'grid-rows-0' };
      return 'grid ' + (columnClasses[totalVisible] || '');
    }
    return 'flex flex-col';

  }
  const columnClasses = { 5: 'grid-cols-5', 4: 'grid-cols-4', 3: 'grid-cols-3', 2: 'grid-cols-2', 1: 'grid-cols-1', 0: 'grid-cols-0' };
  return 'grid ' + columnClasses[totalVisible];
});

onClickOutside(list, () => showMore.value = false);
onClickOutside(morePanelRef, () => {
  if (isMoreMenuOpen.value) {
    isMoreMenuOpen.value = false;
  }
});
</script>

<template>
  <div>
    <!-- Landscape Sidebar -->
    <div
      v-if="Store.isLandscape"
      class="fixed z-50 right-0"
      :style="{ top: `var(--navbar-height, 56px)`, height: `calc(100vh - var(--navbar-height, 56px))` }"
    >
      <div
        :class="['relative z-[1] font-medium bg-secondary h-full overflow-y-auto', gridClasses, Store.isLandscape ? 'flex flex-col justify-between' : '', (!Store.isFullScreen && Store.isLandscape) ? 'pb-14' : '']"
      >
        <div
          v-for="item in initialSidebarList"
          :key="item._id"
          class="flex flex-col py-1.5 px-1 gap-1 hover:cursor-pointer justify-center"
          :class="Store.activeOptionId === item._id ? 'bg-primary' : 'bg-transparent hover:bg-tertiary50opacity'"
          @click="selectOption(item._id, item.scene_id, item.type)"
        >
          <button
            type="button"
            class="flex flex-col items-center justify-center gap-1"
          >
            <span
              class="h-5 w-6 flex items-center justify-center"
              :class="Store.activeOptionId === item._id ? 'activeStrokeClass' : 'strokeclass'"
              v-html="Store.activeOptionId === item._id ? sidebar_icons[item.icon_id].active : sidebar_icons[item.icon_id].inactive"
            />
            <p
              :class="['text-xs font-medium text-center capitalize truncate w-[70px] overflow-hidden text-ellipsis px-1', Store.activeOptionId === item._id ? 'text-primaryText' : 'text-secondaryText']"
            >
              <TranslationComp :text="item.name" />
            </p>
          </button>
        </div>
        <!-- More Button for Landscape -->
        <div
          v-if="additionalSidebarList.length > 0"
          class="w-[78px] min-h-[70px]  flex flex-col py-4 px-1 gap-2 items-center justify-center transition-colors hover:cursor-pointer"
          :class="isAnyAdditionalItemSelected ? 'bg-primary' : 'bg-secondary'"
          @click="isMoreMenuOpen = !isMoreMenuOpen"
        >
          <button
            type="button"
            class="flex flex-col items-center justify-center gap-1"
          >
            <span>
              <svg
                class="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 21 21"
              >
                <path
                  d="M3.83398 4.66663H17.1673"
                  :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M3.83398 10.5H17.1673"
                  :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M3.83398 16.3334H17.1673"
                  :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>
            <p
              :class="['text-xs font-medium text-center capitalize', isAnyAdditionalItemSelected ? 'text-primaryText' : 'text-secondaryText']"
            >
              <TranslationComp text="More" />
            </p>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile Sidebar -->
    <div
      v-else-if="Store.isMobile"
      class="fixed z-[9] bottom-0 w-full"
    >
      <div
        class="relative z-[1] font-medium bg-secondary grid"
        :class="gridClasses"
      >
        <div
          v-for="item in initialSidebarList"
          ref="list"
          :key="item._id"
          class="flex flex-col py-3 px-1 gap-2 sm:gap-1 hover:cursor-pointer"
          :class="[Store.activeOptionId === item._id ? 'bg-primary hover:!bg-primary' : 'hover:bg-tertiary50opacity']"
          @click="selectOption(item._id, item.scene_id, item.type)"
        >
          <button
            type="button"
            class="inline-flex flex-col gap-2 items-center justify-center px-2 group"
          >
            <span
              class="h-6 w-6 flex items-center justify-center"
              :class="Store.activeOptionId == item._id ? 'activeStrokeClass' : 'strokeclass'"
              v-html="Store.activeOptionId == item._id ? sidebar_icons[item.icon_id].active : sidebar_icons[item.icon_id].inactive"
            /><p :class="['text-xs w-full font-medium text-center capitalize overflow-hidden whitespace-nowrap text-ellipsis max-w-[3.5rem]', Store.activeOptionId == item._id ? 'text-primaryText' : 'text-secondaryText brightness-75']">
              <TranslationComp :text="item.name" />
            </p>
          </button>
        </div>
        <!-- More Button for Mobile -->
        <div
          v-if="additionalSidebarList.length > 0"
          class="flex flex-col py-3 px-1 gap-2 sm:gap-1 hover:bg-tertiary50opacity hover:cursor-pointer"
          :class="isAnyAdditionalItemSelected ? 'hover:bg-primary bg-primary' : ' bg-secondary'"
          @click="isMoreMenuOpen = !isMoreMenuOpen"
        >
          <button
            type="button"
            class="inline-flex flex-col gap-2 items-center justify-center px-2 group"
          >
            <span><svg
              class="h-6 w-6"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 21 21"
            ><path
              d="M3.83398 4.66663H17.1673"
              :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            /><path
              d="M3.83398 10.5H17.1673"
              :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            /><path
              d="M3.83398 16.3334H17.1673"
              :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            /></svg>
            </span>
            <p :class="['text-xs font-medium text-center capitalize', isAnyAdditionalItemSelected ? 'text-primaryText' : 'text-secondaryText']">
              <TranslationComp text="More" />
            </p>
          </button>
        </div>
      </div>
    </div>
    <!-- Desktop Sidebar (no more button/overlay) -->
    <div
      v-else
      class="fixed z-50 bottom-0 w-full sm:h-max sm:left-9 sm:w-fit sm:rounded-lg sm:top-2/4 sm:-translate-y-2/4"
    >
      <div
        class="relative z-[11] font-medium bg-secondary grid"
        :class="[gridClasses, 'sm:flex sm:flex-col sm:max-h-fit sm:max-w-lg sm:mx-auto sm:rounded-lg']"
      >
        <div
          v-for="(item, index) in initialSidebarList"
          ref="list"
          :key="item._id"
          class="flex flex-col py-4 px-1 gap-2 sm:gap-1 hover:cursor-pointer"
          :class="[
            Store.activeOptionId === item._id ? 'bg-primary hover:!bg-primary' : 'hover:bg-tertiary50opacity',
            index === 0 ? 'rounded-t-lg' : (index === initialSidebarList.length - 1 && additionalSidebarList.length === 0 ? 'rounded-b-lg' : 'rounded-b-none')
          ]"
          @click="selectOption(item._id, item.scene_id, item.type)"
        >
          <button
            type="button"
            class="inline-flex flex-col gap-2 items-center justify-center px-2 group"
          >
            <span
              class="h-6 w-6 flex items-center justify-center"
              :class="Store.activeOptionId == item._id ? 'activeStrokeClass' : 'strokeclass'"
              v-html="Store.activeOptionId == item._id ? sidebar_icons[item.icon_id].active : sidebar_icons[item.icon_id].inactive"
            />
            <p :class="['text-xs sm:text-sm w-full font-medium text-center capitalize overflow-hidden whitespace-nowrap text-ellipsis max-w-[3.5rem] sm:max-w-[4rem]', Store.activeOptionId == item._id ? 'text-primaryText' : 'text-secondaryText brightness-75']">
              <TranslationComp :text="item.name" />
            </p>
          </button>
        </div>
        <!-- Desktop More Button -->
        <div
          v-if="additionalSidebarList.length > 0"
          class="flex flex-col py-4 px-1 gap-2 hover:cursor-pointer hover:bg-tertiary50opacity rounded-b-lg"
          :class="isAnyAdditionalItemSelected ? 'bg-primary' : 'bg-secondary'"
          @click="isMoreMenuOpen = !isMoreMenuOpen"
        >
          <button
            type="button"
            class="inline-flex flex-col gap-2 items-center justify-center px-2 group"
          >
            <span>
              <svg
                class="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 21 21"
              >
                <path
                  d="M3.83398 4.66663H17.1673"
                  :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M3.83398 10.5H17.1673"
                  :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M3.83398 16.3334H17.1673"
                  :stroke="isAnyAdditionalItemSelected ? 'var(--primaryText)' : 'var(--secondaryText)'"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>
            <p :class="['text-xs sm:text-sm font-medium text-center capitalize', isAnyAdditionalItemSelected ? 'text-primaryText' : 'text-secondaryText']">
              <TranslationComp text="More" />
            </p>
          </button>
        </div>
      </div>
    </div>
    <!-- More Menu overlay (Landscape & Mobile only) -->
    <div
      v-if="isMoreMenuOpen && (Store.isLandscape || Store.isMobile)"
      ref="morePanelRef"
      :class="['fixed flex flex-col duration-100', Store.isLandscape ? 'z-[9] bg-secondary backdrop-blur-md rounded-none shadow-lg' : 'top-[3.5rem] left-0 z-[10] h-full w-full bg-secondary bg-opacity-40 backdrop-blur-[20px]']"
      :style="Store.isLandscape ? { top: `var(--navbar-height, 56px)`, right: '78px', width: '80vw', height: `calc(100vh - var(--navbar-height, 56px))` } : {}"
    >
      <div :class="['flex flex-row justify-between items-center w-full', Store.isLandscape ? 'p-2' : 'p-4']">
        <span class="text-lg font-medium text-secondaryText"><TranslationComp text="More" /></span>
        <span><button
          type="button"
          :class="['flex-shrink-0 inline-flex justify-center w-7 h-7 items-center text-sm p-1.5', Store.isLandscape ? 'text-secondaryText  rounded-lg' : 'text-secondaryText  rounded-lg hover:bg-secondary']"
          @click="() => { isMoreMenuOpen = false; if (!Store.isLandscape) $emit('closeX'); }"
        >
          <svg
            class="w-3 h-3"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 14 14"
          ><path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
          />
          </svg>
          <span class="sr-only">
            Close banner</span></button>
        </span>
      </div>
      <div :class="{'flex-1 overflow-y-auto': Store.isLandscape}">
        <div
          v-for="item in additionalSidebarList"
          :key="item._id"
          :class="['flex flex-col hover:cursor-pointer', Store.isLandscape ? 'py-3 hover:bg-tertiary50opacity' : 'border border-none rounded-lg py-4 hover:bg-secondary', Store.activeOptionId === item._id && (Store.isLandscape ? 'bg-tertiary50opacity' : 'bg-secondary')]"
          @click="selectOption(item._id, item.scene_id, item.type)"
        >
          <div :class="['flex px-4 items-center gap-4 text-sm w-full font-medium capitalize', Store.isLandscape ? (Store.activeOptionId === item._id ? 'text-secondaryText' : 'text-secondaryText') : 'text-center overflow-hidden whitespace-nowrap text-ellipsis', !Store.isLandscape && (Store.activeOptionId == item._id ? 'text-primary' : 'text-secondaryText brightness-75')]">
            <span
              :class="['h-6 w-6 flex items-center justify-center text-sm', !Store.isLandscape && (Store.activeOptionId == item._id ? 'fill-primary' : 'strokeclass')]"
              v-html="Store.isLandscape || Store.activeOptionId !== item._id ? sidebar_icons[item.icon_id].inactive : sidebar_icons[item.icon_id].active"
            />
            <TranslationComp :text="item.name" />
          </div>
        </div>
      </div>
    </div>
    <!-- Desktop More Overlay -->
    <div
      v-if="isMoreMenuOpen && !Store.isMobile && !Store.isLandscape"
      ref="morePanelRef"
      class="fixed  top-[80%] z-[100] left-[calc(2.5rem+90px)] bg-secondary rounded-lg shadow-lg  border border-gray-200"
      :class="!Store.isMobile && !Store.isLandscape ? 'min-w-[240px] max-w-[250px]' : ''"
      style="transform: translateY(-50%);"
    >
      <div>
        <div
          v-for="(item, idx) in additionalSidebarList"
          :key="item._id"
          class="flex flex-row items-center gap-3 py-3 px-2 hover:bg-tertiary50opacity cursor-pointer"
          :class="[
            Store.activeOptionId === item._id ? 'bg-primary text-primaryText' : 'text-secondaryText',
            idx === 0 ? 'rounded-t-lg' : '',
            idx === additionalSidebarList.length - 1 ? 'rounded-b-lg' : ''
          ]"
          @click="selectOption(item._id, item.scene_id, item.type)"
        >
          <span
            class="h-6 w-6 flex items-center justify-center"
            :class="Store.activeOptionId == item._id ? 'activeStrokeClass' : 'strokeclass'"
            v-html="sidebar_icons[item.icon_id].inactive"
          />
          <TranslationComp :text="item.name" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.textColors{
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent;
}

.invisibleScroll::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

.invisibleScroll::-webkit-scrollbar-track {
  background: transparent;
}
</style>

<style lang="scss">
.strokeclass svg path {
  stroke:var(--secondaryText);
}
.activeStrokeClass svg path{
  fill: var(--primaryText);
}
</style>
