<script  setup>
import {  defineProps } from 'vue';
import LandMarkLayer from "./Layers/LandmarkLayer.vue";
import RadiusLayer from "./Layers/RadiusLayer.vue";
import ProjectLayer from "./Layers/ProjectLayer.vue";
import pinLayer from './Layers/pinLayer.vue';
import AmenityLayer from './Layers/amenityLayer.vue';
import communityLayer from './Layers/communityLayer.vue';
import clubhouseLayer from './Layers/clubhouseLayer.vue';
import towerLayer from './Layers/towerLayer.vue';
import droneLayer from './Layers/droneLayer.vue';
import floorLayer from './Layers/floorLayer.vue';
import unitLayer from './Layers/unitLayer.vue';
import unavailableLayer from './Layers/unavailableLayer.vue';
import labelLayer from './Layers/labelLayer.vue';
import amenitycategoryLayer from './Layers/amenitycategoryLayer.vue';
import StaticLayer from './Layers/staticLayer.vue';
import GroupedUnitsLayer from './Layers/groupedUnitsLayer.vue';
import ZoomTargetLayer from './Layers/zoomTargetLayer.vue';

const props = defineProps({
  'layerData': {
    type: Array,
    default () {
      return [];
    },
  },
  'sceneType': {
    type: String,
    default: '',
  },
});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <component
    is="style"
    v-for="style in layerData['style']"
    v-show="props.sceneType!=='deep_zoom'"
    :key="style"
    v-html="`${style.layer.innerHTML}`"
  />
  <!--eslint-enable-->
  <RadiusLayer
    v-if="props.layerData['radius']"
    :layer-data="props.layerData['radius']"
    :sceneType="props.sceneType"
  />
  <ProjectLayer
    v-if="props.layerData['project']"
    :layer-data="props.layerData['project']"
    :sceneType="props.sceneType"
  />
  <communityLayer
    v-if="props.layerData['community']"
    :layer-data="props.layerData['community']"
    :sceneType="props.sceneType"
  />
  <clubhouseLayer
    v-if="props.layerData['clubhouse']"
    :layer-data="props.layerData['clubhouse']"
    :sceneType="props.sceneType"
  />
  <towerLayer
    v-if="props.layerData['building']"
    :layer-data="props.layerData['building']"
    :sceneType="props.sceneType"
  />
  <droneLayer
    v-if="props.layerData['drone']"
    :layer-data="props.layerData['drone']"
    :sceneType="props.sceneType"
  />
  <floorLayer
    v-if="props.layerData['floor']"
    :layer-data="props.layerData['floor']"
    :sceneType="props.sceneType"
  />
  <unitLayer
    v-if="props.layerData['units']"
    :layer-data="props.layerData['units']"
    :sceneType="props.sceneType"
  />
  <AmenityLayer
    v-if="props.layerData['amenity']"
    :layer-data="props.layerData['amenity']"
    :sceneType="props.sceneType"
  />
  <unavailableLayer
    v-if="props.layerData['unavailable']"
    :layer-data="props.layerData['unavailable']"
    :sceneType="props.sceneType"
  />
  <labelLayer
    v-if="props.layerData['label']"
    :layer-data="props.layerData['label']"
    :sceneType="props.sceneType"
  />
  <amenitycategoryLayer
    v-if="props.layerData['amenitycategory']"
    :layer-data="props.layerData['amenitycategory']"
    :sceneType="props.sceneType"
  />
  <StaticLayer
    v-if="props.layerData['static']"
    :layer-data="props.layerData['static']"
    :sceneType="props.sceneType"
  />
  <LandMarkLayer
    v-if="props.layerData['landmark']"
    :layer-data="props.layerData['landmark']"
    :route-data="props.layerData['route']?props.layerData['route']:{}"
    :sceneType="props.sceneType"
  />
  <pinLayer
    v-if="props.layerData['pin']"
    :layer-data="props.layerData['pin']"
    :sceneType="props.sceneType"
  />
  <GroupedUnitsLayer
    v-if="props.layerData['grouped_units']"
    :layer-data="props.layerData['grouped_units']"
    :sceneType="props.sceneType"
  />
  <ZoomTargetLayer
    v-if="props.layerData['zoom_target']"
    :layer-data="props.layerData['zoom_target']"
    :sceneType="props.sceneType"
  />
</template>
