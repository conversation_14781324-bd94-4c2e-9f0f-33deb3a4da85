import FavoriteView from './FavoriteView.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/FavoriteView',
  component: FavoriteView,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    property: {
      'A-1': {
        'status': "Available",
        'currency': 'USD',
        'price': 552500,
        'Tower Name': 'Tower A',
        'unit_id': 'A-1',
        'floor': 1,
        'type': '2 BHK',
        'measurement_type': 'Sq.ft',
        'measurement': 1056,
        'bedrooms': 1,
        'bathroom': 1,
        'is_furnished': 'North-East',

      },
      'A-2': {
        'status': "Available",
        'currency': 'GBP',
        'price': 45090923,
        'Tower Name': 'Tower B',
        'unit_id': 'A-2',
        'floor': 1,
        'type': '2 BHK',
        'measurement_type': 'Sq.ft',
        'measurement': 1056,
        'bedrooms': 1,
        'bathroom': 1,
        'is_furnished': 'North-East',

      },
      'A-3': {
        'status': "Available",
        'currency': 'EUR',
        'price': 45090923,
        'Tower Name': 'Tower C',
        'unit_id': 'A-3',
        'floor': 1,
        'type': '2 BHK',
        'measurement_type': 'Sq.ft',
        'measurement': 1056,
        'bedrooms': 1,
        'bathroom': 1,
        'is_furnished': 'North-East',

      },
      'A-4': {
        'status': "Available", // Status
        'currency': 'INR', // Currency
        'price': 2343,  // Price
        'Tower Name': 'Tower D',      // Building_id  building_name
        'unit_id': 'A-4', // Unit_id
        'floor': 1, // Floor
        'type': '2 BHK', // Type
        'measurement_type': 'Sq.ft', // Measurement_type
        'measurement': 1056, // Measurement
        'bedrooms': 1, // Bedrooms
        'bathroom': 1, // Bathroom
        'is_furnished': 'North-East', // Is_furnished
      },
      'A-5': {
        'status': "Available",
        'currency': 'USD',
        'price': 7998,
        'Tower Name': 'Tower E',
        'unit_id': 'A-5',
        'floor': 1,
        'type': '2 BHK',
        'measurement_type': 'Sq.ft',
        'measurement': 1056,
        'bedrooms': 1,
        'bathroom': 1,
        'is_furnished': 'North-East',

      },
      'A-6': {
        'status': "Available",
        'currency': 'USD',
        'price': 1223,
        'Tower Name': 'Tower f',
        'unit_id': 'A-6',
        'floor': 1,
        'type': '2 BHK',
        'measurement_type': 'Sq.ft',
        'measurement': 1056,
        'bedrooms': 1,
        'bathroom': 1,
        'is_furnished': 'North-East',

      },
      'A-7': {
        'status': "Available",
        'currency': 'USD',
        'price': 3445,
        'Tower Name': 'Tower g',
        'unit_id': 'A-7',
        'floor': 1,
        'type': '2 BHK',
        'measurement_type': 'Sq.ft',
        'measurement': 1056,
        'bedrooms': 1,
        'bathroom': 1,
        'is_furnished': 'North-East',

      },
      'A-8': {
        'status': "Available",
        'currency': 'USD',
        'price': 590,
        'Tower Name': 'Tower h',
        'unit_id': 'A-8',
        'floor': 1,
        'type': '2 BHK',
        'measurement_type': 'Sq.ft',
        'measurement': 1056,
        'bedrooms': 1,
        'bathroom': 1,
        'is_furnished': 'North-East',

      },
    },
  },
};
