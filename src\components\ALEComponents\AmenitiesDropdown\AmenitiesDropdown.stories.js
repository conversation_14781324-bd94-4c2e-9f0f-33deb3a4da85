import AmenitiesDropdown from './AmenitiesDropdown.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/AmenitiesDropdown',
  component: AmenitiesDropdown,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    title: "hello world",
    lists: [
      { id: 1, value: 'Club House' },
      { id: 2, value: 'Gym' },
      { id: 3, value: 'Swimming Pool' },
    ],
  },
};
