<script setup>
// Import PanoramaCard from '../components/ALEComponents/PanoramaCard/PanoramaCard.vue';
import { ref, defineEmits, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import router from '../router';
import AframeVideo360 from "../components/ALEComponents/AframeVideo360/AframeVideo360.vue";
// Import BreadCrumb from '../components/ALEComponents/BreadCrumb/BreadCrumb.vue';
import { creationToolStore } from '../store/index';

import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import { cdn, getSplashCookie, Googleanalytics, listenPost } from '../helpers/helper.js';
// Import Backbar from '../components/ALEComponents/BackBar/BackTopBar.vue';
import BlobImageLoader from '../components/ALEComponents/BlobImageLoader/BlobImageLoader.vue';
import OverflowSlider from '../components/ALEComponents/OverflowSlider/OverflowSlider.vue';
// Import LoaderComponent from '../components/ALEComponents/LoaderComponent/LoaderComponent.vue';
import GalleryVideoPlayer from '../components/ALEComponents/GalleryVideoPlayer/GalleryVideoPlayer.vue';
import AFrameComp from '../components/ALEComponents/AFrameComp/AframeComp.vue';
import { fetchImageURL } from '../helpers/API';
import PDFViewer from '../components/ALEComponents/PDFViewer/PDFViewer.vue';
import NavBar from '../components/ALEComponents/NavBar/NavBar.vue';
import PannellumMultiRes from '../components/ALEComponents/pannellumMultiRes/PannellumMultiRes.vue';
import DropDown from '../components/ALEComponents/DropDown/DropDown.vue';
import NearByFloatingButton from '../components/ALEComponents/NearByFloatingButton/NearByFloatingButton.vue';
import ProgressBar from '../components/ALEComponents/ProgressBar/ProgressBar.vue';
import TourView from '../components/ALEComponents/TourView/TourView.vue';

const route = useRoute();
const emit = defineEmits(['onClick']);
const props = defineProps({
  sceneId: { type: String, default: "" },
  organizationId: { type: String, default: "" },
  projectId: { type: String, default: "" },
  amenityId: { type: String, default: "" },
});

const selectedCategory = ref(null);
const Store = creationToolStore();
Store.getTranslation(route.params.organizationId);
const showSlider = ref(false);
// Const closeFullscreenModal = ref(false);
const circularloader = ref(false);
const selectionId = ref(route.name === 'amenity' ? route.query.id || null : route.params.amenityId);
const categoriesFilteredList = ref(null);
const currentSliderIndex = ref(null);
const showLoader = ref(true);
const initialIndex = ref(0);
const currentImageId = ref(null);

const leftButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.375 10.5L2.625 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7 14.875L2.625 10.5L7 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const rightButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.625 10.5L18.375 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14 14.875L18.375 10.5L14 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const loaderProgress = ref(null);
const categoryList = ref();
Store.hideLogo=true;
if (!Store.sidebarOptions[route.params.projectId]) {
  Store.getOptions(props.organizationId, route.params.projectId);
}
if (!Store.organization_thumbnail) {
  Store.getOrganization(props.organizationId);
}
if (!Store.listOfVrTourData) {
  Store.getListOfVRId(route.params.organizationId, route.params.projectId);
}
if (!Store.amenityCategories) {
  if (Store.projectCardData[route.params.projectId] && Store.projectCardData[route.params.projectId].projectSettings.amenity){
    const dataEntries = Object.values(Store.projectCardData[route.params.projectId].projectSettings.amenity);
    dataEntries.sort((a, b) => a.order - b.order);
    categoryList.value = dataEntries.map((obj) => obj.name);
  } else {
    Store.getCategories(props.organizationId, props.projectId).then(() => {
      categoryList.value=Store.amenityCategories;
      const keys = Object.keys(Store.amenityCardData);
      if (keys.length > 0) {
        fetchImageURL(cdn(Store.amenityCardData[keys[0]].thumbnail)).then(() => {
          Object.values(Store.amenityCardData).forEach((item) => {
            fetchImageURL(cdn(item.thumbnail));
          });
        });
      }
    });
  }

} else {
  if (Store.projectCardData[route.params.projectId] && Store.projectCardData[route.params.projectId].projectSettings.amenity){
    const dataEntries = Object.values(Store.projectCardData[route.params.projectId].projectSettings.amenity);
    dataEntries.sort((a, b) => a.order - b.order);
    categoryList.value = dataEntries.map((obj) => obj.name);
  } else {
    categoryList.value=Store.amenityCategories;
    const keys = Object.keys(Store.amenityCardData);
    if (keys.length > 0) {
      fetchImageURL(cdn(Store.amenityCardData[keys[0]].thumbnail)).then(() => {
        Object.values(Store.amenityCardData).forEach((item) => {
          fetchImageURL(cdn(item.thumbnail));
        });
      });
    }
  }
}
Store.updateSceneType(route.fullPath);
if (route.params.sceneId){
  const splashCookie = getSplashCookie();
  splashCookie[props.projectId] = true;
  if (Object.values(Store.SceneData).length === 0) {
    Store.getProjectScenes(route.params.organizationId, props.projectId).then(() => {
      Store.activeOptionId = Store.SceneData[route.params.sceneId].sceneData.category;
      if (Store.SceneData[route.params.sceneId].sceneData.root) {
        document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
      }
    });
  } else {
    Store.activeOptionId = Store.SceneData[route.params.sceneId].sceneData.category;
    if (Store.SceneData[route.params.sceneId].sceneData.root) {
      document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
    }
  }
} else {
  if (Object.values(Store.sidebarOptions).length === 0){
    Store.getOptions(route.params.organizationId, props.projectId).then(() => {
      Store.activeOptionId = Object.values(Store.sidebarOptions[route.params.projectId]).find((elem) => elem.type === 'amenity')._id;
    });
  } else {
    Store.activeOptionId = Object.values(Store.sidebarOptions[route.params.projectId]).find((elem) => elem.type === 'amenity')._id;
  }
}

setTimeout(() => {
  showSlider.value = true;
}, 1000);
if (Object.keys(Store.projectCardData).length === 0) {
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}

function groupBy (array, keyFn) {
  return array.reduce((result, item) => {
    const key = keyFn(item);
    (result[key] = result[key] || []).push(item);
    return result;
  }, {});
}

const initializeAmenities = async () => {
  if (Object.keys(Store.amenityCardData).length === 0){
    await Store.getListofAmenities(props.projectId, props.organizationId);
  }
  categoriesFilteredList.value = groupBy(Object.values(Store.amenityCardData), ({ category }) => category);
  if (route.name === 'amenity' && !selectionId.value){
    selectionId.value = categoriesFilteredList.value[Object.keys(categoriesFilteredList.value)[0]]?.[0]._id;
    selectedCategory.value = Store.amenityCardData[selectionId.value].category;
    currentSliderIndex.value = categoriesFilteredList.value[selectedCategory.value].findIndex((item) => item._id === selectionId.value) + 1;
    router.push({ name: 'amenity', params: { ...route.params }, query: { ...route.query, id: selectionId.value } });
  }
  selectedCategory.value = Store.amenityCardData[selectionId.value].category;
  currentSliderIndex.value = categoriesFilteredList.value[selectedCategory.value].findIndex((item) => item._id === selectionId.value) + 1;
  showLoader.value = false;

};
initializeAmenities();

watch(
  () => (route.name === 'amenity' ? route.query.id : route.params.amenityId),
  (val) => {
    if (val && val !== selectionId.value) {
      selectionId.value = val;
    }
  },
);

watch(
  () => Store.projectCardData[route.params.projectId],
  () => {
    if (Store.projectCardData[route.params.projectId].projectSettings.amenity){
      const dataEntries = Object.values(Store.projectCardData[route.params.projectId].projectSettings.amenity);
      dataEntries.sort((a, b) => a.order - b.order);
      categoryList.value = dataEntries.map((obj) => obj.name);
    }
  },
);
function moveToLocation (id, type){
  if (type==='masterscene'){
    router.push({name: 'masterScene', params: {sceneId: id} });
  } else if (type==='earth'){
    router.push({name: 'globeScene'});
  } else if (type==='projectscene') {
    router.push({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    router.push({ name: 'unitplansviewer'});
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push( { name: 'galleryview'} );
  }
}

function updatedImageId (id) {
  currentImageId.value = id;
}

const fullScreenChangeHandler = () => {
  if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
    emit('onClick');
    Store.isFullScreen = false;
  }
};

const handleVideoLoader = () => {
  setTimeout(() => {
    circularloader.value = false;
  }, 2000);
};

const handleAmenityCategoriesSelection = (item) => {
  if (item.category === selectedCategory.value) {
    return;
  }
  showLoader.value = true;
  initialIndex.value = 0;
  currentSliderIndex.value = 1;
  setTimeout(() => {
    selectedCategory.value = item.category;
    const firstAmenity = categoriesFilteredList.value[item.category][0];
    if (route.name === 'amenity') {
      router.push({ name: 'amenity', params: { ...route.params }, query: { ...route.query, id: firstAmenity._id } });
    } else {
      router.push({ name: 'amenityview', params: { ...route.params, amenityId: firstAmenity._id }, query: { ...route.query } });
    }
    showLoader.value = false;
  }, 400);
};

const handleSwiperSlideChange = (item, index) => {
  if (route.name === 'amenity') {
    router.push({ name: 'amenity', params: { ...route.params }, query: { ...route.query, id: item._id } });
  } else {
    router.push({ name: 'amenityview', params: { ...route.params, amenityId: item._id }, query: { ...route.query } });
  }
  currentSliderIndex.value = index + 1;
};

function slideTo (item) {
  initialIndex.value = categoriesFilteredList.value[selectedCategory.value].findIndex((element) => element._id === item.id);
  document.getElementById('amenityItemsSlider').swiper.slideTo(initialIndex.value, 0);
}

const handleHotspotClick = (linkData) => {
  console.log('Hotspot clicked:', linkData);

  // Check if the destination scene exists in the current amenity data
  const destinationScene = Object.values(Store.amenityCardData).find(
    item => item._id === linkData.destination_img_id
  );

  if (destinationScene) {
    // Navigate to the destination scene
    if (route.name === 'amenity') {
      router.push({
        name: 'amenity',
        params: { ...route.params },
        query: { ...route.query, id: destinationScene._id }
      });
    } else {
      router.push({
        name: 'amenityview',
        params: { ...route.params, amenityId: destinationScene._id },
        query: { ...route.query }
      });
    }

    // Update the current selection and slider
    selectedCategory.value = destinationScene.category;
    const newIndex = categoriesFilteredList.value[destinationScene.category].findIndex(
      item => item._id === destinationScene._id
    );
    currentSliderIndex.value = newIndex + 1;
    selectionId.value = destinationScene._id;

    // Slide to the new scene
    setTimeout(() => {
      if (document.getElementById('amenityItemsSlider')?.swiper) {
        document.getElementById('amenityItemsSlider').swiper.slideTo(newIndex, 300);
      }
    }, 100);
  } else {
    console.warn('Destination scene not found:', linkData.destination_img_id);
    // You could show a toast notification or handle this case differently
  }
};

function NearByFloatingButtonFilterData (data) {
  const list = [];
  for (let i=0; i<data.length; i++){
    if (data[i]?.media_type){
      if (data[i].media_type === 'image'){
        list.push({
          icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5 15.4788C5.10725 16.5691 5.34963 17.303 5.89742 17.8507C6.87997 18.8333 8.46133 18.8333 11.6241 18.8333C14.7868 18.8333 16.3682 18.8333 17.3507 17.8507C18.3333 16.8682 18.3333 15.2868 18.3333 12.1241C18.3333 8.96133 18.3333 7.37997 17.3507 6.39742C16.803 5.84963 16.0691 5.60725 14.9788 5.5" stroke="#6B7280" stroke-width="1.5"/>
<path d="M1.66663 8.83335C1.66663 5.69065 1.66663 4.11931 2.64293 3.143C3.61925 2.16669 5.19059 2.16669 8.33329 2.16669C11.476 2.16669 13.0474 2.16669 14.0236 3.143C15 4.11931 15 5.69065 15 8.83335C15 11.976 15 13.5474 14.0236 14.5237C13.0474 15.5 11.476 15.5 8.33329 15.5C5.19059 15.5 3.61925 15.5 2.64293 14.5237C1.66663 13.5474 1.66663 11.976 1.66663 8.83335Z" stroke="#6B7280" stroke-width="1.5"/>
<path d="M4.16663 15.5C7.01713 11.5406 10.2205 6.28959 15 10.2278" stroke="#6B7280" stroke-width="1.5"/>
</svg>`,
          value: data[i].name,
          id: data[i]._id,
        });
      } else if (data[i].media_type === 'video'){
        list.push({
          icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.7421 11.205C15.4475 12.3242 14.0555 13.115 11.2714 14.6968C8.57996 16.2258 7.23429 16.9903 6.14982 16.683C5.70146 16.5559 5.29295 16.3147 4.96349 15.9822C4.16663 15.1782 4.16663 13.6188 4.16663 10.5C4.16663 7.38117 4.16663 5.82175 4.96349 5.01777C5.29295 4.68538 5.70146 4.44407 6.14982 4.31702C7.23429 4.00971 8.57996 4.77423 11.2714 6.30328C14.0555 7.88498 15.4475 8.67583 15.7421 9.795C15.8637 10.257 15.8637 10.743 15.7421 11.205Z" stroke="#6B7280" stroke-width="1.5" stroke-linejoin="round"/>
</svg>
`,
          value: data[i].name,
          id: data[i]._id,
        });
      } else if (data[i].media_type === '360_video'){
        list.push({
          icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.0309 10.9218C19.5182 6.33548 18.3845 3.22238 14.129 3C11.036 3.05905 7.84555 4.72552 5.29889 7.30361C3.16169 9.46717 1.05739 12.5658 1.82988 15.515C2.00045 16.1662 2.33304 16.6098 2.85842 17.0273C4.2703 18.1493 5.65613 18.248 8.32522 17.5927C11.0282 16.7714 12.704 15.534 14.117 14.2681M14.117 14.2681C14.1184 14.2668 14.1199 14.2654 14.1214 14.2641C14.124 14.2617 14.1223 14.2573 14.1187 14.2573C14.116 14.2573 14.1141 14.26 14.115 14.2625C14.1157 14.2644 14.1163 14.2663 14.117 14.2681ZM14.117 14.2681C14.4256 15.1808 14.2301 15.9953 13.6987 17.5927" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
          value: data[i].name,
          id: data[i]._id,
        });
      } else if (data[i].media_type === '360_image'){
        list.push({
          icon: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
  <path d="M17.0309 10.9218C19.5182 6.33548 18.3845 3.22238 14.129 3C11.036 3.05905 7.84555 4.72552 5.29889 7.30361C3.16169 9.46717 1.05739 12.5658 1.82988 15.515C2.00045 16.1662 2.33304 16.6098 2.85842 17.0273C4.2703 18.1493 5.65613 18.248 8.32522 17.5927C11.0282 16.7714 12.704 15.534 14.117 14.2681M14.117 14.2681C14.1184 14.2668 14.1199 14.2654 14.1214 14.2641C14.124 14.2617 14.1223 14.2573 14.1187 14.2573C14.116 14.2573 14.1141 14.26 14.115 14.2625C14.1157 14.2644 14.1163 14.2663 14.117 14.2681ZM14.117 14.2681C14.4256 15.1808 14.2301 15.9953 13.6987 17.5927" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
          value: data[i].name,
          id: data[i]._id,
        });
      } else {
        list.push({
          icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5 15.4788C5.10725 16.5691 5.34963 17.303 5.89742 17.8507C6.87997 18.8333 8.46133 18.8333 11.6241 18.8333C14.7868 18.8333 16.3682 18.8333 17.3507 17.8507C18.3333 16.8682 18.3333 15.2868 18.3333 12.1241C18.3333 8.96133 18.3333 7.37997 17.3507 6.39742C16.803 5.84963 16.0691 5.60725 14.9788 5.5" stroke="#6B7280" stroke-width="1.5"/>
<path d="M1.66663 8.83335C1.66663 5.69065 1.66663 4.11931 2.64293 3.143C3.61925 2.16669 5.19059 2.16669 8.33329 2.16669C11.476 2.16669 13.0474 2.16669 14.0236 3.143C15 4.11931 15 5.69065 15 8.83335C15 11.976 15 13.5474 14.0236 14.5237C13.0474 15.5 11.476 15.5 8.33329 15.5C5.19059 15.5 3.61925 15.5 2.64293 14.5237C1.66663 13.5474 1.66663 11.976 1.66663 8.83335Z" stroke="#6B7280" stroke-width="1.5"/>
<path d="M4.16663 15.5C7.01713 11.5406 10.2205 6.28959 15 10.2278" stroke="#6B7280" stroke-width="1.5"/>
</svg>`,
          value: data[i].name,
          id: data[i]._id,
        });
      }
    } else {
      list.push({
        icon: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5 15.4788C5.10725 16.5691 5.34963 17.303 5.89742 17.8507C6.87997 18.8333 8.46133 18.8333 11.6241 18.8333C14.7868 18.8333 16.3682 18.8333 17.3507 17.8507C18.3333 16.8682 18.3333 15.2868 18.3333 12.1241C18.3333 8.96133 18.3333 7.37997 17.3507 6.39742C16.803 5.84963 16.0691 5.60725 14.9788 5.5" stroke="#6B7280" stroke-width="1.5"/>
<path d="M1.66663 8.83335C1.66663 5.69065 1.66663 4.11931 2.64293 3.143C3.61925 2.16669 5.19059 2.16669 8.33329 2.16669C11.476 2.16669 13.0474 2.16669 14.0236 3.143C15 4.11931 15 5.69065 15 8.83335C15 11.976 15 13.5474 14.0236 14.5237C13.0474 15.5 11.476 15.5 8.33329 15.5C5.19059 15.5 3.61925 15.5 2.64293 14.5237C1.66663 13.5474 1.66663 11.976 1.66663 8.83335Z" stroke="#6B7280" stroke-width="1.5"/>
<path d="M4.16663 15.5C7.01713 11.5406 10.2205 6.28959 15 10.2278" stroke="#6B7280" stroke-width="1.5"/>
</svg>`,
        value: data[i].name,
        id: data[i]._id,
      });
    }
  }
  return list;
}

// Const handlePrev = () => {
//   Document.getElementById('amenityItemsSlider').swiper.slidePrev(); // Previous
// };

// Const handleNext = () => {
//   Document.getElementById('amenityItemsSlider').swiper.slideNext(); // Next
// };

const callGoogleanalytics = () => {
  Googleanalytics("experience_start", {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  Store.isExperienceStartEventSent = true;
};

onMounted(() => {
  listenPost();
  // Setup event listener for fullscreen change
  document.addEventListener('fullscreenchange', fullScreenChangeHandler);
  document.addEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('MSFullscreenChange', fullScreenChangeHandler);

  watch(
    () => [Store.organizationDetails, Store.projectCardData],
    ([newOrgDetails, newProjectCardData]) => {
      if (
        newOrgDetails && Object.keys(newOrgDetails).length > 0 &&
        newProjectCardData && Object.keys(newProjectCardData).length > 0
      ) {
        if (!Store.isExperienceStartEventSent && !Store.isNavigatedFromMasterScene) {
          callGoogleanalytics();
        }
      }
    },
    { immediate: false, deep: true },
  );

});
onUnmounted(() => {
  // Cleanup event listener when component is destroyed
  document.removeEventListener('fullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('MSFullscreenChange', fullScreenChangeHandler);
  Store.hideLogo=false;
});

/* Previous Scene Navigation */
const handleBackNavigation = () => {
  router.push({ name: 'projectScene', query: {...route.query} });
};

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();

</script>

<template>
  <div :style="{ fontFamily: Store.loadedFont }">
    <NavBar
      :is-root="route.name === 'amenity'"
      @handle-navigation="handleBackNavigation"
    />

    <ProgressBar
      v-if="loaderProgress!==false && loaderProgress!==null"
      class="z-30"
      :progress="loaderProgress"
    />

    <div class="absolute top-[50%] z-[4]">
      <NewAleSideBar
        v-if="!showLoader && (Store.isMobile? categoriesFilteredList[selectedCategory].filter((item) => item._id === selectionId)[0]?.media_type == 'pdf'?false:true:true)"
        :sidebarList="Store.sidebarOptions[route.params.projectId]"
        @select-option="moveToLocation"
      />
    </div>

    <!-- <div class="absolute top-0 z-[4] sm:hidden">
      <Backbar
        :is-root="Store.SceneData[route.params.sceneId]?.sceneData.root"
        :scene-name="Store.SceneData[route.params.sceneId]?.sceneData.name"
        :thumbnail="Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo"
        @go-back="goToPreviousScene"
      />
    </div> -->

    <!-- <div
      v-if="Store.amenityCategories"
      :class="['block w-full py-3 sm:py-0 sm:w-[60%] h-[61px] transform-none sm:-translate-x-[-33%] top-12 sm:top-auto sm:bottom-4 left-0 z-[3] transition-all transition-timing-function:cubic-bezier(0.45, 0.05, 0.55, 0.95); duration-200 fixed']"
    >
      <div
        v-if="Object.keys(Store.amenityCategories).length > 0"
        class=" font-medium w-full flex justify-start items-start sm:justify-center sm:items-center pl-3 sm:pl-0"
      >
        <OverflowSlider
          slides-per-view="auto"
          direction="horizontal"
          :mousewheel="true"
          space-between="10"
          class="sm:w-auto w-full"
          :initial-slide="selectedCategory ? Store.amenityCategories.findIndex((e) => e.Category == selectedCategory) : 0"
        >
          <template #options>
            <swiper-slide
              v-for="item, index in Store.amenityCategories"
              :key="index"
              :class="['rounded-[50px] h-11 px-4 text-[14px] w-fit flex items-center justify-center capitalize hover:cursor-pointer border', (selectedCategory == item.category ? ' border-white bg-white text-black bg-primary ' : 'border-transparent text-white bg-black bg-opacity-40 backdrop-blur-[20px]')]"
              @click="handleAmenityCategoriesSelection(item)"
            >
              {{ item.category ? `${item.category.replace('_', ' ')}` : '' }}
            </swiper-slide>
          </template>
        </OverflowSlider>
      </div>
    </div> -->

    <!-- Amenities Sider -->
    <div
      v-if="categoriesFilteredList && selectedCategory"
      id="maincompview"
    >
      <OverflowSlider
        v-if="selectedCategory && !showLoader"
        id="amenityItemsSlider"
        slidesPerView="1"
        direction="horizontal"
        space-between="3"
        class=" absolute top-0 left-0 w-full h-full"
        :initial-slide="selectionId ? categoriesFilteredList[selectedCategory].findIndex((item) => item._id === selectionId) : 0"
        Keyboard="true"
        allow-touch-move="false"
        @swiper-slide-change="(val) => handleSwiperSlideChange(categoriesFilteredList[selectedCategory][val.detail[0].realIndex], val.detail[0].realIndex)"
      >
        <template #options>
          <swiper-slide
            v-for="item, index in categoriesFilteredList[selectedCategory]"
            :key="index"
            class=" h-full w-screen"
          >
            <!-- Image -->
            <div
              v-if="item?.media_type === 'image'"
              class="relative overflow-hidden bg-no-repeat text-center h-full w-full bg-cover"
              :style="{ backgroundImage: `url('${cdn(item.thumbnail)}')` }"
            >
              <div class="h-full w-full bg-black bg-opacity-40 backdrop-blur-[20px]">
                <!--  v-if="item._id === selectionId" -->
                <div
                  v-if="index === (currentSliderIndex - 1)"
                  class="flex justify-center items-center h-full w-full relative"
                >
                  <BlobImageLoader
                    :name="item.name"
                    :thumbnail="cdn(item.thumbnail)"
                    :url="cdn(item.file)"
                    @progress-data="(progress)=>{loaderProgress=progress;}"
                  />
                </div>
              </div>
            </div>

            <!-- video -->

            <div
              v-else-if="item?.media_type === 'video'"
              class="h-full w-full flex items-center justify-center"
            >
              <div
                v-if="index === (currentSliderIndex - 1)"
                class="h-full w-full flex items-center justify-center relative"
              >
                <div
                  v-if="circularloader"
                  class="z-10 absolute top-0 left-0 h-full w-full bg-black bg-opacity-40 backdrop-blur-[20px] flex"
                >
                  <div
                    class="m-auto loade
                  r border-4 border-gray-200 border-t-4 border-t-black rounded-full w-12 h-12 animate-spin"
                  />
                </div>

                <div class="h-full w-full flex items-center justify-center">
                  <GalleryVideoPlayer
                    :url="cdn(item.file)"
                    @handle-loading-complete="handleVideoLoader"
                  />
                </div>
              </div>
            </div>

            <!-- 360-video -->
            <div
              v-if="item?.media_type === '360_video'"
              class="h-full w-full flex items-center justify-center"
            >
              <div
                v-if="(currentSliderIndex - 1) === index"
                class="h-full w-full flex items-center justify-center relative"
              >
                <AframeVideo360
                  :videoSrc="cdn(item.file)"
                  :thumbnailSrc="cdn(item.thumbnail)"
                />
              </div>
            </div>

            <!-- 360 image -->
            <div
              v-if="item?.media_type === '360_image'"
              class="relative overflow-hidden bg-cover bg-no-repeat text-center h-full sm:mt-0"
            >
              <div
                v-if="index === (currentSliderIndex - 1)"
                class="flex justify-center items-center h-full w-full relative"
              >
                <AFrameComp
                  :sceneData="item"
                  @progress-data="(progress)=>{loaderProgress=progress;}"
                  @hotspot-click="handleHotspotClick"
                />
              </div>
            </div>

            <!-- multires -->
            <div
              v-if="item?.media_type === 'multires'"
              class="h-full w-full"
            >
              <div
                v-if="(currentSliderIndex - 1) === index"
                class="h-full w-full"
              >
                <PannellumMultiRes
                  :type="item.media_type"
                  :basepath="item.media[0].multi_res.basepath"
                  :path="item.media[0].multi_res.path"
                  :fallbackPath="item.media[0].multi_res.fallbackPath"
                  :extension="item.media[0].multi_res.extension"
                  :tileResolution="item.media[0].multi_res.tileResolution"
                  :maxLevel="item.media[0].multi_res.maxLevel"
                  :cubeResolution="item.media[0].multi_res.cubeResolution"
                />
              </div>
            </div>

            <!-- EMbed Link  -->

            <div
              v-if="item?.media_type === 'iframe' || item?.media_type === 'embed_link'"
            >
              <div v-if="index === (currentSliderIndex - 1)">
                <iframe
                  id="showcase_frame"
                  class="w-full h-screen"
                  :src="item.embed_link"
                  frameborder="0"
                  allowfullscreen
                  allow="xr-spatial-tracking"
                />
              </div>
            </div>

            <!-- Virtual Tours -->
            <div
              v-if="item?.media_type === 'virtual_tour'"
            >
              <div :class="[Store.isMobile ? 'flex-grow w-full' : 'h-full']">
                <TourView
                  :showInterior="item.tour_id"
                  :listOfVrTourData="Store.listOfVrTourData[item.tour_id]"
                  :currentImageId="currentImageId"
                  @updated-image-id="updatedImageId"
                  @current-image-id-emit="updatedImageId"
                />
              </div>
            </div>

            <!--PDF-->
            <div
              v-if="item?.media_type === 'pdf'"
              class="h-full w-full flex"
            >
              <PDFViewer :url="item.file" />
            </div>

            <!--no media_type -->

            <div
              v-if="!item.media_type"
              class="relative overflow-hidden bg-no-repeat text-center h-full w-full bg-cover"
              :style="{ backgroundImage: `url('${cdn(item.thumbnail)}')` }"
            >
              {{ console.log(item.name) }}
              <div class="h-full w-full bg-black bg-opacity-40 backdrop-blur-[20px]">
                <div
                  v-if="index === (currentSliderIndex - 1)"
                  class="flex justify-center items-center h-full w-full relative"
                >
                  <BlobImageLoader
                    :name="item.name"
                    :thumbnail="cdn(item.thumbnail)"
                    :url="cdn(item.file)"
                    @progress-data="(progress)=>{loaderProgress=progress;}"
                  />
                </div>
              </div>
            </div>
          </swiper-slide>
        </template>
      </OverflowSlider>

      <!-- <div
        class="flex flex-row fixed right-[50%] bottom-[10%] translate-x-[50%] -translate-y-[50%] sm:transform-none sm:right-5 sm:bottom-8 items-center gap-1 justify-start z-[3]"
      >
        <button
          :disabled="(currentSliderIndex - 1) === 0 ? true : false"
          class="h-11 w-11 border-transparent text-white bg-black bg-opacity-40 backdrop-blur-[20px] flex justify-center items-center rounded-[50%] cursor-pointer  disabled:opacity-15 disabled:cursor-default"
          @click="handlePrev"
        >
          <svg
            class="w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21 12L3 12"
              stroke="white"
              stroke-width="1.4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8 17L3 12L8 7"
              stroke="white"
              stroke-width="1.4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>

        <div
          v-if="selectedCategory"
          class="h-11 w-11 bg-black flex justify-center items-center rounded-[50%] cursor-pointer text-white text-sm"
        >
          {{ currentSliderIndex }}/{{ categoriesFilteredList[selectedCategory].length }}
        </div>

        <button
          :disabled="currentSliderIndex === categoriesFilteredList[selectedCategory].length ? true : false"
          class="h-11 w-11  flex justify-center items-center rounded-[50%] cursor-pointer border-transparent text-white bg-black bg-opacity-40 backdrop-blur-[20px] disabled:opacity-50 disabled:cursor-default"
          @click="handleNext"
        >
          <svg
            class="w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3 12L21 12"
              stroke="white"
              stroke-width="1.4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M16 7L21 12L16 17"
              stroke="white"
              stroke-width="1.4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div> -->

      <div
        v-if="!showLoader && categoryList && categoryList.length > 1"
        :class="[
          'fixed',
          Store.isMobile
            ? 'right-3 z-[6]'
            : Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
              ? 'bottom-14 !right-24 z-[3]'
              : Store.isLandscape
                ? 'bottom-14 right-0 z-[6]'
                : 'left-9 z-[6]',
          Store.isMobile
            ? (categoriesFilteredList[selectedCategory].filter((item) => item._id === selectionId)[0]?.media_type === 'pdf'
              ? 'bottom-24'
              : 'bottom-[9rem]'
            )
            : 'bottom-10'
        ]"
      >
        <DropDown
          :align-to-end="Store.isMobile || Store.isLandscape ? true : false"
          placement="top"
          :list="categoryList"
          :defaultValue="categoryList.filter((e) => e.category === selectedCategory)[0]"
          :objectKey="`category`"
          type="Object"
          @select-option="handleAmenityCategoriesSelection"
        />
      </div>
      <div
        v-if="!showLoader && selectedCategory"
        class="fixed w-full flex justify-center z-[3]"
        :class="Store.isLandscape
          ? 'bottom-2'
          : Store.isMobile
            ? selectionId
              ? categoriesFilteredList[selectedCategory].filter((item) => item._id === selectionId)[0]?.media_type !== 'pdf'
                ? 'bottom-24'
                : 'bottom-10'
              : categoriesFilteredList[selectedCategory][0]?.media_type !== 'pdf'
                ? 'bottom-24'
                : 'bottom-10'
            : 'bottom-10'"
      >
        <NearByFloatingButton
          v-if="NearByFloatingButtonFilterData(categoriesFilteredList[selectedCategory])?.length > 1"
          class="flex"
          :class="Store.isMobile || Store.isLandscape ? 'w-full' : 'sm:w-[28%] md:w-[33%] lg:w-[52%] xl:[70%]'"
          :itemsList="NearByFloatingButtonFilterData(categoriesFilteredList[selectedCategory])"
          :sliderButton="true"
          :active="selectionId ? categoriesFilteredList[selectedCategory].findIndex((item) => item._id === selectionId) : 0"
          :leftButton="leftButton"
          :rightButton="rightButton"
          :objectIconKey="`icon`"
          :objectNameKey="`value`"
          @button-clicked="slideTo"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>

.trans {
  transition: right 0.8s ease;
  /* Adjust the duration and easing function as needed */
}
</style>
