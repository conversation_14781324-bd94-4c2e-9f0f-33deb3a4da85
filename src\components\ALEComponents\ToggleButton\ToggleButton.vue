<script setup>
import { ref, watch } from 'vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const props = defineProps({
  label: {type: String, default: ''},
  id: {type: String, default: ""},
  active: Boolean,
});
const emit = defineEmits(['onToggle']);

const toggle = ref(props.active);
watch(() => toggle.value, () => {
  emit('onToggle', props.id);
});
</script>

<template>
  <div class="px-5 py-2 rounded-md shadow justify-center items-center gap-2 inline-flex bg-secondary">
    <label class="inline-flex items-center cursor-pointer">
      <input
        v-model="toggle"
        type="checkbox"
        value=""
        class="sr-only peer"
      >
      <div class="relative w-11 h-6 bg-secondaryText peer-focus:outline-none  rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-tertiary50opacity" />
      <span class="ms-3 text-sm font-medium text-secondaryText dark:text-secondaryText">
        <TranslationComp
          :text="label"
        />
      </span>
    </label>
  </div>
</template>

<style lang="scss">

</style>
