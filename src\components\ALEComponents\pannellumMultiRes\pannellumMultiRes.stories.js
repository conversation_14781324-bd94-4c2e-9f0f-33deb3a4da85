import PannellumMultiRes from './PannellumMultiRes.vue';

export default {
  title: 'Design System/ALE/PannellumMultiRes',
  component: PannellumMultiRes,
  tags: ['autodocs'],
};

export const Primary = {
  args: {
    type: "multires",
    basepath: "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F9jV7y3%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Famenities%2F6686aeeecfa4b4e164d88bd9/",
    path: "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F9jV7y3%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Famenities%2F6686aeeecfa4b4e164d88bd9%2Foutput%2F%l%2F%s%y_%x",
    fallbackPath: "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2F9jV7y3%2Fprojects%2F65b8ae6d0fd62ab0b1bc2ca8%2Famenities%2F6686aeeecfa4b4e164d88bd9/fallback/%s",
    extension: "jpg?alt=media",
    tileResolution: 512,
    maxLevel: 3,
    cubeResolution: 1272,
  },
};
