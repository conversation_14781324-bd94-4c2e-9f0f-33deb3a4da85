import FloorPlanCard from './FloorPlanCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories

export default {
  title: 'Design System/ALE/FloorPlanCard',
  component: FloorPlanCard,
  tags: ['autodocs'],
};

const Template = (args) => ({
  components: { FloorPlanCard },
  setup () {
    return { args };
  },
  template: `
    <FloorPlanCard :addFavorites=args.addFavorites :title=args.title :price=args.price :currencyTitle=args.currencyTitle :measurement=args.measurement  :measurementType=args.measurementType :bedrooms=args.bedrooms :buttonView=args.buttonView :isShowButtons=args.isShowButtons :status=args.status>
        <template v-slot:currencySymbol>
            
             <svg class='w-6 h-6'   viewBox="0 0 28 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 9.49791C21 9.7732 20.8919 10.0372 20.6995 10.2319C20.5072 10.4265 20.2462 10.5359 19.9741 10.5359H17.5805C17.5789 12.0036 17.002 13.4107 15.9763 14.4486C14.9506 15.4864 13.5599 16.0702 12.1093 16.0718H11.6819L17.2385 21.1855C17.3401 21.2767 17.4228 21.3873 17.4817 21.5111C17.5407 21.6348 17.5748 21.7691 17.5821 21.9063C17.5894 22.0434 17.5697 22.1807 17.5242 22.3101C17.4787 22.4395 17.4082 22.5585 17.3169 22.6601C17.2257 22.7618 17.1153 22.8441 16.9924 22.9023C16.8694 22.9604 16.7362 22.9934 16.6006 22.9991C16.465 23.0048 16.3295 22.9833 16.2022 22.9357C16.0749 22.8881 15.9581 22.8153 15.8588 22.7218L8.33588 15.8019C8.18227 15.6606 8.07463 15.4756 8.02718 15.2711C7.97972 15.0666 7.99467 14.8524 8.07006 14.6568C8.14546 14.4611 8.27774 14.2932 8.44944 14.1751C8.62114 14.0571 8.82418 13.9946 9.03174 13.9958H12.1093C13.0162 13.9958 13.886 13.6313 14.5272 12.9824C15.1685 12.3336 15.5288 11.4535 15.5288 10.5359H9.03174C8.75967 10.5359 8.49874 10.4265 8.30636 10.2319C8.11397 10.0372 8.00589 9.7732 8.00589 9.49791C8.00589 9.22262 8.11397 8.9586 8.30636 8.76395C8.49874 8.56929 8.75967 8.45993 9.03174 8.45993H14.8449C14.5264 8.03022 14.1134 7.68145 13.6385 7.44123C13.1637 7.20102 12.6402 7.07596 12.1093 7.07596H9.03174C8.75967 7.07596 8.49874 6.9666 8.30636 6.77194C8.11397 6.57728 8.00589 6.31327 8.00589 6.03798C8.00589 5.76269 8.11397 5.49868 8.30636 5.30402C8.49874 5.10936 8.75967 5 9.03174 5H19.9741C20.2462 5 20.5072 5.10936 20.6995 5.30402C20.8919 5.49868 21 5.76269 21 6.03798C21 6.31327 20.8919 6.57728 20.6995 6.77194C20.5072 6.9666 20.2462 7.07596 19.9741 7.07596H16.376C16.7089 7.49533 16.9796 7.96155 17.1796 8.45993H19.9741C20.2462 8.45993 20.5072 8.56929 20.6995 8.76395C20.8919 8.9586 21 9.22262 21 9.49791Z" fill="white"/>
              </svg>

        </template>
    </FloorPlanCard>
  `,
});

export const Primary = Template.bind({});

Primary.args = {
  title: 'Unit -456',
  price: 5000000,
  currencyTitle: 'INR',
  measurement: 2500,
  measurementType: 'Sqft',
  bedrooms: 2,
  buttonView: 'floorplanview',
  isShowButtons: true,
  status: 'available',
  addFavorites: false,
  isMobile: false,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1024-3262&mode=dev",
    },
  },
}; // Props
