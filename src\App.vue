<script setup>
import { onMounted, ref, watch } from 'vue';
import { creationToolStore } from './store/index';
import { useRoute } from 'vue-router';
import { hexToRgb, calcBrightness, loadFont } from './helpers/helper';
import { ancesterDomainToRestrict, standaloneDomainToRestrict } from './config/masterdata';
const route = useRoute();
const Store = creationToolStore();
const vh = window.innerHeight * 0.01;
document.documentElement.style.setProperty('--vh', `${vh}px`);
const primaryColor = ref(), secondaryColor = ref(), colormix = ref(), tertiary50opacity = ref(), primaryTextColor = ref(), secondaryTextColor = ref();
const measurementId = ref(null);

// Default theme settings
const defaultTheme = {
  light: {
    primaryColor: '#000000',
    primaryTextColor: '#ffffff',
    secondaryColor: '#ffffff',
    secondaryTextColor: '#000000',
  },
  dark: {
    primaryColor: '#ffffff',
    primaryTextColor: '#000000',
    secondaryColor: '#000000',
    secondaryTextColor: '#ffffff',
  },
};
// Default Organization settings
const defaultOrgTheme = {
  light: {
    primaryColor: '#ffffff',
    primaryTextColor: '#000000',
    secondaryColor: '#1f2a37',
    secondaryTextColor: '#ffffff',
  },
  dark: {
    primaryColor: '#ffffff',
    primaryTextColor: '#000000',
    secondaryColor: '#1f2a37',
    secondaryTextColor: '#ffffff',
  },
};

// Function to get theme settings
const getThemeSettings = () => {

  const projectData = Store.projectCardData[route.params.projectId];
  const organizationData = Store.organizationDetails;
  // No project data found
  if (!projectData && !organizationData) {
    return null;
  }

  const themeConfig = projectData?.projectSettings?.theme;

  if (!themeConfig) {
    if (Object.keys(Store.organizationDetails).length > 0) {
      const orgTheme = Store.organizationDetails.theme;
      if (['light', 'dark', 'custom'].includes(orgTheme)) {
        return orgTheme === 'custom'
          ? {
            primaryColor: Store.organizationDetails.primary || primaryColor.value,
            primaryTextColor: Store.organizationDetails.primary_text || primaryTextColor.value,
            secondaryColor: Store.organizationDetails.secondary || secondaryColor.value,
            secondaryTextColor: Store.organizationDetails.secondary_text || secondaryTextColor.value,
          }
          : defaultOrgTheme[orgTheme];
      }
    }
    // Fallback to default dark theme if no org theme
    return defaultOrgTheme.dark;
  }

  // Project theme handling
  if (['light', 'dark'].includes(themeConfig.theme)) {
    return defaultTheme[themeConfig.theme];
  }

  // Project Custom theme
  if (themeConfig.theme === 'custom') {
    return {
      primaryColor: themeConfig.primary || primaryColor.value,
      primaryTextColor: themeConfig.primary_text || primaryTextColor.value,
      secondaryColor: themeConfig.secondary || secondaryColor.value,
      secondaryTextColor: themeConfig.secondary_text || secondaryTextColor.value,
    };
  }
  // Final fallback to default dark theme
  return defaultTheme.dark;
};

watch(() => [Store.organizationDetails, Store.projectCardData], () => {
  // if (route.fullPath.includes('masterscene')) {
  //   return;
  // }
  // Get theme settings
  const themeSettings = getThemeSettings();

  if (themeSettings) {
    // Set color values
    primaryColor.value = themeSettings.primaryColor;
    primaryTextColor.value = themeSettings.primaryTextColor;
    secondaryColor.value = themeSettings.secondaryColor;
    secondaryTextColor.value = themeSettings.secondaryTextColor;

    // Process colors
    const hextorgb = hexToRgb(secondaryTextColor.value);
    colormix.value = hextorgb.mixStyle;
    tertiary50opacity.value = hextorgb.tertiary50opacity;
    Store.logo = calcBrightness(secondaryColor.value);

    // Update CSS variables
    const cssVariables = {
      '--primary': primaryColor.value,
      '--primaryText': primaryTextColor.value,
      '--secondary': secondaryColor.value,
      '--secondaryText': secondaryTextColor.value,
      '--colormix': colormix.value,
      '--tertiary50opacity': tertiary50opacity.value,
    };

    Object.entries(cssVariables).forEach(([key, value]) => {
      document.documentElement.style.setProperty(key, value);
    });

    const fontUrl = Store.projectCardData[route.params.projectId]?.projectSettings?.theme?.font_url
      ?? Store.organizationDetails?.font_url;
    const fontType = Store.projectCardData[route.params.projectId]?.projectSettings?.theme?.font_type
      ?? Store.organizationDetails?.font_type
      ?? 'Roboto'; // Default to Roboto if nothing is set

    if (fontUrl) {
      loadFont(fontUrl).then(() => {
        Store.loadedFont = fontType;
        document.body.style.fontFamily = fontType;
      });
    } else {
      // No custom font, set default
      Store.loadedFont = 'Roboto';
      document.body.style.fontFamily = 'Roboto, Arial, sans-serif';
      // Optionally, load Roboto from Google Fonts if not already present
      loadFont('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
    }

  }
}, { deep: true });

// Function to initialize analytics
const initializeAnalytics = () => {
  if (!measurementId.value) {
    return;
  }
  // Dynamically load the GA script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId.value}`;
  document.head.appendChild(script);

  window.dataLayer = window.dataLayer || [];
  window.gtag = function () {
    window.dataLayer.push(arguments);
  };
  window.gtag('js', new Date());
  window.gtag('config', measurementId.value);
};

// Block GA for specific domains
const isBlockedByAncestor = () => {
  // 1.iframe embedding, Chromium browsers(chrome, Edge)
  if (window.location.ancestorOrigins && window.location.ancestorOrigins.length > 0) {
    for (let i = 0; i < window.location.ancestorOrigins.length; i++) {
      const ancestor = window.location.ancestorOrigins[i];
      if (ancesterDomainToRestrict.some((domain) => ancestor.includes(domain))) {
        return true;
      }
    }
  }

  // 2. iframe embedding, non-Chromium browsers(safari, Firefox)
  if (document.referrer) {
    try {
      const referrerOrigin = new URL(document.referrer).origin;
      if (ancesterDomainToRestrict.some((domain) => referrerOrigin.includes(domain))) {
        return true;
      }
    } catch (e) {
      // Ignore invalid referrer URLs
    }
  }

  // 3. Standalone: block for specific domains
  if (standaloneDomainToRestrict && standaloneDomainToRestrict.some((domain) => window.location.origin.includes(domain))) {
    return true;
  }

  // 4. check ancestorDomainToRestrict for standalone (legacy)
  if (ancesterDomainToRestrict.some((domain) => window.location.origin.includes(domain))) {
    return true;
  }

  return false;
};

// Fetch measurement_id
onMounted(() => {
  watch([() => route.params.organizationId, () => Store.organizationDetails],
    () => {
      if (Object.keys(Store.organizationDetails).length !== 0){
        measurementId.value=Store.organizationDetails?.measurement_id ? Store.organizationDetails?.measurement_id : import.meta.env.VITE_APP_GA ;
        if (!isBlockedByAncestor()) {
          initializeAnalytics();
        }
      }
    }, { immediate: true },
  );
});

</script>

<template>
  <router-view />
</template>

<style>
svg {
  height: 100%;
  width: 100%;
}

.svgLayer {
  /* width: auto; */
  height: 100%;
  display: block;
  overflow: auto;
}

@media only screen and (min-width:991px) {
  .svgLayer {
    width: 100%;
  }
}

@media only screen and (min-height:700px) {
  .svgLayer {
    height: 100%;
  }
}

.areaSVGCls,
.mediaSvgCls,
.landmarkSvgCls,
.locationSvgCls {
  cursor: pointer;
}

.landmarkSvgCls:hover>path {
  fill: blue;
}

.mediaSvgCls:hover>path {
  fill: green;
}

.areaSVGCls:hover>path {
  fill: red;
}

.routeSvgCls>polyline,
.routeSvgCls>path {
  stroke-dasharray: 10000;
  stroke-dashoffset: 10000;
}

.routeSvgCls.active>polyline,
.routeSvgCls.active>path {
  animation: dash 5s linear forwards;
}

@keyframes dash {
  to {
    stroke-dashoffset: 0;
  }
}
</style>
